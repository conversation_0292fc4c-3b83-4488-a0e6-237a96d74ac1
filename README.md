# SwiftTrack - SWIFT报文处理系统

## 项目概述

SwiftTrack是一个专业的SWIFT金融报文处理系统，用于处理、管理和转换SWIFT消息。系统采用领域驱动设计(DDD)架构风格开发，为金融机构和企业提供完整的国际支付消息处理解决方案。

## 系统架构

该系统遵循经典的四层DDD架构：

### 1. 接口层 (swifttrack-interface)
- 处理HTTP请求/响应
- 提供RESTful API接口和Swagger文档
- 包含控制器、过滤器和请求验证

### 2. 应用层 (swifttrack-application)
- 协调领域对象和基础设施
- 实现业务流程和服务
- 处理参数转换和结果封装

### 3. 领域层 (swifttrack-domain)
- 包含核心业务逻辑和规则
- 定义领域对象、枚举和接口抽象
- 设计与SWIFT报文相关的业务概念

### 4. 基础设施层 (swifttrack-infrastructure)
- 提供技术支持与实现
- 包含数据持久化、缓存、消息处理等
- 集成外部系统和服务

## 关键业务流程

### SWIFT报文处理流程
- **报文创建**：银行或企业用户创建SWIFT报文（主要支持pacs.008和pacs.009）
- **草稿保存**：用户可保存报文草稿供后续编辑
- **报文预览**：生成不同格式（XML、JSON等）的报文预览
- **提交处理**：报文提交进入工作流
- **签收确认**：报文接收方进行签收操作
- **状态追踪**：全流程状态跟踪和监控

### 审批与授权流程
- **银行端报文审核**：银行制单员创建报文后提交审核
- **企业端报文授权**：企业制单员创建报文后提交审核
- **驳回处理**：被驳回报文可修改后重新提交
- **多级审批**：根据报文类型和金额设置不同级别审批

### 身份验证与安全
- **用户登录认证**：基于JWT的用户认证
- **TOTP验证**：支持双因素认证
- **权限控制**：基于角色的访问控制
- **数据加密**：敏感信息加密存储

### 报文转换与格式化
- **格式转换**：ISO20022 XML与SWIFT MT格式互转
- **报文验证**：BIC编码和字段格式验证
- **标准符合性检查**：确保报文符合SWIFT标准规范

### 监控与统计
- **报文处理状态统计**：按类型、状态统计报文处理情况
- **系统健康监控**：提供系统运行状态监控
- **性能指标追踪**：追踪关键业务流程的执行效率

## 技术栈

- **框架**：Spring Boot 2.7.4、Spring Cloud
- **数据库访问**：MyBatis Plus 3.5.7
- **缓存**：Redis
- **API文档**：Swagger/OpenAPI 3
- **SWIFT消息处理**：使用ProwideSoftware的SWIFT和ISO20022库
- **构建工具**：Maven
- **其他**：JWT (认证)、Alibaba Cloud组件

## 项目启动

### 环境要求
- JDK 17
- Maven 3.6+
- Redis
- MySQL 5.7+

### 构建与运行
```bash
# 构建项目
mvn clean package

# 运行应用
java -jar swifttrack-interface/target/swifttrack-interface-1.0.0-SNAPSHOT.jar
```

### 配置修改
主要配置文件位于`swifttrack-interface/src/main/resources/application.yml`，根据环境需求修改数据库连接和Redis配置。

## 开发指南

### 模块依赖关系
```
swifttrack-interface
    ├── swifttrack-application
    │      ├── swifttrack-domain
    │      └── swifttrack-infrastructure
    ├── swifttrack-infrastructure
    │      └── swifttrack-domain
    └── swifttrack-domain
```

### 开发规范
- 遵循DDD设计思想，保持层次职责清晰
- 控制器只负责请求处理，业务逻辑在应用层和领域层实现
- 使用统一的异常处理和响应格式
- 重要的业务逻辑添加单元测试

## 联系与支持

有关该项目的问题，请联系SwiftTrack团队：<EMAIL>
