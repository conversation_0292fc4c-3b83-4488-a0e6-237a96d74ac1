#!/bin/bash

# SwiftTrack 测试运行脚本
# 用于运行 SendMessageService 的完整测试套件

echo "=========================================="
echo "SwiftTrack SendMessageService 测试套件"
echo "=========================================="

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查Maven是否安装
if ! command -v mvn &> /dev/null; then
    echo -e "${RED}错误: Maven 未安装或不在PATH中${NC}"
    exit 1
fi

# 检查Java版本
JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
if [ "$JAVA_VERSION" -lt 17 ]; then
    echo -e "${RED}错误: 需要Java 17或更高版本，当前版本: $JAVA_VERSION${NC}"
    exit 1
fi

echo -e "${GREEN}✓ 环境检查通过${NC}"
echo ""

# 清理并编译项目
echo -e "${YELLOW}正在清理并编译项目...${NC}"
mvn clean compile -q
if [ $? -ne 0 ]; then
    echo -e "${RED}✗ 项目编译失败${NC}"
    exit 1
fi
echo -e "${GREEN}✓ 项目编译成功${NC}"
echo ""

# 运行单元测试
echo -e "${YELLOW}正在运行单元测试...${NC}"
mvn test -Dtest=SendMessageServiceTest -pl swifttrack-application
UNIT_TEST_RESULT=$?

if [ $UNIT_TEST_RESULT -eq 0 ]; then
    echo -e "${GREEN}✓ 单元测试通过${NC}"
else
    echo -e "${RED}✗ 单元测试失败${NC}"
fi
echo ""

# 运行集成测试
echo -e "${YELLOW}正在运行集成测试...${NC}"
mvn test -Dtest=SendMessageServiceIntegrationTest -pl swifttrack-application
INTEGRATION_TEST_RESULT=$?

if [ $INTEGRATION_TEST_RESULT -eq 0 ]; then
    echo -e "${GREEN}✓ 集成测试通过${NC}"
else
    echo -e "${RED}✗ 集成测试失败${NC}"
fi
echo ""

# 运行性能测试
echo -e "${YELLOW}正在运行性能测试...${NC}"
mvn test -Dtest=SendMessageServicePerformanceTest -pl swifttrack-application
PERFORMANCE_TEST_RESULT=$?

if [ $PERFORMANCE_TEST_RESULT -eq 0 ]; then
    echo -e "${GREEN}✓ 性能测试通过${NC}"
else
    echo -e "${RED}✗ 性能测试失败${NC}"
fi
echo ""

# 运行所有SendMessageService相关测试
echo -e "${YELLOW}正在运行完整测试套件...${NC}"
mvn test -Dtest="*SendMessageService*" -pl swifttrack-application
ALL_TESTS_RESULT=$?

# 生成测试报告
echo ""
echo "=========================================="
echo "测试结果汇总"
echo "=========================================="

if [ $UNIT_TEST_RESULT -eq 0 ]; then
    echo -e "单元测试:     ${GREEN}✓ 通过${NC}"
else
    echo -e "单元测试:     ${RED}✗ 失败${NC}"
fi

if [ $INTEGRATION_TEST_RESULT -eq 0 ]; then
    echo -e "集成测试:     ${GREEN}✓ 通过${NC}"
else
    echo -e "集成测试:     ${RED}✗ 失败${NC}"
fi

if [ $PERFORMANCE_TEST_RESULT -eq 0 ]; then
    echo -e "性能测试:     ${GREEN}✓ 通过${NC}"
else
    echo -e "性能测试:     ${RED}✗ 失败${NC}"
fi

if [ $ALL_TESTS_RESULT -eq 0 ]; then
    echo -e "完整测试套件: ${GREEN}✓ 通过${NC}"
else
    echo -e "完整测试套件: ${RED}✗ 失败${NC}"
fi

echo ""

# 检查测试覆盖率（如果有jacoco插件）
if mvn help:describe -Dplugin=org.jacoco:jacoco-maven-plugin -q &> /dev/null; then
    echo -e "${YELLOW}正在生成测试覆盖率报告...${NC}"
    mvn jacoco:report -pl swifttrack-application -q
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ 测试覆盖率报告已生成: swifttrack-application/target/site/jacoco/index.html${NC}"
    fi
fi

# 最终结果
echo ""
if [ $ALL_TESTS_RESULT -eq 0 ]; then
    echo -e "${GREEN}🎉 所有测试通过！SendMessageService测试套件运行成功。${NC}"
    exit 0
else
    echo -e "${RED}❌ 部分测试失败，请检查上述输出信息。${NC}"
    exit 1
fi 