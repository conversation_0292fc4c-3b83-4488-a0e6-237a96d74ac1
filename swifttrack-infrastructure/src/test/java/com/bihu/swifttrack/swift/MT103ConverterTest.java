package com.bihu.swifttrack.swift;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.bihu.swifttrack.mybatis.repository.po.MessageDetailsPO;
import com.bihu.swifttrack.mybatis.repository.po.MessagesPO;
import com.bihu.swifttrack.mybatis.repository.po.ReceiverPO;
import com.bihu.swifttrack.mybatis.repository.po.SenderPO;
import com.bihu.swifttrack.swift.convertor.StandardMT103Convertor;
import com.prowidesoftware.swift.model.field.Field32A;
import com.prowidesoftware.swift.model.field.Field50A;
import com.prowidesoftware.swift.model.field.Field59A;
import com.prowidesoftware.swift.model.mt.mt1xx.MT103;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;

public class MT103ConverterTest {

  @Test
  void testConvert() throws Exception {
    // Prepare test data
    MessagesPO message = new MessagesPO();
//    message.setMessageId("BT000OR2501208");
    message.setId(****************L);

    SenderPO sender = new SenderPO();
    sender.setBicCode("BTCBCNBJXRLY");
    sender.setName("rongtuoguoji");
    sender.setInstitution("baotou");
    sender.setCountry("CN");
    sender.setAccount("*********");

    ReceiverPO receiver = new ReceiverPO();
    receiver.setBicCode("CITIUS33RLY");
    receiver.setName("TEST2");
    receiver.setInstitution("BEIJING34");
    receiver.setCountry("CN");
    receiver.setAccount("*********");

    List<MessageDetailsPO> messageDetails = new ArrayList<>();
    messageDetails.add(createMessageDetail("32A",
        "{\"date\":\"250320\",\"currency\":\"CNY\",\"amount\":\"1,12\"}"));
    messageDetails.add(createMessageDetail("33B", "{\"currency\":\"CNY\",\"amount\":\"1,12\"}"));
    messageDetails.add(createMessageDetail("53A",
        "{\"account\":\"/151000179013000144757\",\"bicCode\":\"BTCBCNBJRLY\"}"));
    messageDetails.add(createMessageDetail("71A", "{\"code\":\"SHA\"}"));
    messageDetails.add(createMessageDetail("71F", "{\"currency\":\"CNY\",\"amount\":\"0,\"}"));

    MessageContext context = new MessageContext(message, sender, receiver, messageDetails);

    // Execute conversion
    StandardMT103Convertor converter = new StandardMT103Convertor();
    MT103 result = converter.convert(context);

    // Verify results
    assertNotNull(result);
    assertEquals("BTCBCNBJARLY", result.getSender());
    assertEquals("CITIUS33XRLY", result.getReceiver());

    assertEquals("****************", result.getField20().getValue());
    assertEquals("CRED", result.getField23B().getValue());

    Field32A field32A = result.getField32A();
    assertNotNull(field32A);
    assertEquals("250320", field32A.getDate());
    assertEquals("CNY", field32A.getCurrency());
    assertEquals("1,12", field32A.getAmount());

    Field50A field50A = result.getField50A();
    assertNotNull(field50A);
    assertEquals("BTCBCNBJARLY", field50A.getIdentifierCode());
    assertEquals("rongtuoguoji", field50A.getComponent1());
    assertTrue(field50A.getComponent2().contains("baotou"));
    assertEquals("/*********", field50A.getAccount());

    Field59A field59A = result.getField59A();
    assertNotNull(field59A);
    assertEquals("/*********", field59A.getAccount());
    assertTrue(field59A.getComponent2().contains("TEST2"));
    assertTrue(field59A.getComponent2().contains("BEIJING34"));

    assertEquals("SHA", result.getField71A().getValue());

    // Verify optional fields
    assertNotNull(result.getField33B());
    assertNotNull(result.getField53A());
    assertNotNull(result.getField71F());
  }

  private MessageDetailsPO createMessageDetail(String fieldName, String fieldValue) {
    MessageDetailsPO detail = new MessageDetailsPO();
    detail.setFieldName(fieldName);
    detail.setFieldValue(fieldValue);
    detail.setCreatedAt(LocalDateTime.now());
    detail.setUpdatedAt(LocalDateTime.now());
    detail.setIsDeleted(0);
    return detail;
  }
}
