<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bihu.swifttrack.mybatis.repository.dao.MessagesMapper">
    <select id="getMessagingList" parameterType="com.bihu.swifttrack.dto.MessagePageFilterDTO"
            resultType="com.bihu.swifttrack.mybatis.repository.dto.MessagingRowDTO">
        SELECT
        m.id, m.status, m.message_type, m.direction, m.amount, m.created_at, m.extra_info,
        s.bic_code as payer_bic_code, s.institution as payer_bank, s.account as payer_account, s.address as payer_address, s.name as payer_name,
        s.country as payer_country,
        r.bic_code as payee_bic_code, r.institution as payee_bank, r.account as payee_account, r.address as payee_address, r.name as payee_name,
        r.country as payee_country
        FROM messages m
        JOIN receiver r ON m.id = r.message_id
        JOIN sender s ON m.id = s.message_id
        <where>
            COALESCE(m.is_deleted, 0) != 1
            AND m.direction = #{direction}
            AND m.message_type IN
            <foreach item="type" collection="messageTypes" open="(" separator="," close=")">
                #{type} 
            </foreach>
            <if test="status == 0">
                AND m.created_by = #{id}
                AND m.status = 0
            </if>
            <if test="status == 1">
                AND m.status = 1
                AND (
                    m.assignee = #{id}
                    <if test="confirmIds != null and confirmIds.size() > 0">
                        OR m.id IN 
                        <foreach item="cId" collection="confirmIds" open="(" separator="," close=")">
                            #{cId}
                        </foreach>
                    </if>
                )
            </if>
            <if test="status == 2">
                AND m.id IN
                <foreach item="id" collection="hasDoneIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="status == 3">
                AND (m.created_by = #{id} or  m.assignee = #{id})
                AND m.status = -1
            </if>
            <if test="status == 4">
                AND m.created_by = #{id}
                AND m.status NOT IN (0, -3)
            </if>
            <if test="status == 5">
                AND m.status = -2
            </if>
            <if test="status == 6">
                AND m.assignee = #{id}
                AND m.status = -3
            </if>
            <if test="status == 7">
                AND m.status = 0
            </if>
            <if test="status == 8">
                AND m.assignee = #{id}
                AND m.status = 1
            </if>
        </where>
        order by m.created_at desc
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <select id="countMessaging" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM messages m
        <where>
            COALESCE(m.is_deleted, 0) != 1
            AND m.direction = #{direction}
            AND m.message_type IN
            <foreach item="type" collection="messageTypes" open="(" separator="," close=")">
                #{type}
            </foreach>
            <if test="status == 0">
                AND m.created_by = #{id}
                AND m.status = 0
            </if>
            <if test="status == 1">
                AND m.status = 1
                AND (
                    m.assignee = #{id}
                    <if test="confirmIds != null and confirmIds.size() > 0">
                        OR m.id IN
                        <foreach item="cId" collection="confirmIds" open="(" separator="," close=")">
                            #{cId}
                        </foreach>
                    </if>
                )
            </if>
            <if test="status == 2">
                AND m.id IN
                <foreach item="id" collection="hasDoneIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="status == 3">
                AND (m.created_by = #{id} or  m.assignee = #{id})
                AND m.status = -1
            </if>
            <if test="status == 4">
                AND m.created_by = #{id}
                AND m.status NOT IN (0, -3)
            </if>
            <if test="status == 5">
                AND m.status = -2
            </if>
            <if test="status == 6">
                AND m.assignee = #{id}
                AND m.status = -3
            </if>
            <if test="status == 7">
                AND m.status = 0
            </if>
            <if test="status == 8">
                AND m.assignee = #{id}
                AND m.status = 1
            </if>
        </where>
    </select>

    <select id="getMessagingDetail" resultMap="MessagingDetailResultMap">
        SELECT m.id,
               m.status,
               m.message_type,
               m.direction,
               m.created_at,
               m.extra_info,
               s.bic_code    as payer_bic_code,
               s.institution as payer_bank,
               s.account     as payer_account,
               s.address     as payer_address,
               s.name        as payer_name,
               s.country     as payer_country,
               r.bic_code    as payee_bic_code,
               r.institution as payee_bank,
               r.account     as payee_account,
               r.address     as payee_address,
               r.name        as payee_name,
               r.country     as payee_country,
               t.status     as process_claim_status,
               md.field_name,
               md.field_value
        FROM messages m
                 JOIN receiver r ON m.id = r.message_id
                 JOIN sender s ON m.id = s.message_id
                 JOIN message_details md ON m.id = md.message_id
                 LEFT JOIN process_instance pi ON pi.business_key = m.id
                 LEFT JOIN task t ON t.process_instance_id = pi.id
        WHERE m.id = #{messagingId} AND COALESCE(m.is_deleted, 0) != 1 AND COALESCE(md.is_deleted, 0) != 1
    </select>

    <resultMap id="MessagingDetailResultMap" type="com.bihu.swifttrack.mybatis.repository.dto.MessagingDTO">
        <result property="id" column="id"/>
        <result property="status" column="status"/>
        <result property="direction" column="direction"/>
        <result property="createdAt" column="created_at"/>
        <result property="messageType" column="message_type" />
        <result property="preMessageId" column="pre_message_id" />

        <result property="payerBank" column="payer_bank"/>
        <result property="payerName" column="payer_name"/>
        <result property="payerBicCode" column="payer_bic_code"/>
        <result property="payerAccount" column="payer_account"/>
        <result property="payerAddress" column="payer_address"/>
        <result property="payerCountry" column="payer_country"/>

        <result property="payeeName" column="payee_name"/>
        <result property="payeeBicCode" column="payee_bic_code"/>
        <result property="payeeAccount" column="payee_account"/>
        <result property="payeeAddress" column="payee_address"/>
        <result property="payeeBank" column="payee_bank"/>
        <result property="payeeCountry" column="payee_country"/>
        <result property="claimStatus" column="process_claim_status" />
        <collection property="messagingDetails" ofType="com.bihu.swifttrack.mybatis.repository.dto.MessagingDetailDTO">
            <result property="fieldName" column="field_name"/>
            <result property="fieldValue" column="field_value"/>
        </collection>
    </resultMap>
</mapper>
