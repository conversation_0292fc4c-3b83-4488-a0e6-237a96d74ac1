package com.bihu.swifttrack.swift.convertor;

import com.bihu.swifttrack.common.utils.JSONUtil;
import com.bihu.swifttrack.mybatis.repository.po.MessageDetailsPO;
import com.bihu.swifttrack.swift.Block3Utils;
import com.bihu.swifttrack.swift.MessageContext;
import com.bihu.swifttrack.swift.MessageConvertor;
import com.prowidesoftware.swift.model.field.Field;
import com.prowidesoftware.swift.model.field.Field20;
import com.prowidesoftware.swift.model.field.Field21;
import com.prowidesoftware.swift.model.field.Field25;
import com.prowidesoftware.swift.model.field.Field28C;
import com.prowidesoftware.swift.model.field.Field60F;
import com.prowidesoftware.swift.model.field.Field60M;
import com.prowidesoftware.swift.model.field.Field61;
import com.prowidesoftware.swift.model.field.Field62F;
import com.prowidesoftware.swift.model.field.Field62M;
import com.prowidesoftware.swift.model.field.Field64;
import com.prowidesoftware.swift.model.field.Field65;
import com.prowidesoftware.swift.model.field.Field86;
import com.prowidesoftware.swift.model.mt.mt9xx.MT940;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

/**
 * StandardMT940Convertor
 *
 * <p></p>
 *
 * <AUTHOR>
 * @date 2024/09/04
 */
@Slf4j
public class StandardMT940Convertor implements MessageConvertor<MT940> {

  @Override
  public MT940 convert(MessageContext context) throws Exception {
    MT940 mt940 = new MT940();
    mt940.setSender(context.getSender().getBicCode());
    mt940.setReceiver(context.getReceiver().getBicCode());

    Block3Utils.mur(mt940.getSwiftMessage(), context.getMessage().getId());
    List<MessageDetailsPO> messageDetailsPOList = context.getMessageDetails();

    // 处理单字段
    addField(mt940, Field20.class, getFieldValue(messageDetailsPOList, Field20.NAME));
    addField(mt940, Field21.class, getFieldValue(messageDetailsPOList, Field21.NAME)); // MT940 特有
    addField(mt940, Field25.class, getFieldValue(messageDetailsPOList, Field25.NAME));
    addField(mt940, Field28C.class, getFieldValue(messageDetailsPOList, Field28C.NAME));

    addMutuallyExclusiveField(mt940, Field60F.class, Field60M.class, messageDetailsPOList);
    addListFields(mt940, messageDetailsPOList);
    addMutuallyExclusiveField(mt940, Field62F.class, Field62M.class, messageDetailsPOList);
    addField(mt940, Field64.class, getFieldValue(messageDetailsPOList, Field64.NAME));
    List<Field65> field65Values = getFieldValues(messageDetailsPOList, Field65.class);
    field65Values.forEach(mt940::addField); // MT940 特有

    addField(mt940, Field86.class, getFieldValue(messageDetailsPOList, Field86.NAME));
    return mt940;
  }

  private void addListFields(MT940 mt940, List<MessageDetailsPO> messageDetailsPOList)
      throws Exception {
    List<Field61> field61Values = getFieldValues(messageDetailsPOList, Field61.class);
    field61Values.forEach(mt940::addField);
  }

  private Optional<String> getFieldValue(List<MessageDetailsPO> list, String fieldName) {
    return list.stream()
        .filter(d -> d.getFieldName().equals(fieldName))
        .map(MessageDetailsPO::getFieldValue)
        .findFirst();
  }

  private <T> List<T> getFieldValues(List<MessageDetailsPO> list, Class<T> fieldClass)
      throws Exception {
    return JSONUtil.parseList(
        getFieldValue(list, fieldClass.getSimpleName().substring(5)).orElse("[]"), fieldClass);
  }

  private <T1 extends Field, T2 extends Field> void addMutuallyExclusiveField(MT940 mt940,
      Class<T1> fieldClass1, Class<T2> fieldClass2, List<MessageDetailsPO> messageDetailsPOList) {

    Optional<String> field1 = getFieldValue(messageDetailsPOList,
        fieldClass1.getSimpleName().substring(5));
    Optional<String> field2 = getFieldValue(messageDetailsPOList,
        fieldClass2.getSimpleName().substring(5));

    if (field1.isPresent()) {
      addField(mt940, fieldClass1, field1);
    } else if (field2.isPresent()) {
      addField(mt940, fieldClass2, field2);
    } else {
      log.error("Neither {} nor {} is present", fieldClass1.getSimpleName(),
          fieldClass2.getSimpleName());
    }
  }

  private <T extends Field> void addField(MT940 mt940, Class<T> fieldClass,
      Optional<String> fieldValue) {
    fieldValue.ifPresent(value -> {
      try {
        T field = JSONUtil.parse(value, fieldClass);
        mt940.addField(field);
      } catch (Exception e) {
        log.error("Error parsing field " + fieldClass.getSimpleName() + ": " + value, e);
      }
    });
  }

  @Override
  public String message(MT940 msg) {
    return msg.message();
  }
}
