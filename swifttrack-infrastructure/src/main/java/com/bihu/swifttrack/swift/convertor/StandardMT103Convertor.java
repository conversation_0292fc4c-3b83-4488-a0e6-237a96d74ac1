package com.bihu.swifttrack.swift.convertor;

import com.bihu.swifttrack.common.enums.MessagingDetailFormField;
import com.bihu.swifttrack.common.utils.JSONUtil;
import com.bihu.swifttrack.dto.BaseInfo;
import com.bihu.swifttrack.mybatis.repository.po.MessageDetailsPO;
import com.bihu.swifttrack.swift.Block3Utils;
import com.bihu.swifttrack.swift.MessageContext;
import com.bihu.swifttrack.swift.MessageConvertor;
import com.bihu.swifttrack.swift.fields.FieldGenerator;
import com.bihu.swifttrack.swift.fields.FieldGeneratorFactory;
import com.prowidesoftware.swift.model.field.Field;
import com.prowidesoftware.swift.model.field.Field20;
import com.prowidesoftware.swift.model.field.Field21;
import com.prowidesoftware.swift.model.field.Field23B;
import com.prowidesoftware.swift.model.field.Field32A;
import com.prowidesoftware.swift.model.field.Field33B;
import com.prowidesoftware.swift.model.field.Field50K;
import com.prowidesoftware.swift.model.field.Field52A;
import com.prowidesoftware.swift.model.field.Field53A;
import com.prowidesoftware.swift.model.field.Field53B;
import com.prowidesoftware.swift.model.field.Field54A;
import com.prowidesoftware.swift.model.field.Field56A;
import com.prowidesoftware.swift.model.field.Field57A;
import com.prowidesoftware.swift.model.field.Field59;
import com.prowidesoftware.swift.model.field.Field59A;
import com.prowidesoftware.swift.model.field.Field70;
import com.prowidesoftware.swift.model.field.Field71A;
import com.prowidesoftware.swift.model.field.Field71F;
import com.prowidesoftware.swift.model.field.Field71G;
import com.prowidesoftware.swift.model.field.Field72;
import com.prowidesoftware.swift.model.mt.mt1xx.MT103;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class StandardMT103Convertor implements MessageConvertor<MT103> {

  @Override
  public MT103 convert(MessageContext context) throws Exception {
    MT103 mt103 = new MT103();
    List<MessageDetailsPO> messageDetailsPOList = context.getMessageDetails();

    Function<String, Optional<MessageDetailsPO>> getFieldDetail = fieldName ->
        messageDetailsPOList.stream()
            .filter(d -> d.getFieldName().equals(fieldName))
            .max(Comparator.comparing(MessageDetailsPO::getUpdatedAt));

    // 设置基本头字段
    mt103.setSender(context.getSender().getBicCode());
    mt103.setReceiver(context.getReceiver().getBicCode());
    String defaultRef = "RLY" + (context.getMessage().getId() != null ? ("" + context.getMessage()
        .getId()).substring(0, 13)
        : "000000");
    String relatedRef = null;
    String[] ref = new String[]{defaultRef, relatedRef};
    getFieldDetail.apply(MessagingDetailFormField.BASE_INFO.getFieldName())
        .map(MessageDetailsPO::getFieldValue).ifPresent(v -> {
          try {
            BaseInfo baseInfo = JSONUtil.parse(v, BaseInfo.class);
            mt103.setSender(baseInfo.getSender());
            mt103.setReceiver(baseInfo.getReceiver());
            if (StringUtils.isNotBlank(baseInfo.getSenderRef())) {
              ref[0] = baseInfo.getSenderRef();
            }
            if (StringUtils.isNotBlank(baseInfo.getRelatedRef())) {
              ref[1] = baseInfo.getRelatedRef();
            }
          } catch (Exception e) {
            log.error("Error parsing baseInfo: " + v, e);
          }
        });

    Block3Utils.mur(mt103.getSwiftMessage(), context.getMessage().getId());

    FieldGenerator<Field20> field20Generator = FieldGeneratorFactory.getGenerator(Field20.NAME);

    mt103.addField(field20Generator.generateField(ref[0]));

    if (StringUtils.isNotBlank(ref[1])) {
      mt103.addField(new Field21(ref[1]));
    }

    FieldGenerator<Field23B> field23BGenerator = FieldGeneratorFactory.getGenerator(Field23B.NAME);
    mt103.addField(field23BGenerator.generateField("CRED"));

    addField(mt103, Field32A.class,
        getFieldDetail.apply(Field32A.NAME).map(MessageDetailsPO::getFieldValue));
    addField(mt103, Field33B.class,
        getFieldDetail.apply(Field33B.NAME).map(MessageDetailsPO::getFieldValue));
    addField(mt103, Field50K.class,
        getFieldDetail.apply(Field50K.NAME).map(MessageDetailsPO::getFieldValue));
    addField(mt103, Field52A.class,
        getFieldDetail.apply(Field52A.NAME).map(MessageDetailsPO::getFieldValue));
    addField(mt103, Field53A.class,
        getFieldDetail.apply(Field53A.NAME).map(MessageDetailsPO::getFieldValue));
    addField(mt103, Field53B.class,
        getFieldDetail.apply(Field53B.NAME).map(MessageDetailsPO::getFieldValue));
    addField(mt103, Field54A.class,
        getFieldDetail.apply(Field54A.NAME).map(MessageDetailsPO::getFieldValue));
    addField(mt103, Field56A.class,
        getFieldDetail.apply(Field56A.NAME).map(MessageDetailsPO::getFieldValue));
    addField(mt103, Field57A.class,
        getFieldDetail.apply(Field57A.NAME).map(MessageDetailsPO::getFieldValue));
    addField(mt103, Field59A.class,
        getFieldDetail.apply(Field59A.NAME).map(MessageDetailsPO::getFieldValue));
    if (mt103.getField59A() == null) {
      addField(mt103, Field59.class,
          getFieldDetail.apply(Field59.NAME).map(MessageDetailsPO::getFieldValue));
    }
    addField(mt103, Field70.class,
        getFieldDetail.apply(Field70.NAME).map(MessageDetailsPO::getFieldValue));
    addLatestField71(mt103, getFieldDetail);
    addField(mt103, Field72.class,
        getFieldDetail.apply(Field72.NAME).map(MessageDetailsPO::getFieldValue));

    return mt103;
  }

  private void addLatestField71(MT103 mt103,
      Function<String, Optional<MessageDetailsPO>> getFieldDetail) {
    List<String> field71Names = Arrays.asList(Field71F.NAME, Field71G.NAME);

    addField(mt103, Field71A.class,
        getFieldDetail.apply(Field71A.NAME).map(MessageDetailsPO::getFieldValue));
    Optional<MessageDetailsPO> latestField71 = field71Names.stream()
        .map(getFieldDetail)
        .filter(Optional::isPresent)
        .map(Optional::get)
        .max(Comparator.comparing(MessageDetailsPO::getUpdatedAt,
            Comparator.nullsLast(Comparator.naturalOrder())));

    latestField71.ifPresent(field -> {
      String fieldName = field.getFieldName();
      String fieldValue = field.getFieldValue();
      try {
        switch (fieldName) {
          case Field71F.NAME:
            mt103.addField(JSONUtil.parse(fieldValue, Field71F.class));
            break;
          case Field71G.NAME:
            mt103.addField(JSONUtil.parse(fieldValue, Field71G.class));
            break;
        }
      } catch (Exception e) {
        log.error("Error parsing field " + fieldName + ": " + fieldValue, e);
      }
    });
  }

  private <T extends Field> void addField(MT103 mt103, Class<T> fieldClass,
      Optional<String> fieldValue) {
    fieldValue.ifPresent(value -> {
      try {
        log.debug("Parsing field {} with value: {}", fieldClass.getSimpleName(), value);
        T field = JSONUtil.parse(value, fieldClass);
        mt103.addField(field);
        log.info("Successfully added field {} to MT103", fieldClass.getSimpleName());
      } catch (Exception e) {
        log.error("Error parsing field " + fieldClass.getSimpleName() + ": " + value, e);
      }
    });
  }

  @Override
  public String message(MT103 msg) {
    return SwiftMessageCleaner.removeRepeatedAddress(msg.message());
  }
}
