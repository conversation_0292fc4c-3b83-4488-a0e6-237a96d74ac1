package com.bihu.swifttrack.swift;

import com.prowidesoftware.swift.model.SwiftBlock3;
import com.prowidesoftware.swift.model.SwiftMessage;
import com.prowidesoftware.swift.model.Tag;
import java.util.UUID;

/**
 * Block3Utils
 *
 * <p></p>
 *
 * <AUTHOR>
 * @date 2024/09/03
 */
public class Block3Utils {

  public static final String MUR_TAG = "108";

  public static void mur(SwiftMessage message, Long id) {
    String value = id == null ? UUID.randomUUID().toString() : String.valueOf(id);
    SwiftBlock3 block3 = message.getBlock3();
    if (block3 != null && block3.getTagByName(MUR_TAG) != null) {
      block3.getTagByName(MUR_TAG).setValue(value);
    } else {
      assert block3 != null;
      if (!block3.getTags().isEmpty()) {
        block3.getTags().clear();
      }
      block3.append(new Tag(MUR_TAG, value));
    }
  }

}
