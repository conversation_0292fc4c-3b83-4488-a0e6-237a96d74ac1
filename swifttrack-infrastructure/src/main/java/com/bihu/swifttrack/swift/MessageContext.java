package com.bihu.swifttrack.swift;

import com.bihu.swifttrack.mybatis.repository.po.MessageDetailsPO;
import com.bihu.swifttrack.mybatis.repository.po.MessagesPO;
import com.bihu.swifttrack.mybatis.repository.po.ReceiverPO;
import com.bihu.swifttrack.mybatis.repository.po.SenderPO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public class MessageContext {

  private MessagesPO message;

  private SenderPO sender;

  private ReceiverPO receiver;

  private List<MessageDetailsPO> messageDetails;

}
