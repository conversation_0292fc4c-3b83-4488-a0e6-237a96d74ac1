package com.bihu.swifttrack.swift.fields;

import com.prowidesoftware.swift.model.field.Field;
import java.util.HashMap;
import java.util.Map;

/**
 * FieldGeneratorFactory
 *
 * <p></p>
 *
 * <AUTHOR>
 * @date 2024/08/23
 */
public class FieldGeneratorFactory {

  private static final Map<String, FieldGenerator<?>> generators = new HashMap<>();

  static {
    // Initialize generators
    generators.put("20", new Field20Generator());
    generators.put("23B", new Field23BGenerator());
    generators.put("32A", new Field32AGenerator());
    generators.put("33B", new Field33BGenerator());
    generators.put("50K", new Field50KGenerator());
    generators.put("53A", new Field53AGenerator());
    generators.put("54A", new Field54AGenerator());
    generators.put("56A", new Field56AGenerator());
    generators.put("57A", new Field57AGenerator());
    generators.put("59", new Field59Generator());
    generators.put("71A", new Field71AGenerator());
    generators.put("71F", new Field71FGenerator());
    generators.put("72", new Field72Generator());
    // Add more generators as needed
  }

  /**
   * Get a field generator for the specified field name
   *
   * @param fieldName the name of the field
   * @return the corresponding field generator
   * @throws IllegalArgumentException if no generator is found for the given field name
   */
  @SuppressWarnings("unchecked")
  public static <T extends Field> FieldGenerator<T> getGenerator(String fieldName) {
    FieldGenerator<?> generator = generators.get(fieldName);
    if (generator == null) {
      throw new IllegalArgumentException("No generator found for field: " + fieldName);
    }
    return (FieldGenerator<T>) generator;
  }

  /**
   * Register a new field generator
   *
   * @param fieldName the name of the field
   * @param generator the generator for the field
   */
  public static void registerGenerator(String fieldName, FieldGenerator<?> generator) {
    generators.put(fieldName, generator);
  }
}
