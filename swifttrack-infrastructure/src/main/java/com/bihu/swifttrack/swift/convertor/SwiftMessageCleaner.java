package com.bihu.swifttrack.swift.convertor;

import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * SwiftMessageCleaner
 *
 * <p></p>
 *
 * <AUTHOR>
 * @date 2024/09/02
 */
public class SwiftMessageCleaner {

  public static String removeRepeatedAddress(String input) {
    Pattern pattern = Pattern.compile("(:59:[^\\r?\\n]*\\r?\\n)((?:(?!:\\d{2}:).*\\r?\\n)*)");
    Matcher matcher = pattern.matcher(input);

    StringBuffer result = new StringBuffer();
    while (matcher.find()) {
      String addressLines = matcher.group(2);
      String[] lines = addressLines.split("\\r?\\n", -1);

      StringBuilder uniqueLines = new StringBuilder();
      boolean hasRepeatedContent = false;

      if (lines.length > 0) {
        String firstLine = lines[0];
        StringBuilder optimizedFirstLine = new StringBuilder(firstLine);

        // Check if first line contains content from other lines
        for (int i = 1; i < lines.length; i++) {
          String line = lines[i].trim();
          if (!line.isEmpty()) {
            int index = optimizedFirstLine.indexOf(line);
            if (index != -1) {
              optimizedFirstLine.delete(index, index + line.length());
              hasRepeatedContent = true;
            }
          }
        }

        uniqueLines.append(optimizedFirstLine.toString().trim()).append("\r\n");

        // Append remaining lines that are not fully contained in the first line
        for (int i = 1; i < lines.length; i++) {
          String line = lines[i].trim();
          if (!line.isEmpty()) {
            uniqueLines.append(line).append("\r\n");
          }
        }
      }

      // Remove the last extra newline character
      String uniqueLinesStr = uniqueLines.toString().trim() + "\r\n";

      if (hasRepeatedContent) {
        matcher.appendReplacement(result, matcher.group(1) + uniqueLinesStr);
      } else {
        matcher.appendReplacement(result, matcher.group(0));
      }
    }
    matcher.appendTail(result);

    return result.toString();
  }

  public static void main(String[] args) {
    String input = "{1:F01NEWMRUMMARLY0000000000}{2:I103BTCBCNBJXRLYN}{3:{108:null}}{4:\r\n"
        + ":20:RLY000000\r\n"
        + ":23B:CRED\r\n"
        + ":32A:240906CNY1008,00\r\n"
        + ":33B:CNY898,00\r\n"
        + ":50A:/98900001\r\n"
        + "NEWMRUMMRLY\r\n"
        + ":57A:BTCBCNBJRLY\r\n"
        + ":59:/20050100020100020058\r\nXIAMEN KINGLAND CO., LTD,CHINA C AN\r\nD INTERNATIONAL BUILD 16/F,N1699 EA\r\nST HUANDAO ROAD,SIMING DISTR,XIAMEN\r\n"
        + ":70:INVOICE 1900018890\r\n"
        + "889900\r\n"
        + ":71A:OUR\r\n"
        + ":71G:CNY110,00\r\n"
        + "-}";

    String output = removeRepeatedAddress(input);
    System.out.println(output);
  }

}
