package com.bihu.swifttrack.swift.convertor;

import com.bihu.swifttrack.swift.MessageContext;
import com.bihu.swifttrack.swift.MessageConvertor;
import com.prowidesoftware.swift.model.field.Field;
import com.prowidesoftware.swift.model.field.Field20;
import com.prowidesoftware.swift.model.field.Field32A;
import com.prowidesoftware.swift.model.field.Field71A;
import com.prowidesoftware.swift.model.field.Field72;
import com.prowidesoftware.swift.model.mt.mt1xx.MT103;
import com.prowidesoftware.swift.model.mx.MxPacs00800102;
import com.prowidesoftware.swift.model.mx.MxWriteConfiguration;
import com.prowidesoftware.swift.model.mx.dic.ActiveCurrencyAndAmount;
import com.prowidesoftware.swift.model.mx.dic.ActiveOrHistoricCurrencyAndAmount;
import com.prowidesoftware.swift.model.mx.dic.BranchAndFinancialInstitutionIdentification4;
import com.prowidesoftware.swift.model.mx.dic.ChargesInformation5;
import com.prowidesoftware.swift.model.mx.dic.CreditTransferTransactionInformation11;
import com.prowidesoftware.swift.model.mx.dic.FIToFICustomerCreditTransferV02;
import com.prowidesoftware.swift.model.mx.dic.FinancialInstitutionIdentification7;
import com.prowidesoftware.swift.model.mx.dic.GroupHeader33;
import com.prowidesoftware.swift.model.mx.dic.PartyIdentification32;
import com.prowidesoftware.swift.model.mx.dic.RemittanceInformation5;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class ISOMT103Convertor implements MessageConvertor<MxPacs00800102> {

  private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyMMdd");

  private final StandardMT103Convertor standardMT103Convertor;

  @Override
  public MxPacs00800102 convert(MessageContext context) throws Exception {
    MT103 mt103 = standardMT103Convertor.convert(context);

    FIToFICustomerCreditTransferV02 creditTransfer = createCreditTransfer(mt103);
    CreditTransferTransactionInformation11 cdtTrfTxInf = prepareCreditTransferInformation(mt103);

    creditTransfer.getCdtTrfTxInf().add(cdtTrfTxInf);

    MxPacs00800102 mx = new MxPacs00800102();
    mx.setFIToFICstmrCdtTrf(creditTransfer); // set the credit transfer object to the MX message

    return mx;
  }

  private FIToFICustomerCreditTransferV02 createCreditTransfer(MT103 mt103) {
    FIToFICustomerCreditTransferV02 creditTransfer = new FIToFICustomerCreditTransferV02();
    GroupHeader33 header = prepareGroupHeader(mt103.getField20()); // Set the group header
    creditTransfer.setGrpHdr(header);
    return creditTransfer;
  }

  private GroupHeader33 prepareGroupHeader(Field20 field20) {
    GroupHeader33 header = new GroupHeader33();
    if (field20 != null) {
      header.setMsgId(field20.getValue());
    }
    header.setCreDtTm(OffsetDateTime.now(ZoneOffset.UTC)); // Current timestamp
    return header;
  }

  private CreditTransferTransactionInformation11 prepareCreditTransferInformation(
      MT103 mt103) {
    CreditTransferTransactionInformation11 cdtTrfTxInf = new CreditTransferTransactionInformation11();

    // Set debtor, creditor and instructed and instructing agent details
    cdtTrfTxInf.setDbtr(preparePartyIdentification(mt103.getField50A()));
    cdtTrfTxInf.setCdtr(preparePartyIdentification(mt103.getField59A()));
    cdtTrfTxInf.setCdtrAgt(
        prepareBranchAndFinancialInstitutionIdentification(mt103.getField52A()));
    cdtTrfTxInf.setDbtrAgt(prepareBranchAndFinancialInstitutionIdentification(mt103.getField57A()));
    // Set value date, currency, and amount
    prepareAmountAndSettlementDate(cdtTrfTxInf, mt103.getField32A());

    // Set charge information, if available
    prepareAndAddChargesInformation(cdtTrfTxInf, mt103.getField71A());

    // Set remittance information, if available
    prepareAndAddRemittanceInformation(cdtTrfTxInf, mt103.getField72());

    return cdtTrfTxInf;
  }

  private PartyIdentification32 preparePartyIdentification(Field field) {
    if (field != null) {
      PartyIdentification32 partyId = new PartyIdentification32();
      partyId.setNm(field.getComponent(2));
      return partyId;
    }
    return null;
  }

  private BranchAndFinancialInstitutionIdentification4 prepareBranchAndFinancialInstitutionIdentification(
      Field field) {
    if (field != null) {
      BranchAndFinancialInstitutionIdentification4 instdAgt = new BranchAndFinancialInstitutionIdentification4();
      FinancialInstitutionIdentification7 finInstId = new FinancialInstitutionIdentification7();
      finInstId.setBIC(field.getValue());
      instdAgt.setFinInstnId(finInstId);
      return instdAgt;
    }
    return null;
  }

  private void prepareAmountAndSettlementDate(
      CreditTransferTransactionInformation11 cdtTrfTxInf, Field32A field32A) {
    if (field32A != null) {
      ActiveCurrencyAndAmount amount = new ActiveCurrencyAndAmount();
      amount.setCcy(field32A.getCurrency());
      amount.setValue(new BigDecimal(field32A.getAmount()));

      cdtTrfTxInf.setIntrBkSttlmAmt(amount);

      LocalDate valueDate = LocalDate.parse(field32A.getDate(), DATE_FORMATTER);
      cdtTrfTxInf.setIntrBkSttlmDt(valueDate);
    }
  }

  private void prepareAndAddChargesInformation(
      CreditTransferTransactionInformation11 cdtTrfTxInf, Field71A field71A) {
    if (field71A != null) {
      ChargesInformation5 chargesInformation = new ChargesInformation5();

      ActiveOrHistoricCurrencyAndAmount chargesAmount = new ActiveOrHistoricCurrencyAndAmount();
      chargesAmount.setCcy("USD"); // E.g. currency
      chargesAmount.setValue(BigDecimal.ZERO); // E.g. amount (modify as needed)
      chargesInformation.setAmt(chargesAmount);

      // Set the agent based on provided value
      BranchAndFinancialInstitutionIdentification4 chargeBearer = new BranchAndFinancialInstitutionIdentification4();
      chargeBearer.setFinInstnId(
          new FinancialInstitutionIdentification7().setBIC(getBicForCharge(field71A)));

      chargesInformation.setPty(chargeBearer);

      cdtTrfTxInf.getChrgsInf().add(chargesInformation);
    }
  }

  private String getBicForCharge(Field71A field71A) {
    return switch (field71A.getValue()) {
      case "SHA" -> "SLEV"; // Shared
      case "BEN" -> "SHAR"; // Shared
      case "OUR" -> "DEBT"; // Our
      default -> "SLEV"; // Default to shared if unknown
    };
  }

  private void prepareAndAddRemittanceInformation(
      CreditTransferTransactionInformation11 cdtTrfTxInf, Field72 field72) {
    if (field72 != null) {
      RemittanceInformation5 remittanceInformation = new RemittanceInformation5();
      remittanceInformation.getUstrd().add(field72.getValue());
      cdtTrfTxInf.setRmtInf(remittanceInformation);
    }
  }

  @Override
  public String message(MxPacs00800102 msg) {
    MxWriteConfiguration conf = new MxWriteConfiguration();
    conf.documentPrefix = null;
    return msg.message(conf);
  }
}
