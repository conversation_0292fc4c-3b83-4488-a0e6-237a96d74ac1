package com.bihu.swifttrack.swift;

import com.bihu.swifttrack.common.enums.MessageFormat;
import com.bihu.swifttrack.common.enums.MessagingType;
import com.bihu.swifttrack.mybatis.repository.po.MessageDetailsPO;
import com.bihu.swifttrack.mybatis.repository.po.MessagesPO;
import com.bihu.swifttrack.mybatis.repository.po.ReceiverPO;
import com.bihu.swifttrack.mybatis.repository.po.SenderPO;
import com.bihu.swifttrack.swift.convertor.ISOMT103Convertor;
import com.bihu.swifttrack.swift.convertor.ISOMT199Convertor;
import com.bihu.swifttrack.swift.convertor.ISOMT202Convertor;
import com.bihu.swifttrack.swift.convertor.ISOMT299Convertor;
import com.bihu.swifttrack.swift.convertor.ISOMT910Convertor;
import com.bihu.swifttrack.swift.convertor.ISOMT950Convertor;
import com.bihu.swifttrack.swift.convertor.ISOMT999Convertor;
import com.bihu.swifttrack.swift.convertor.StandardMT103Convertor;
import com.bihu.swifttrack.swift.convertor.StandardMT199Convertor;
import com.bihu.swifttrack.swift.convertor.StandardMT202Convertor;
import com.bihu.swifttrack.swift.convertor.StandardMT299Convertor;
import com.bihu.swifttrack.swift.convertor.StandardMT910Convertor;
import com.bihu.swifttrack.swift.convertor.StandardMT940Convertor;
import com.bihu.swifttrack.swift.convertor.StandardMT950Convertor;
import com.bihu.swifttrack.swift.convertor.StandardMT999Convertor;
import com.prowidesoftware.swift.model.mt.mt1xx.MT103;
import com.prowidesoftware.swift.model.mt.mt1xx.MT199;
import com.prowidesoftware.swift.model.mt.mt2xx.MT202;
import com.prowidesoftware.swift.model.mt.mt2xx.MT299;
import com.prowidesoftware.swift.model.mt.mt9xx.MT910;
import com.prowidesoftware.swift.model.mt.mt9xx.MT940;
import com.prowidesoftware.swift.model.mt.mt9xx.MT950;
import com.prowidesoftware.swift.model.mt.mt9xx.MT999;
import com.prowidesoftware.swift.model.mx.MxCamt02900112;
import com.prowidesoftware.swift.model.mx.MxCamt05300108;
import com.prowidesoftware.swift.model.mx.MxPacs00800102;
import com.prowidesoftware.swift.model.mx.MxPacs00900108;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SwiftMessageConvertor {

  private static final Logger log = LoggerFactory.getLogger(SwiftMessageConvertor.class);

  public static String getStandardFormat(MessageFormat format, MessagesPO messagesPO,
      SenderPO senderPO,
      ReceiverPO receiverPO, List<MessageDetailsPO> detailList) throws Exception {
    MessagingType type = MessagingType.toMessagingType(messagesPO.getMessageType());
    if (format == MessageFormat.MT) {
      return convertToMT(type, messagesPO, senderPO, receiverPO, detailList);
    }

    if (format == MessageFormat.ISO20022) {
      Function<String, Optional<String>> getFieldValue = fieldName ->
          detailList.stream()
              .filter(d -> d.getFieldName().equals(fieldName))
              .map(MessageDetailsPO::getFieldValue)
              .findFirst();

      return switch (type) {
        case MX_PACS004 -> getFieldValue.apply("mxPacs004").orElse(null);
        case MX_PACS008 -> getFieldValue.apply("mxPacs008").orElse(null);
        case MX_PACS009 -> getFieldValue.apply("mxPacs009").orElse(null);
        case MX_CAMT053 -> getFieldValue.apply("mxCamt053").orElse(null);
        case MX_CAMT054 -> getFieldValue.apply("mxCamt054").orElse(null);
        default -> null;
      };
    }

    throw new IllegalArgumentException("Unexpected value: " + format);
  }

  public static String convertToMT(MessagingType type, MessagesPO messagesPO, SenderPO senderPO,
      ReceiverPO receiverPO,
      List<MessageDetailsPO> detailList) throws Exception {
    return switch (type) {
      case MT103, CA103 ->
          SwiftMessageConvertor.convertToMT103(messagesPO, senderPO, receiverPO, detailList);
      case MT199 ->
          SwiftMessageConvertor.convertToMT199(messagesPO, senderPO, receiverPO, detailList);
      case MT202 ->
          SwiftMessageConvertor.convertToMT202(messagesPO, senderPO, receiverPO, detailList);
      case MT299 ->
          SwiftMessageConvertor.convertToMT299(messagesPO, senderPO, receiverPO, detailList);
      case MT910 ->
          SwiftMessageConvertor.convertToMT910(messagesPO, senderPO, receiverPO, detailList);
      case MT940 ->
          SwiftMessageConvertor.convertToMT940(messagesPO, senderPO, receiverPO, detailList);
      case MT950 ->
          SwiftMessageConvertor.convertToMT950(messagesPO, senderPO, receiverPO, detailList);
      case MT999 ->
          SwiftMessageConvertor.convertToMT999(messagesPO, senderPO, receiverPO, detailList);
      default -> throw new IllegalArgumentException("Unexpected value: " + type);
    };
  }

  public static String convertToMT103(MessagesPO message, SenderPO sender, ReceiverPO receiver,
      List<MessageDetailsPO> messageDetails) throws Exception {
    StandardMT103Convertor convertor = new StandardMT103Convertor();
    MT103 mt103 = convertor.convert(new MessageContext(message, sender, receiver, messageDetails));
    return convertor.message(mt103);
  }

  /**
   * 转换成 MT103 ISO 报文
   *
   * @param message        消息对象
   * @param sender         发送方对象
   * @param receiver       接受方对象
   * @param messageDetails 消息附属字段
   * @return MT103 ISO 报文对象
   */
  public static String convertMT103OfISO20022(MessagesPO message, SenderPO sender,
      ReceiverPO receiver,
      List<MessageDetailsPO> messageDetails) throws Exception {
    ISOMT103Convertor convertor = new ISOMT103Convertor(new StandardMT103Convertor());
    MxPacs00800102 isoMT103 = convertor.convert(
        new MessageContext(message, sender, receiver, messageDetails));
    return convertor.message(isoMT103);
  }

  /**
   * 转换成 MT202 报文
   *
   * @param message        消息对象
   * @param sender         发送方对象
   * @param receiver       接受方对象
   * @param messageDetails 消息附属字段
   * @return MT202报文对象
   */
  public static String convertToMT202(MessagesPO message, SenderPO sender, ReceiverPO receiver,
      List<MessageDetailsPO> messageDetails) throws Exception {
    StandardMT202Convertor convertor = new StandardMT202Convertor();
    MT202 mt202 = convertor.convert(new MessageContext(message, sender, receiver, messageDetails));
    return convertor.message(mt202);
  }

  /**
   * 转换成 MT202 ISO 报文
   *
   * @param message        消息对象
   * @param sender         发送方对象
   * @param receiver       接受方对象
   * @param messageDetails 消息附属字段
   * @return MT202 ISO 报文对象
   */
  public static String convertToMxPacs00900108(MessagesPO message, SenderPO sender,
      ReceiverPO receiver, List<MessageDetailsPO> messageDetails)
      throws Exception {
    ISOMT202Convertor convertor = new ISOMT202Convertor(new StandardMT202Convertor());
    MxPacs00900108 msg = convertor.convert(
        new MessageContext(message, sender, receiver, messageDetails));
    return convertor.message(msg);
  }

  /**
   * 转换成 MT199 报文
   *
   * @param message        消息对象
   * @param sender         发送方对象
   * @param receiver       接受方对象
   * @param messageDetails 消息附属字段
   * @return MT199报文对象
   */
  public static String convertToMT199(MessagesPO message, SenderPO sender, ReceiverPO receiver,
      List<MessageDetailsPO> messageDetails) throws Exception {
    StandardMT199Convertor convertor = new StandardMT199Convertor();
    MT199 mt199 = convertor.convert(new MessageContext(message, sender, receiver, messageDetails));
    return convertor.message(mt199);
  }

  /**
   * 转换成 MT299 报文
   *
   * @param message        消息对象
   * @param sender         发送方对象
   * @param receiver       接受方对象
   * @param messageDetails 消息附属字段
   * @return MT299报文对象
   */
  public static String convertToMT299(MessagesPO message, SenderPO sender, ReceiverPO receiver,
      List<MessageDetailsPO> messageDetails) throws Exception {
    StandardMT299Convertor convertor = new StandardMT299Convertor();
    MT299 mt299 = convertor.convert(new MessageContext(message, sender, receiver, messageDetails));
    return convertor.message(mt299);
  }

  /**
   * Converts an MT199 message to an ISO20022 message.
   *
   * @param message        The message object.
   * @param sender         The sender object.
   * @param receiver       The receiver object.
   * @param messageDetails The message details list.
   * @return The converted ISO20022 message.
   */
  public static String convertMT199toISO20022(MessagesPO message, SenderPO sender,
      ReceiverPO receiver,
      List<MessageDetailsPO> messageDetails) throws Exception {
    ISOMT199Convertor convertor = new ISOMT199Convertor(new StandardMT199Convertor());
    MxPacs00900108 msg = convertor.convert(
        new MessageContext(message, sender, receiver, messageDetails));
    return convertor.message(msg);
  }

  /**
   * Converts an MT299 message to an ISO20022 message.
   *
   * @param message        The message object.
   * @param sender         The sender object.
   * @param receiver       The receiver object.
   * @param messageDetails The message details list.
   * @return The converted ISO20022 message.
   */
  public static String convertMT299toISO20022(MessagesPO message, SenderPO sender,
      ReceiverPO receiver,
      List<MessageDetailsPO> messageDetails) throws Exception {
    ISOMT299Convertor convertor = new ISOMT299Convertor(new StandardMT299Convertor());
    MxPacs00900108 msg = convertor.convert(
        new MessageContext(message, sender, receiver, messageDetails));
    return convertor.message(msg);
  }

  /**
   * 转换成 MT910 报文
   *
   * @param message
   * @param sender
   * @param receiver
   * @param messageDetails
   * @return
   * @throws Exception
   */
  public static String convertToMT910(MessagesPO message, SenderPO sender, ReceiverPO receiver,
      List<MessageDetailsPO> messageDetails) throws Exception {
    StandardMT910Convertor convertor = new StandardMT910Convertor();
    MT910 mt910 = convertor.convert(new MessageContext(message, sender, receiver, messageDetails));
    return convertor.message(mt910);
  }


  public static String convertToMT940(MessagesPO message, SenderPO sender, ReceiverPO receiver,
      List<MessageDetailsPO> messageDetails) throws Exception {
    StandardMT940Convertor convertor = new StandardMT940Convertor();
    MT940 mt940 = convertor.convert(new MessageContext(message, sender, receiver, messageDetails));
    return convertor.message(mt940);
  }

  public static String convertMT910toISO20022(MessagesPO message, SenderPO sender,
      ReceiverPO receiver,
      List<MessageDetailsPO> messageDetails) throws Exception {
    ISOMT910Convertor convertor = new ISOMT910Convertor(new StandardMT910Convertor());
    MxPacs00800102 msg = convertor.convert(
        new MessageContext(message, sender, receiver, messageDetails));
    return convertor.message(msg);
  }

  /**
   * 转换成 MT950 报文
   *
   * @param message
   * @param sender
   * @param receiver
   * @param messageDetails
   * @return
   * @throws Exception
   */
  public static String convertToMT950(MessagesPO message, SenderPO sender, ReceiverPO receiver,
      List<MessageDetailsPO> messageDetails) throws Exception {
    StandardMT950Convertor convertor = new StandardMT950Convertor();
    MT950 mt950 = convertor.convert(new MessageContext(message, sender, receiver, messageDetails));
    return convertor.message(mt950);
  }

  public static String convertMT950toISO20022(MessagesPO message, SenderPO sender,
      ReceiverPO receiver,
      List<MessageDetailsPO> messageDetails) throws Exception {
    ISOMT950Convertor convertor = new ISOMT950Convertor(new StandardMT950Convertor());
    MxCamt05300108 msg = convertor.convert(
        new MessageContext(message, sender, receiver, messageDetails));
    return convertor.message(msg);
  }

  /**
   * 转换成 MT999 报文
   *
   * @param message
   * @param sender
   * @param receiver
   * @param messageDetails
   * @return
   * @throws Exception
   */
  public static String convertToMT999(MessagesPO message, SenderPO sender, ReceiverPO receiver,
      List<MessageDetailsPO> messageDetails) throws Exception {
    StandardMT999Convertor convertor = new StandardMT999Convertor();
    MT999 mt999 = convertor.convert(new MessageContext(message, sender, receiver, messageDetails));
    return convertor.message(mt999);
  }

  public static String convertMT999toISO20022(MessagesPO message, SenderPO sender,
      ReceiverPO receiver,
      List<MessageDetailsPO> messageDetails) throws Exception {
    ISOMT999Convertor convertor = new ISOMT999Convertor(new StandardMT999Convertor());
    MxCamt02900112 msg = convertor.convert(
        new MessageContext(message, sender, receiver, messageDetails));
    return convertor.message(msg);
  }

}
