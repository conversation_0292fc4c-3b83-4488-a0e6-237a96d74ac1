package com.bihu.swifttrack.swift.convertor;

import com.bihu.swifttrack.common.utils.JSONUtil;
import com.bihu.swifttrack.mybatis.repository.po.MessageDetailsPO;
import com.bihu.swifttrack.swift.Block3Utils;
import com.bihu.swifttrack.swift.MessageContext;
import com.bihu.swifttrack.swift.MessageConvertor;
import com.prowidesoftware.swift.model.Tag;
import com.prowidesoftware.swift.model.field.Field;
import com.prowidesoftware.swift.model.field.Field20;
import com.prowidesoftware.swift.model.field.Field21;
import com.prowidesoftware.swift.model.field.Field25;
import com.prowidesoftware.swift.model.field.Field32A;
import com.prowidesoftware.swift.model.field.Field50A;
import com.prowidesoftware.swift.model.field.Field72;
import com.prowidesoftware.swift.model.mt.mt9xx.MT910;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class StandardMT910Convertor implements MessageConvertor<MT910> {

  @Override
  public MT910 convert(MessageContext context) throws Exception {
    MT910 mt910 = new MT910();

    addBlock3(mt910);
    // 设置基本头字段
    mt910.setSender(context.getSender().getBicCode());
    mt910.setReceiver(context.getReceiver().getBicCode());

    Block3Utils.mur(mt910.getSwiftMessage(), context.getMessage().getId());

    // 设置必填字段
    mt910.addField(new Field20(Optional.ofNullable(context.getMessage().getId())
        .map(id -> "RLY" + id.toString().substring(0, 13)).orElse("RLY000000")));
    // 交易参考号 // 交易参考号
    List<MessageDetailsPO> messageDetailsPOList = context.getMessageDetails();

    // 辅助方法来从 messageDetailsPOList 中提取和反序列化字段
    Function<String, Optional<String>> getFieldValue = fieldName ->
        messageDetailsPOList.stream()
            .filter(d -> d.getFieldName().equals(fieldName))
            .map(MessageDetailsPO::getFieldValue)
            .findFirst();

    // 字段 20: Transaction Reference Number
    addField(mt910, Field20.class, getFieldValue.apply(Field20.NAME));

    // 字段 21: Related Reference (可选)
    addField(mt910, Field21.class, getFieldValue.apply(Field21.NAME));

    // 字段 25: Account Identification
    addField(mt910, Field25.class, getFieldValue.apply(Field25.NAME));

    // 字段 32A: Value Date, Currency Code, Amount
    addField(mt910, Field32A.class, getFieldValue.apply(Field32A.NAME));

    // 字段 50K:
    addField(mt910, Field50A.class, getFieldValue.apply(Field50A.NAME));

    // 字段 72: Sender to Receiver Information (可选)
    addField(mt910, Field72.class, getFieldValue.apply(Field72.NAME));

    return mt910;
  }

  private void addBlock3(MT910 mt910) {
    String uuid = UUID.randomUUID().toString();
    mt910.getSwiftMessage().getBlock3().append(new Tag("121", uuid));
  }

  private <T extends Field> void addField(MT910 mt910, Class<T> fieldClass,
      Optional<String> fieldValue) {
    fieldValue.ifPresent(value -> {
      try {
        T field = JSONUtil.parse(value, fieldClass);
        mt910.addField(field);
      } catch (Exception e) {
        log.error("Error parsing field " + fieldClass.getSimpleName() + ": " + value, e);
      }
    });
  }

  @Override
  public String message(MT910 msg) {
    return msg.message();
  }

}
