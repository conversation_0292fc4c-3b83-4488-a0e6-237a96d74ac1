package com.bihu.swifttrack.swift.convertor;

import com.bihu.swifttrack.common.utils.JSONUtil;
import com.bihu.swifttrack.mybatis.repository.po.MessageDetailsPO;
import com.bihu.swifttrack.swift.Block3Utils;
import com.bihu.swifttrack.swift.MessageContext;
import com.bihu.swifttrack.swift.MessageConvertor;
import com.prowidesoftware.swift.model.field.Field;
import com.prowidesoftware.swift.model.field.Field20;
import com.prowidesoftware.swift.model.field.Field25;
import com.prowidesoftware.swift.model.field.Field28C;
import com.prowidesoftware.swift.model.field.Field60F;
import com.prowidesoftware.swift.model.field.Field60M;
import com.prowidesoftware.swift.model.field.Field61;
import com.prowidesoftware.swift.model.field.Field62F;
import com.prowidesoftware.swift.model.field.Field62M;
import com.prowidesoftware.swift.model.field.Field64;
import com.prowidesoftware.swift.model.mt.mt9xx.MT950;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class StandardMT950Convertor implements MessageConvertor<MT950> {

  @Override
  public MT950 convert(MessageContext context) throws Exception {
    MT950 mt950 = new MT950();
    mt950.setSender(context.getSender().getBicCode());
    mt950.setReceiver(context.getReceiver().getBicCode());
    Block3Utils.mur(mt950.getSwiftMessage(), context.getMessage().getId());
    List<MessageDetailsPO> messageDetailsPOList = context.getMessageDetails();

    // 处理单字段
    addField(mt950, Field20.class, getFieldValue(messageDetailsPOList, Field20.NAME));
    addField(mt950, Field25.class, getFieldValue(messageDetailsPOList, Field25.NAME));
    addField(mt950, Field28C.class, getFieldValue(messageDetailsPOList, Field28C.NAME));

    addMutuallyExclusiveField(mt950, Field60F.class, Field60M.class, messageDetailsPOList);
    addListFields(mt950, messageDetailsPOList);
    addMutuallyExclusiveField(mt950, Field62F.class, Field62M.class, messageDetailsPOList);
    addField(mt950, Field64.class, getFieldValue(messageDetailsPOList, Field64.NAME));
    return mt950;
  }

  private void addListFields(MT950 mt950, List<MessageDetailsPO> messageDetailsPOList)
      throws Exception {
    List<Field61> field61Values = getFieldValues(messageDetailsPOList, Field61.class);
    field61Values.forEach(mt950::addField);
  }

  private Optional<String> getFieldValue(List<MessageDetailsPO> list, String fieldName) {
    return list.stream()
        .filter(d -> d.getFieldName().equals(fieldName))
        .map(MessageDetailsPO::getFieldValue)
        .findFirst();
  }

  private <T> List<T> getFieldValues(List<MessageDetailsPO> list, Class<T> fieldClass)
      throws Exception {
    return JSONUtil.parseList(
        getFieldValue(list, fieldClass.getSimpleName().substring(5)).orElse("[]"), fieldClass);
  }

  private <T1 extends Field, T2 extends Field> void addMutuallyExclusiveField(MT950 mt950,
      Class<T1> fieldClass1, Class<T2> fieldClass2, List<MessageDetailsPO> messageDetailsPOList) {

    Optional<String> field1 = getFieldValue(messageDetailsPOList,
        fieldClass1.getSimpleName().substring(5));
    Optional<String> field2 = getFieldValue(messageDetailsPOList,
        fieldClass2.getSimpleName().substring(5));

    if (field1.isPresent()) {
      addField(mt950, fieldClass1, field1);
    } else if (field2.isPresent()) {
      addField(mt950, fieldClass2, field2);
    } else {
      log.error("Neither {} nor {} is present", fieldClass1.getSimpleName(),
          fieldClass2.getSimpleName());
    }
  }

  private <T extends Field> void addField(MT950 mt950, Class<T> fieldClass,
      Optional<String> fieldValue) {
    fieldValue.ifPresent(value -> {
      try {
        T field = JSONUtil.parse(value, fieldClass);
        mt950.addField(field);
      } catch (Exception e) {
        log.error("Error parsing field " + fieldClass.getSimpleName() + ": " + value, e);
      }
    });
  }

  @Override
  public String message(MT950 msg) {
    return msg.message();
  }
}
