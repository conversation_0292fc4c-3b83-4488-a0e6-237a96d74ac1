package com.bihu.swifttrack.swift.convertor;

import com.bihu.swifttrack.swift.MessageContext;
import com.bihu.swifttrack.swift.MessageConvertor;
import com.prowidesoftware.swift.model.mt.mt2xx.MT299;
import com.prowidesoftware.swift.model.mx.MxPacs00900108;
import com.prowidesoftware.swift.model.mx.MxWriteConfiguration;
import com.prowidesoftware.swift.model.mx.dic.CreditTransferTransaction36;
import com.prowidesoftware.swift.model.mx.dic.FinancialInstitutionCreditTransferV08;
import com.prowidesoftware.swift.model.mx.dic.GroupHeader93;
import com.prowidesoftware.swift.model.mx.dic.RemittanceInformation2;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class ISOMT299Convertor implements MessageConvertor<MxPacs00900108> {

  private final StandardMT299Convertor standardMT299Convertor;

  @Override
  public MxPacs00900108 convert(MessageContext context) throws Exception {
    MT299 mt299 = standardMT299Convertor.convert(context);
    // Create the MX message object
    MxPacs00900108 mx = new MxPacs00900108();

    // Create the FinancialInstitutionCreditTransferV08 object
    FinancialInstitutionCreditTransferV08 creditTransfer = new FinancialInstitutionCreditTransferV08();

    // Set the group header
    GroupHeader93 header = new GroupHeader93();
    header.setMsgId(mt299.getField20().getValue());
    OffsetDateTime currentTimestamp = OffsetDateTime.now(ZoneOffset.UTC);
    header.setCreDtTm(currentTimestamp);
    creditTransfer.setGrpHdr(header);

    // Prepare credit transfer information
    CreditTransferTransaction36 cdtTrfTxInf = new CreditTransferTransaction36();

    // Set free text message using RemittanceInformation5
    RemittanceInformation2 remittanceInfo = new RemittanceInformation2();
    remittanceInfo.getUstrd().add(mt299.getField79().getValue());
    cdtTrfTxInf.setRmtInf(remittanceInfo);

    // Add transaction information to credit transfer
    creditTransfer.getCdtTrfTxInf().add(cdtTrfTxInf);

    // Set the FinancialInstitutionCreditTransferV08 object to the MX message
    mx.setFICdtTrf(creditTransfer);

    return mx;
  }

  @Override
  public String message(MxPacs00900108 msg) {
    MxWriteConfiguration conf = new MxWriteConfiguration();
    conf.documentPrefix = null;
    return msg.message(conf);
  }
}
