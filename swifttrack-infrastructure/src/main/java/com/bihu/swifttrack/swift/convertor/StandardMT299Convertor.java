package com.bihu.swifttrack.swift.convertor;

import com.bihu.swifttrack.common.utils.JSONUtil;
import com.bihu.swifttrack.mybatis.repository.po.MessageDetailsPO;
import com.bihu.swifttrack.swift.Block3Utils;
import com.bihu.swifttrack.swift.MessageContext;
import com.bihu.swifttrack.swift.MessageConvertor;
import com.prowidesoftware.swift.model.field.Field20;
import com.prowidesoftware.swift.model.field.Field21;
import com.prowidesoftware.swift.model.field.Field79;
import com.prowidesoftware.swift.model.mt.mt2xx.MT299;
import java.util.Optional;

public class StandardMT299Convertor implements MessageConvertor<MT299> {

  @Override
  public MT299 convert(MessageContext context) throws Exception {
    MT299 mt299 = new MT299();

    // 设置基本头字段
    mt299.setSender(context.getSender().getBicCode());
    mt299.setReceiver(context.getReceiver().getBicCode());
    Block3Utils.mur(mt299.getSwiftMessage(), context.getMessage().getId());

    // 设置必填字段
    mt299.addField(new Field20(Optional.ofNullable(context.getMessage().getId())
        .map(id -> "RLY" + id.toString().substring(0, 13)).orElse("RLY000000"))); // 交易参考号 // 交易参考号

    // 设置基本信息
    for (MessageDetailsPO messageDetail : context.getMessageDetails()) {
      addFieldFromMessageDetail(mt299, messageDetail);
    }

    return mt299;

  }

  private void addFieldFromMessageDetail(MT299 mt299, MessageDetailsPO messageDetail)
      throws Exception {
    if (messageDetail.getFieldName().equals(Field79.NAME)) {
      mt299.addField(JSONUtil.parse(messageDetail.getFieldValue(), Field79.class));
    }
    if (messageDetail.getFieldName().equals(Field21.NAME)) {
      mt299.addField(JSONUtil.parse(messageDetail.getFieldValue(), Field21.class));
    }
  }

  @Override
  public String message(MT299 msg) {
    return msg.message();
  }
}
