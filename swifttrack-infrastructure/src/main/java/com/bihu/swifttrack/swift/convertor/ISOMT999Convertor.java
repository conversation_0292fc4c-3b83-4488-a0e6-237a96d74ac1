package com.bihu.swifttrack.swift.convertor;

import com.bihu.swifttrack.swift.MessageContext;
import com.bihu.swifttrack.swift.MessageConvertor;
import com.prowidesoftware.swift.model.mt.mt9xx.MT999;
import com.prowidesoftware.swift.model.mx.MxCamt02900112;
import com.prowidesoftware.swift.model.mx.MxWriteConfiguration;
import com.prowidesoftware.swift.model.mx.dic.CaseAssignment5;
import com.prowidesoftware.swift.model.mx.dic.ResolutionOfInvestigationV12;
import com.prowidesoftware.swift.model.mx.dic.SupplementaryData1;
import com.prowidesoftware.swift.model.mx.dic.SupplementaryDataEnvelope1;
import jakarta.xml.bind.JAXBElement;
import javax.xml.namespace.QName;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class ISOMT999Convertor implements MessageConvertor<MxCamt02900112> {

  private final StandardMT999Convertor standardMT999Convertor;

  @Override
  public MxCamt02900112 convert(MessageContext context) throws Exception {
    MT999 mt999 = standardMT999Convertor.convert(context);
    // 创建 MxCamt02900109 对象
    MxCamt02900112 mxCamt02900112 = new MxCamt02900112();

    ResolutionOfInvestigationV12 resolutionOfInvestigationV12 = new ResolutionOfInvestigationV12();
    mxCamt02900112.setRsltnOfInvstgtn(resolutionOfInvestigationV12);

    // BusinessAppHdrV02 appHdr = new BusinessAppHdrV02();
    // if (mt999.getField21() != null) {
    // appHdr.setBizMsgIdr(mt999.getField21().getValue()); // 设置 Field21 作为
    // BizMsgIdr
    // }
    // appHdr.setCreDt(OffsetDateTime.now(ZoneOffset.UTC)); // 设置创建时间
    // mxCamt02900112.setAppHdr(appHdr);

    // 创建并填充 ResolutionOfInvestigationV12 对象
    // resolutionOfInvestigationV12.setCase(new
    // Case5().setId(mt999.getField21().getValue()));
    resolutionOfInvestigationV12.setAssgnmt(
        new CaseAssignment5().setId(mt999.getField21().getValue()));

    SupplementaryData1 supplementaryData = new SupplementaryData1();
    SupplementaryDataEnvelope1 envelope = new SupplementaryDataEnvelope1();
    JAXBElement<String> jaxbElement = new JAXBElement<>(
        new QName("urn:iso:std:iso:20022:tech:xsd:camt.029.001.12", "Inf"),
        String.class,
        mt999.getField79().getValue());
    envelope.setAny(jaxbElement); // 设置 Field79 内容作为附加信息
    supplementaryData.setEnvlp(envelope);
    resolutionOfInvestigationV12.getSplmtryData().add(supplementaryData);

    return mxCamt02900112;
  }

  @Override
  public String message(MxCamt02900112 msg) {
    MxWriteConfiguration conf = new MxWriteConfiguration();
    conf.documentPrefix = null;
    return msg.message(conf);
  }

}
