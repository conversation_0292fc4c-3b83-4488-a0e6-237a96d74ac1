package com.bihu.swifttrack.swift.convertor;

import com.bihu.swifttrack.swift.MessageContext;
import com.bihu.swifttrack.swift.MessageConvertor;
import com.prowidesoftware.swift.model.field.Field60F;
import com.prowidesoftware.swift.model.field.Field61;
import com.prowidesoftware.swift.model.field.Field62F;
import com.prowidesoftware.swift.model.mt.mt9xx.MT950;
import com.prowidesoftware.swift.model.mx.MxCamt05300108;
import com.prowidesoftware.swift.model.mx.MxWriteConfiguration;
import com.prowidesoftware.swift.model.mx.dic.AccountStatement9;
import com.prowidesoftware.swift.model.mx.dic.ActiveOrHistoricCurrencyAndAmount;
import com.prowidesoftware.swift.model.mx.dic.BankToCustomerStatementV08;
import com.prowidesoftware.swift.model.mx.dic.BranchAndFinancialInstitutionIdentification6;
import com.prowidesoftware.swift.model.mx.dic.CashBalance8;
import com.prowidesoftware.swift.model.mx.dic.CreditDebitCode;
import com.prowidesoftware.swift.model.mx.dic.DateAndDateTime2Choice;
import com.prowidesoftware.swift.model.mx.dic.EntryDetails9;
import com.prowidesoftware.swift.model.mx.dic.EntryTransaction10;
import com.prowidesoftware.swift.model.mx.dic.FinancialInstitutionIdentification18;
import com.prowidesoftware.swift.model.mx.dic.Party40Choice;
import com.prowidesoftware.swift.model.mx.dic.ReportEntry10;
import com.prowidesoftware.swift.model.mx.dic.TransactionParties6;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class ISOMT950Convertor implements MessageConvertor<MxCamt05300108> {

  private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyMMdd");
  private final StandardMT950Convertor standardMT950Convertor;

  @Override
  public MxCamt05300108 convert(MessageContext context) throws Exception {
    MT950 mt950 = standardMT950Convertor.convert(context);
    MxCamt05300108 mxCamt053 = new MxCamt05300108();
    BankToCustomerStatementV08 bankToCustomerStatement = new BankToCustomerStatementV08();

    AccountStatement9 statement = new AccountStatement9();

    // Field 25: Account Identification
    statement.setId(mt950.getField25().getAccount());

    // Field 28C: Statement Number
    statement.setElctrncSeqNb(new BigDecimal(mt950.getField28C().getStatementNumber()));

    // Field 60F: Opening Balance
    Field60F field60f = mt950.getField60F();
    ActiveOrHistoricCurrencyAndAmount openingBalanceAmount = new ActiveOrHistoricCurrencyAndAmount();
    openingBalanceAmount.setCcy(field60f.getCurrency());
    openingBalanceAmount.setValue(new BigDecimal(field60f.getAmount().replace(",", "")));
    CashBalance8 openingBalanceCash = new CashBalance8();
    openingBalanceCash.setAmt(openingBalanceAmount);
    openingBalanceCash.setCdtDbtInd(CreditDebitCode.CRDT);
    openingBalanceCash.setDt(convertStringToDateAndDateTime2Choice(field60f.getDate()));
    statement.getBal().add(openingBalanceCash);

    // Field 62F: Closing Balance
    Field62F field62f = mt950.getField62F();
    ActiveOrHistoricCurrencyAndAmount closingBalanceAmount = new ActiveOrHistoricCurrencyAndAmount();
    closingBalanceAmount.setCcy(field62f.getCurrency());
    closingBalanceAmount.setValue(new BigDecimal(field62f.getAmount().replace(",", "")));
    CashBalance8 closingBalanceCash = new CashBalance8();
    closingBalanceCash.setAmt(closingBalanceAmount);
    closingBalanceCash.setCdtDbtInd(CreditDebitCode.CRDT);
    closingBalanceCash.setDt(convertStringToDateAndDateTime2Choice(field62f.getDate()));
    statement.getBal().add(closingBalanceCash);

    // Field 61: Statement Line
    for (Field61 field61 : mt950.getField61()) {
      ReportEntry10 entry = new ReportEntry10();
      entry.setAcctSvcrRef(field61.getValue());

      // Related Parties
      TransactionParties6 relatedParties = new TransactionParties6();

      // Debtor Agent
      Party40Choice dbtrAgt = new Party40Choice();
      BranchAndFinancialInstitutionIdentification6 dbtrAgtFin = new BranchAndFinancialInstitutionIdentification6();
      dbtrAgtFin.setFinInstnId(
          new FinancialInstitutionIdentification18().setBICFI(context.getSender().getBicCode()));
      dbtrAgt.setAgt(dbtrAgtFin);
      relatedParties.setDbtr(dbtrAgt);

      // Creditor Agent
      Party40Choice cdtrAgt = new Party40Choice();
      BranchAndFinancialInstitutionIdentification6 cdtrAgtFin = new BranchAndFinancialInstitutionIdentification6();
      cdtrAgtFin.setFinInstnId(
          new FinancialInstitutionIdentification18().setBICFI(context.getReceiver().getBicCode()));
      cdtrAgt.setAgt(cdtrAgtFin);
      relatedParties.setCdtr(cdtrAgt);

      // relatedParties.getCdtr().setNm("[REDACTED] LIMITED");
      // relatedParties.getCdtr().getPstlAdr().setStrtNm("CHINA RESOURCES BUILDING,
      // NO.26 HARBOUR ROAD");
      // relatedParties.getCdtr().getPstlAdr().setTwnNm("WAN CHAI");
      // relatedParties.getCdtr().getPstlAdr().setCtry("HK");

      // Adding Related Parties to Entry Transaction
      EntryTransaction10 entryTransaction = new EntryTransaction10();
      entryTransaction.setRltdPties(relatedParties);

      EntryDetails9 entryDetails = new EntryDetails9();
      entryDetails.addTxDtls(entryTransaction);
      entry.addNtryDtls(entryDetails);

      statement.getNtry().add(entry);
    }

    bankToCustomerStatement.getStmt().add(statement);
    mxCamt053.setBkToCstmrStmt(bankToCustomerStatement);

    return mxCamt053;
  }

  public static DateAndDateTime2Choice convertStringToDateAndDateTime2Choice(String dateStr) {
    // 解析 yyMMdd 格式的字符串
    LocalDate localDate = LocalDate.parse(dateStr, DATE_FORMATTER);

    // 创建 DateAndDateTime2Choice 实例并设置日期
    DateAndDateTime2Choice dateAndDateTime2Choice = new DateAndDateTime2Choice();
    dateAndDateTime2Choice.setDt(localDate);

    return dateAndDateTime2Choice;
  }

  @Override
  public String message(MxCamt05300108 msg) {
    MxWriteConfiguration conf = new MxWriteConfiguration();
    conf.documentPrefix = null;
    return msg.message(conf);
  }

}
