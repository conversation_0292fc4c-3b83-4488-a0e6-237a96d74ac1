package com.bihu.swifttrack.swift;

import com.bihu.swifttrack.common.exception.BusinessException;
import com.bihu.swifttrack.common.exception.ServerCode;
import com.prowidesoftware.swift.model.Tag;
import com.prowidesoftware.swift.model.field.Field;
import com.prowidesoftware.swift.model.mt.AbstractMT;

public interface MessageConvertor<T> {

  T convert(MessageContext context) throws Exception;

  String message(T msg);

  /**
   * 从 Block4 提取 MT 对象中未暴露 getter setter 的 Field
   *
   * @param <T>
   * @param mt
   * @param tagName
   * @return
   */
  @SuppressWarnings("unchecked")
  public static <T extends Field> T getFieldFromMT(AbstractMT mt, String tagName) {
    Tag[] tags = mt.getSwiftMessage().getBlock4().getTagsByName(tagName);
    if (tags != null && tags.length > 0) {
      try {
        return (T) Field.getField(tags[0]);
      } catch (Exception e) {
        throw new BusinessException(ServerCode.INVALID_MESSAGE_CONVERSION);
      }
    }
    return null;
  }
}
