package com.bihu.swifttrack.swift;

import java.util.regex.Pattern;

/**
 * SwiftPatternConverter
 *
 * <p></p>
 *
 * <AUTHOR>
 * @date 2024/08/23
 */
public class SwiftPatternConverter {

  public static String convertToRegex(String swiftPattern) {
    StringBuilder regex = new StringBuilder("^");
    int i = 0;
    while (i < swiftPattern.length()) {
      char c = swiftPattern.charAt(i);
      if (Character.isDigit(c)) {
        int count = 0;
        while (i < swiftPattern.length() && Character.isDigit(swiftPattern.charAt(i))) {
          count = count * 10 + Character.getNumericValue(swiftPattern.charAt(i));
          i++;
        }
        if (i < swiftPattern.length()) {
          regex.append(getCharacterClass(swiftPattern.charAt(i))).append("{").append(count)
              .append("}");
        }
      } else if (c == '*') {
        regex.append(".*");
      } else if (c == '[') {
        int closeBracket = swiftPattern.indexOf(']', i);
        if (closeBracket != -1) {
          regex.append(swiftPattern, i, closeBracket + 1);
          i = closeBracket;
        }
      } else if (c == '(') {
        regex.append("(?:");
      } else if (c == ')') {
        regex.append(")?");
      } else {
        regex.append(getCharacterClass(c));
      }
      i++;
    }
    return regex.append("$").toString();
  }

  private static String getCharacterClass(char c) {
    return switch (c) {
      case 'n' -> "\\d";
      case 'a' -> "[A-Z]";
      case 'c' -> "[A-Za-z0-9]";
      case 'x' -> "[A-Z0-9]";
      case 'z' -> "[\n\r]";
      default -> String.valueOf(c);
    };
  }

  public static boolean validate(String value, String swiftPattern) {
    String regex = convertToRegex(swiftPattern);
    return Pattern.matches(regex, value);
  }

}
