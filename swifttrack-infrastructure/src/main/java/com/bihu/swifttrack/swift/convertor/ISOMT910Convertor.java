package com.bihu.swifttrack.swift.convertor;

import com.bihu.swifttrack.swift.MessageContext;
import com.bihu.swifttrack.swift.MessageConvertor;
import com.prowidesoftware.swift.model.field.Field;
import com.prowidesoftware.swift.model.field.Field20;
import com.prowidesoftware.swift.model.field.Field32A;
import com.prowidesoftware.swift.model.field.Field53A;
import com.prowidesoftware.swift.model.field.Field54A;
import com.prowidesoftware.swift.model.field.Field59A;
import com.prowidesoftware.swift.model.field.Field71A;
import com.prowidesoftware.swift.model.mt.mt9xx.MT910;
import com.prowidesoftware.swift.model.mx.MxPacs00800102;
import com.prowidesoftware.swift.model.mx.MxWriteConfiguration;
import com.prowidesoftware.swift.model.mx.dic.ActiveCurrencyAndAmount;
import com.prowidesoftware.swift.model.mx.dic.BranchAndFinancialInstitutionIdentification4;
import com.prowidesoftware.swift.model.mx.dic.ChargeBearerType1Code;
import com.prowidesoftware.swift.model.mx.dic.CreditTransferTransactionInformation11;
import com.prowidesoftware.swift.model.mx.dic.FIToFICustomerCreditTransferV02;
import com.prowidesoftware.swift.model.mx.dic.FinancialInstitutionIdentification7;
import com.prowidesoftware.swift.model.mx.dic.GroupHeader33;
import com.prowidesoftware.swift.model.mx.dic.PartyIdentification32;
import com.prowidesoftware.swift.model.mx.dic.PaymentIdentification3;
import com.prowidesoftware.swift.model.mx.dic.RemittanceInformation5;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class ISOMT910Convertor implements MessageConvertor<MxPacs00800102> {

  private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyMMdd");
  private final StandardMT910Convertor standardMT910Convertor;

  @Override
  public MxPacs00800102 convert(MessageContext context) throws Exception {
    MT910 mt910 = standardMT910Convertor.convert(context);
    MxPacs00800102 pacs008 = new MxPacs00800102();

    FIToFICustomerCreditTransferV02 creditTransfer = new FIToFICustomerCreditTransferV02();

    // 设置组头信息
    GroupHeader33 grpHdr = new GroupHeader33();
    Field20 field20 = mt910.getField20();
    grpHdr.setMsgId(field20.getValue());
    grpHdr.setCreDtTm(OffsetDateTime.now(ZoneOffset.UTC));
    creditTransfer.setGrpHdr(grpHdr);

    // 创建并添加交易信息
    CreditTransferTransactionInformation11 transaction = createTransactionInformation(mt910);
    creditTransfer.getCdtTrfTxInf().add(transaction);

    pacs008.setFIToFICstmrCdtTrf(creditTransfer);

    return pacs008;
  }

  private CreditTransferTransactionInformation11 createTransactionInformation(MT910 mt910) {
    CreditTransferTransactionInformation11 cdtTrfTxInf = new CreditTransferTransactionInformation11();

    // Field 20: Transaction Reference Number
    PaymentIdentification3 paymentId = new PaymentIdentification3();
    paymentId.setInstrId(mt910.getField20().getValue());
    cdtTrfTxInf.setPmtId(paymentId);

    // Field 32A: Value Date, Currency Code, Amount
    prepareAmountAndSettlementDate(cdtTrfTxInf, mt910.getField32A());

    // Set debtor, creditor and instructed and instructing agent details
    cdtTrfTxInf.setDbtr(preparePartyIdentification(mt910.getField50A()));
    Field59A field59a = MessageConvertor.getFieldFromMT(mt910, "59A");
    cdtTrfTxInf.setCdtr(preparePartyIdentification(field59a));

    // Field 53A: Sender's Correspondent
    BranchAndFinancialInstitutionIdentification4 sendersCorr = new BranchAndFinancialInstitutionIdentification4();
    FinancialInstitutionIdentification7 fiSendersCorr = new FinancialInstitutionIdentification7();
    Field53A field53a = MessageConvertor.getFieldFromMT(mt910, "53A");
    fiSendersCorr.setBIC(field53a.getComponent3());
    sendersCorr.setFinInstnId(fiSendersCorr);
    cdtTrfTxInf.setIntrmyAgt1(sendersCorr);

    // Field 54A: Receiver's Correspondent
    BranchAndFinancialInstitutionIdentification4 receiversCorr = new BranchAndFinancialInstitutionIdentification4();
    FinancialInstitutionIdentification7 fiReceiversCorr = new FinancialInstitutionIdentification7();
    Field54A field54a = MessageConvertor.getFieldFromMT(mt910, "54A");
    fiReceiversCorr.setBIC(field54a.getComponent3());
    receiversCorr.setFinInstnId(fiReceiversCorr);
    cdtTrfTxInf.setIntrmyAgt2(receiversCorr);

    // Field 71A: Details of Charges
    Field71A field71a = MessageConvertor.getFieldFromMT(mt910, "71A");
    cdtTrfTxInf.setChrgBr(ChargeBearerType1Code.fromValue(getBicForCharge(field71a)));

    // Field 72: Sender to Receiver Information
    RemittanceInformation5 remittanceInfo = new RemittanceInformation5();
    remittanceInfo.getUstrd().add(mt910.getField72().getValue());
    cdtTrfTxInf.setRmtInf(remittanceInfo);

    return cdtTrfTxInf;
  }

  private PartyIdentification32 preparePartyIdentification(Field field) {
    if (field != null) {
      PartyIdentification32 partyId = new PartyIdentification32();
      partyId.setNm(field.getComponent(2));
      return partyId;
    }
    return null;
  }

  private void prepareAmountAndSettlementDate(
      CreditTransferTransactionInformation11 cdtTrfTxInf, Field32A field32A) {
    if (field32A != null) {
      ActiveCurrencyAndAmount amount = new ActiveCurrencyAndAmount();
      amount.setCcy(field32A.getCurrency());
      amount.setValue(new BigDecimal(field32A.getAmount()));

      cdtTrfTxInf.setIntrBkSttlmAmt(amount);

      LocalDate valueDate = LocalDate.parse(field32A.getDate(), DATE_FORMATTER);
      cdtTrfTxInf.setIntrBkSttlmDt(valueDate);
    }
  }

  private String getBicForCharge(Field71A field71A) {
    return switch (field71A.getValue()) {
      case "SHA" -> "SLEV"; // Shared
      case "BEN" -> "SHAR"; // Shared
      case "OUR" -> "DEBT"; // Our
      default -> "SLEV"; // Default to shared if unknown
    };
  }

  @Override
  public String message(MxPacs00800102 msg) {
    MxWriteConfiguration conf = new MxWriteConfiguration();
    conf.documentPrefix = null;
    return msg.message(conf);
  }
}
