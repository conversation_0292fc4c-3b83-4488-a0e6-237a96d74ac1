package com.bihu.swifttrack.swift.convertor;

import com.bihu.swifttrack.common.utils.JSONUtil;
import com.bihu.swifttrack.mybatis.repository.po.MessageDetailsPO;
import com.bihu.swifttrack.swift.Block3Utils;
import com.bihu.swifttrack.swift.MessageContext;
import com.bihu.swifttrack.swift.MessageConvertor;
import com.prowidesoftware.swift.model.field.Field20;
import com.prowidesoftware.swift.model.field.Field21;
import com.prowidesoftware.swift.model.field.Field79;
import com.prowidesoftware.swift.model.mt.mt9xx.MT999;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class StandardMT999Convertor implements MessageConvertor<MT999> {

  @Override
  public MT999 convert(MessageContext context) throws Exception {
    MT999 mt999 = new MT999();
    // 设置基本头字段
    mt999.setSender(context.getSender().getBicCode());
    mt999.setReceiver(context.getReceiver().getBicCode());
    Block3Utils.mur(mt999.getSwiftMessage(), context.getMessage().getId());

    // 设置必填字段
    mt999.addField(new Field20(Optional.ofNullable(context.getMessage().getId())
        .map(id -> "RLY" + id.toString().substring(0, 13)).orElse("RLY000000"))); // 交易参考号 // 交易参考号
    for (MessageDetailsPO messageDetail : context.getMessageDetails()) {
      switch (messageDetail.getFieldName()) {
        case Field21.NAME ->
            mt999.addField(JSONUtil.parse(messageDetail.getFieldValue(), Field21.class));
        case Field79.NAME ->
            mt999.addField(JSONUtil.parse(messageDetail.getFieldValue(), Field79.class));
        default -> {
        }
      }
    }

    return mt999;
  }

  @Override
  public String message(MT999 msg) {
    log.debug("===MT999===" + msg.message());
    return msg.message();
  }
}
