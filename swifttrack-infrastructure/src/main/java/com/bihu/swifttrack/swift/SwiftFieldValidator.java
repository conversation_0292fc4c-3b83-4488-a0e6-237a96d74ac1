package com.bihu.swifttrack.swift;


import java.util.regex.Pattern;

/**
 * SwiftFieldValidator
 *
 * <p></p>
 *
 * <AUTHOR>
 * @date 2024/09/04
 */
public class SwiftFieldValidator {

  // Field20: 16x，开头结尾不能是斜杠，中间不能有连续斜杠
  private static final Pattern FIELD20_PATTERN = Pattern.compile(
      "^[A-Z0-9](([A-Z0-9]/)?[A-Z0-9]){0,14}[A-Z0-9]$");

  // Field25: 35x
  private static final Pattern FIELD25_PATTERN = Pattern.compile("^.{1,35}$");

  // Field28C: 5n[/5n]
  private static final Pattern FIELD28C_PATTERN = Pattern.compile("^\\d{1,5}(/\\d{1,5})?$");

  private static final Pattern BALANCE_PATTERN = Pattern.compile(
      "^[DC]\\d{6}[A-Z]{3}\\d{1,15},(\\d{1,2})?$");

  public static void validateBalance(String value) throws IllegalArgumentException {
    if (value == null || value.isEmpty()) {
      throw new IllegalArgumentException("Balance is required");
    }
    if (!BALANCE_PATTERN.matcher(value).matches()) {
      throw new IllegalArgumentException(
          "Balance format is invalid: " + value + ". It should be in the format 1!a6!n3!a15d");
    }
  }

  public static void validateField20(String value) throws IllegalArgumentException {
    validateField("Field20", value, FIELD20_PATTERN, true);
  }

  public static void validateField25(String value) throws IllegalArgumentException {
    validateField("Field25", value, FIELD25_PATTERN, true);
  }

  public static void validateField28C(String value) throws IllegalArgumentException {
    validateField("Field28C", value, FIELD28C_PATTERN, true);
  }

  public static void validateField(String fieldName, String value, Pattern pattern,
      boolean isRequired) throws IllegalArgumentException {
    if (isRequired && (value == null || value.isEmpty())) {
      throw new IllegalArgumentException(fieldName + " is required");
    }
    if (value != null && !value.isEmpty() && !pattern.matcher(value).matches()) {
      throw new IllegalArgumentException(fieldName + " format is invalid: " + value);
    }
  }


}
