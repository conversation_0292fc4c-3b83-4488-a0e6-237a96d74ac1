package com.bihu.swifttrack.swift.convertor;

import com.bihu.swifttrack.common.enums.MessagingDetailFormField;
import com.bihu.swifttrack.common.utils.JSONUtil;
import com.bihu.swifttrack.dto.BaseInfo;
import com.bihu.swifttrack.mybatis.repository.po.MessageDetailsPO;
import com.bihu.swifttrack.swift.Block3Utils;
import com.bihu.swifttrack.swift.MessageContext;
import com.bihu.swifttrack.swift.MessageConvertor;
import com.bihu.swifttrack.swift.fields.FieldGenerator;
import com.bihu.swifttrack.swift.fields.FieldGeneratorFactory;
import com.prowidesoftware.swift.model.field.Field20;
import com.prowidesoftware.swift.model.field.Field21;
import com.prowidesoftware.swift.model.field.Field32A;
import com.prowidesoftware.swift.model.field.Field52A;
import com.prowidesoftware.swift.model.field.Field53A;
import com.prowidesoftware.swift.model.field.Field53B;
import com.prowidesoftware.swift.model.field.Field54A;
import com.prowidesoftware.swift.model.field.Field56A;
import com.prowidesoftware.swift.model.field.Field57A;
import com.prowidesoftware.swift.model.field.Field58A;
import com.prowidesoftware.swift.model.field.Field58D;
import com.prowidesoftware.swift.model.field.Field72;
import com.prowidesoftware.swift.model.mt.mt2xx.MT202;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class StandardMT202Convertor implements MessageConvertor<MT202> {

  @Override
  public MT202 convert(MessageContext context) throws Exception {
    MT202 mt202 = new MT202();

    List<MessageDetailsPO> messageDetailsPOList = context.getMessageDetails();
    Function<String, Optional<String>> getFieldValue = fieldName ->
        messageDetailsPOList.stream()
            .filter(d -> d.getFieldName().equals(fieldName))
            .map(MessageDetailsPO::getFieldValue)
            .findFirst();
    // 设置基本头字段
    mt202.setSender(context.getSender().getBicCode());
    mt202.setReceiver(context.getReceiver().getBicCode());

    String defaultRef = "RLY" + (context.getMessage().getId() != null ? ("" + context.getMessage()
        .getId()).substring(0, 13)
        : "000000");
    String relatedRef = null;
    String[] ref = new String[]{defaultRef, relatedRef};
    getFieldValue.apply(MessagingDetailFormField.BASE_INFO.getFieldName()).ifPresent(v -> {
      try {
        BaseInfo baseInfo = JSONUtil.parse(v, BaseInfo.class);
        mt202.setSender(baseInfo.getSender());
        mt202.setReceiver(baseInfo.getReceiver());
        if (StringUtils.isNotBlank(baseInfo.getSenderRef())) {
          ref[0] = baseInfo.getSenderRef();
        }
        if (StringUtils.isNotBlank(baseInfo.getRelatedRef())) {
          ref[1] = baseInfo.getRelatedRef();
        }
      } catch (Exception e) {
        log.error("Error parsing baseInfo: " + v, e);
      }
    });

    Block3Utils.mur(mt202.getSwiftMessage(), context.getMessage().getId());

    FieldGenerator<Field20> field20Generator = FieldGeneratorFactory.getGenerator(Field20.NAME);

    mt202.addField(field20Generator.generateField(ref[0]));

    if (StringUtils.isNotBlank(ref[1])) {
      mt202.addField(new Field21(ref[1]));
    }


    for (MessageDetailsPO detail : context.getMessageDetails()) {
      if (!Pattern.matches("^\\d{2,3}[A-Z]?$", detail.getFieldName())) {
        continue;
      }
      switch (detail.getFieldName()) {
        case Field32A.NAME ->
            mt202.addField(JSONUtil.parse(detail.getFieldValue(), Field32A.class));
//        case Field33B.NAME ->
//            mt202.addField(JSONUtil.parse(detail.getFieldValue(), Field33B.class));
        case Field52A.NAME ->
            mt202.addField(JSONUtil.parse(detail.getFieldValue(), Field52A.class));
        case Field53A.NAME ->
            mt202.addField(JSONUtil.parse(detail.getFieldValue(), Field53A.class));
        case Field53B.NAME ->
            mt202.addField(JSONUtil.parse(detail.getFieldValue(), Field53B.class));
        case Field54A.NAME ->
            mt202.addField(JSONUtil.parse(detail.getFieldValue(), Field54A.class));
        case Field56A.NAME ->
            mt202.addField(JSONUtil.parse(detail.getFieldValue(), Field56A.class));

        case Field57A.NAME ->
            mt202.addField(JSONUtil.parse(detail.getFieldValue(), Field57A.class));
        case Field58A.NAME ->
            mt202.addField(JSONUtil.parse(detail.getFieldValue(), Field58A.class));
        case Field58D.NAME ->
            mt202.addField(JSONUtil.parse(detail.getFieldValue(), Field58D.class));
        case Field72.NAME -> mt202.addField(JSONUtil.parse(detail.getFieldValue(), Field72.class));
        default -> {
        }
      }
    }

    return mt202;
  }


  @Override
  public String message(MT202 msg) {
    return msg.message();
  }
}
