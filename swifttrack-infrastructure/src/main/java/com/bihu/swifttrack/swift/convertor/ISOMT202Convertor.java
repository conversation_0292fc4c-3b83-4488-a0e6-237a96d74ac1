package com.bihu.swifttrack.swift.convertor;

import com.bihu.swifttrack.swift.MessageContext;
import com.bihu.swifttrack.swift.MessageConvertor;
import com.prowidesoftware.swift.model.field.Field;
import com.prowidesoftware.swift.model.field.Field20;
import com.prowidesoftware.swift.model.field.Field32A;
import com.prowidesoftware.swift.model.field.Field72;
import com.prowidesoftware.swift.model.mt.mt2xx.MT202;
import com.prowidesoftware.swift.model.mx.MxPacs00900108;
import com.prowidesoftware.swift.model.mx.MxWriteConfiguration;
import com.prowidesoftware.swift.model.mx.dic.ActiveCurrencyAndAmount;
import com.prowidesoftware.swift.model.mx.dic.BranchAndFinancialInstitutionIdentification6;
import com.prowidesoftware.swift.model.mx.dic.CreditTransferTransaction36;
import com.prowidesoftware.swift.model.mx.dic.FinancialInstitutionCreditTransferV08;
import com.prowidesoftware.swift.model.mx.dic.FinancialInstitutionIdentification18;
import com.prowidesoftware.swift.model.mx.dic.GroupHeader93;
import com.prowidesoftware.swift.model.mx.dic.RemittanceInformation2;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class ISOMT202Convertor implements MessageConvertor<MxPacs00900108> {

  private final StandardMT202Convertor standardMT202Convertor;

  @Override
  public MxPacs00900108 convert(MessageContext context) throws Exception {
    MT202 mt202 = standardMT202Convertor.convert(context);
    MxPacs00900108 mx = new MxPacs00900108();
    FinancialInstitutionCreditTransferV08 creditTransfer = createCreditTransfer(mt202);

    CreditTransferTransaction36 cdtTrfTxInf = new CreditTransferTransaction36();

    cdtTrfTxInf.setInstgAgt(setFinancialInstitutionIdentification(mt202.getField52A()));
    cdtTrfTxInf.setDbtrAgt(setFinancialInstitutionIdentification(mt202.getField53A()));
    cdtTrfTxInf.setCdtrAgt(setFinancialInstitutionIdentification(mt202.getField57A()));

    setAmountDate(cdtTrfTxInf, mt202.getField32A());
    setRemittanceInfo(cdtTrfTxInf, mt202.getField72());

    creditTransfer.getCdtTrfTxInf().add(cdtTrfTxInf);
    mx.setFICdtTrf(creditTransfer);
    return mx;
  }


  private FinancialInstitutionCreditTransferV08 createCreditTransfer(MT202 mt202) {
    FinancialInstitutionCreditTransferV08 creditTransfer = new FinancialInstitutionCreditTransferV08();
    GroupHeader93 header = new GroupHeader93();
    Field20 field20 = mt202.getField20();
    if (field20 != null) {
      header.setMsgId(field20.getValue());
    }
    OffsetDateTime currentTimestamp = OffsetDateTime.now(ZoneOffset.UTC);
    header.setCreDtTm(currentTimestamp);
    creditTransfer.setGrpHdr(header);
    return creditTransfer;
  }

  private void setAmountDate(CreditTransferTransaction36 cdtTrfTxInf, Field32A field32A) {
    if (field32A != null) {
      ActiveCurrencyAndAmount amount = new ActiveCurrencyAndAmount();
      amount.setCcy(field32A.getCurrency());
      amount.setValue(new BigDecimal(field32A.getAmount()));
      cdtTrfTxInf.setIntrBkSttlmAmt(amount);
      LocalDate valueDate = LocalDate.parse(field32A.getDate(),
          DateTimeFormatter.ofPattern("yyMMdd"));
      cdtTrfTxInf.setIntrBkSttlmDt(valueDate);
    }
  }

  private void setRemittanceInfo(CreditTransferTransaction36 cdtTrfTxInf, Field72 field72) {
    if (field72 != null) {
      RemittanceInformation2 remittanceInformation = new RemittanceInformation2();
      remittanceInformation.getUstrd().add(field72.getValue());
      cdtTrfTxInf.setRmtInf(remittanceInformation);
    }
  }

  private BranchAndFinancialInstitutionIdentification6 setFinancialInstitutionIdentification(
      Field field) {
    if (field != null) {
      BranchAndFinancialInstitutionIdentification6 instgAgt = new BranchAndFinancialInstitutionIdentification6();
      FinancialInstitutionIdentification18 finInstId = new FinancialInstitutionIdentification18();
      finInstId.setBICFI(field.getValue());
      instgAgt.setFinInstnId(finInstId);
      return instgAgt;
    }
    return null;
  }

  @Override
  public String message(MxPacs00900108 msg) {
    MxWriteConfiguration conf = new MxWriteConfiguration();
    conf.documentPrefix = null;
    return msg.message(conf);
  }
}
