package com.bihu.swifttrack.swift.convertor;

import com.bihu.swifttrack.common.utils.JSONUtil;
import com.bihu.swifttrack.mybatis.repository.po.MessageDetailsPO;
import com.bihu.swifttrack.swift.Block3Utils;
import com.bihu.swifttrack.swift.MessageContext;
import com.bihu.swifttrack.swift.MessageConvertor;
import com.prowidesoftware.swift.model.field.Field20;
import com.prowidesoftware.swift.model.field.Field21;
import com.prowidesoftware.swift.model.field.Field79;
import com.prowidesoftware.swift.model.mt.mt1xx.MT199;
import java.util.Optional;

public class StandardMT199Convertor implements MessageConvertor<MT199> {

  @Override
  public MT199 convert(MessageContext context) throws Exception {
    MT199 mt199 = new MT199();
    // 设置基本头字段
    mt199.setSender(context.getSender().getBicCode());
    mt199.setReceiver(context.getReceiver().getBicCode());
    Block3Utils.mur(mt199.getSwiftMessage(), context.getMessage().getId());
    // 设置必填字段
    mt199.addField(new Field20(Optional.ofNullable(context.getMessage().getId())
        .map(id -> "RLY" + id.toString().substring(0, 13)).orElse("RLY000000"))); // 交易参考号

    // 设置基本信息
    for (MessageDetailsPO messageDetail : context.getMessageDetails()) {
      addFieldFromMessageDetail(mt199, messageDetail);
    }
    return mt199;
  }

  private void addFieldFromMessageDetail(MT199 mt199, MessageDetailsPO messageDetail)
      throws Exception {
    if (messageDetail.getFieldName().equals(Field79.NAME)) {
      mt199.addField(JSONUtil.parse(messageDetail.getFieldValue(), Field79.class));
    }
    if (messageDetail.getFieldName().equals(Field21.NAME)) {
      mt199.addField(JSONUtil.parse(messageDetail.getFieldValue(), Field21.class));
    }
  }

  @Override
  public String message(MT199 msg) {
    return msg.message();
  }
}
