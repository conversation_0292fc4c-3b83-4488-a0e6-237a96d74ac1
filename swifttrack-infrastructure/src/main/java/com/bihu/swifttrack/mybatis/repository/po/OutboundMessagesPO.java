package com.bihu.swifttrack.mybatis.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-15
 */
@Getter
@Setter
@TableName("outbound_messages")
@Builder
public class OutboundMessagesPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * ID
     */
    private Long messageId;

    /**
     * BIC
     */
    private String receiverBic;

    private String messageContent;

    private String messageFormat;

    private String endpointType;

    private String status;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;
}
