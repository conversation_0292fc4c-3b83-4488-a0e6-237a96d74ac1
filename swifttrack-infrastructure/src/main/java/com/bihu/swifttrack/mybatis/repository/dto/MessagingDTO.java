/**
 * <AUTHOR>
 * @date 2024/06/07
 */
package com.bihu.swifttrack.mybatis.repository.dto;

import com.bihu.swifttrack.common.enums.MessageFormat;
import com.bihu.swifttrack.mybatis.repository.po.MessageDetailsPO;
import com.bihu.swifttrack.mybatis.repository.po.MessagesPO;
import com.bihu.swifttrack.mybatis.repository.po.ReceiverPO;
import com.bihu.swifttrack.mybatis.repository.po.SenderPO;
import com.bihu.swifttrack.swift.SwiftMessageConvertor;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Data;

@Data
public class MessagingDTO {

  /**
   * 主键
   */
  private Long id;

  /**
   * 报文状态，支持16个状态
   */
  private Integer status;

  /**
   * 方向，表示汇入还是汇出
   */
  private Integer direction;

  /**
   * 创建时间
   */
  private LocalDateTime createdAt;

  /**
   * 报文类型
   */
  private String messageType;

  /**
   * 关联报文 id
   */
  private Long preMessageId;

  // 发送方信息
  /**
   * 付款方银行
   */
  private String payerBank;

  /**
   * 付款方名称
   */
  private String payerName;

  /**
   * 付款方 bic_code
   */
  private String payerBicCode;

  /**
   * 付款方账号
   */
  private String payerAccount;

  /**
   * 付款方地址
   */
  private String payerAddress;

  /**
   * 付款方国家
   */
  private String payerCountry;

  // 收款方信息
  /**
   * 收款方银行
   */
  private String payeeBank;

  /**
   * 收款方名称
   */
  private String payeeName;

  /**
   * 收款方 bic_code
   */
  private String payeeBicCode;

  /**
   * 收款方账号
   */
  private String payeeAccount;

  /**
   * 收款方地址
   */
  private String payeeAddress;

  /**
   * 收款方国家
   */
  private String payeeCountry;

  /**
   * 流程认领状态
   */
  private Integer claimStatus;

  /**
   * 报文详情数据
   */
  private List<MessagingDetailDTO> messagingDetails;

  public SenderPO toSender() {
    SenderPO senderPO = new SenderPO();
    senderPO.setAccount(payerAccount).setAddress(payerAddress).setBicCode(payerBicCode)
        .setCountry(payerCountry)
        .setName(payerName).setInstitution(payerBank);
    return senderPO;
  }

  public ReceiverPO toReceiver() {
    ReceiverPO receiverPO = new ReceiverPO();
    receiverPO.setAccount(payeeAccount).setAddress(payeeAddress).setBicCode(payeeBicCode)
        .setCountry(payeeCountry)
        .setName(payeeName).setInstitution(payeeBank);
    return receiverPO;
  }

  public MessagesPO toMessage() {
    MessagesPO messagesPO = new MessagesPO();
    messagesPO.setId(id).setStatus(status).setDirection(direction).setPreMessageId(preMessageId)
        .setMessageType(messageType);
    return messagesPO;
  }

  public List<MessageDetailsPO> toMessageDetails() {
    List<MessageDetailsPO> detailList = messagingDetails.stream().map(detail -> {
      MessageDetailsPO messageDetailsPO = new MessageDetailsPO();
      messageDetailsPO.setFieldName(detail.getFieldName());
      messageDetailsPO.setFieldValue(detail.getFieldValue());
      return messageDetailsPO;
    }).collect(Collectors.toList());
    return detailList;
  }

  public String toStandardContent(MessageFormat format) throws Exception {
    ReceiverPO receiverPO = toReceiver();
    SenderPO senderPO = toSender();
    MessagesPO messagesPO = toMessage();
    List<MessageDetailsPO> detailList = toMessageDetails();
    return SwiftMessageConvertor.getStandardFormat(format, messagesPO, senderPO, receiverPO,
        detailList);
  }
}
