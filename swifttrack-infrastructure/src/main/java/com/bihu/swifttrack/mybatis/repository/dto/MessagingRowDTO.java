/**
 * <AUTHOR>
 * @date 2024/06/07
 */
package com.bihu.swifttrack.mybatis.repository.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

@Data
public class MessagingRowDTO {

  /**
   * 主键
   */
  private Long id;

  /**
   * 报文状态，支持16个状态
   */
  private Integer status;

  /**
   * 报文类型
   */
  private String messageType;

  /**
   * 方向，表示汇入还是汇出
   */
  private Integer direction;

  /**
   * 交易数额
   */
  private BigDecimal amount;

  /**
   * 创建时间
   */
  private LocalDateTime createdAt;

  // 发送方信息
  /**
   * 付款方银行
   */
  private String payerBank;

  /**
   * 付款方名称
   */
  private String payerName;

  /**
   * 付款方 bic_code
   */
  private String payerBicCode;

  /**
   * 付款方账号
   */
  private String payerAccount;

  /**
   * 付款方地址
   */
  private String payerAddress;

  /**
   * 付款方国家
   */
  private String payerCountry;

  // 收款方信息
  /**
   * 收款方银行
   */
  private String payeeBank;

  /**
   * 收款方名称
   */
  private String payeeName;

  /**
   * 收款方 bic_code
   */
  private String payeeBicCode;

  /**
   * 收款方账号
   */
  private String payeeAccount;

  /**
   * 收款方地址
   */
  private String payeeAddress;

  /**
   * 收款方国家
   */
  private String payeeCountry;

  // 报文详情中包含的信息，如交易信息等
}
