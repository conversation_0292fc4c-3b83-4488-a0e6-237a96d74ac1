package com.bihu.swifttrack.mybatis.repository.impl;

import com.bihu.swifttrack.mybatis.repository.po.OutboundMessagesPO;
import com.bihu.swifttrack.mybatis.repository.dao.OutboundMessagesMapper;
import com.bihu.swifttrack.mybatis.repository.OutboundMessagesRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-15
 */
@Service
public class OutboundMessagesRepositoryImpl extends ServiceImpl<OutboundMessagesMapper, OutboundMessagesPO> implements OutboundMessagesRepository {

}
