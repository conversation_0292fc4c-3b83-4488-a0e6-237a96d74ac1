package com.bihu.swifttrack.mybatis.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bihu.swifttrack.common.enums.SystemRoleEnum;
import com.bihu.swifttrack.dto.UserInfoDTO;
import com.bihu.swifttrack.mybatis.repository.UserRoleRepository;
import com.bihu.swifttrack.mybatis.repository.dao.UserRoleMapper;
import com.bihu.swifttrack.mybatis.repository.dto.UserRoleDTO;
import com.bihu.swifttrack.mybatis.repository.po.UserRolePO;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户角色关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-07
 */
@Service
public class UserRoleRepositoryImpl extends ServiceImpl<UserRoleMapper, UserRolePO> implements
    UserRoleRepository {

  @Override
  public UserInfoDTO getUserRoles(String loginName) {
    return this.baseMapper.selectUserRoles(loginName);
  }

  @Override
  public List<UserRoleDTO> listRoles(Long userId) {
    return baseMapper.listRoles(userId);
  }

  @Override
  public boolean userExistRole(Long userId, SystemRoleEnum role) {
    return baseMapper.userExistRole(userId, role.getRoleName()) > 0;
  }
}
