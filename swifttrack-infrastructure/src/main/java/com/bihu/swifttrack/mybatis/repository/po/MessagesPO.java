package com.bihu.swifttrack.mybatis.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 存储所有报文的公共字段和类型
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("messages")
public class MessagesPO implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * 主键
   */
  private Long id;

  /**
   * 报文类型（如MT103）
   */
  private String messageType;

  /**
   * 报文状态，支持16个状态
   */
  private Integer status;

  /**
   * 方向，表示汇入还是汇出
   */
  private Integer direction;

  /**
   * 交易金额
   */
  private BigDecimal amount;

  /**
   * 指派的审批人
   */
  private Long assignee;

  /**
   * 创建人
   */
  private Long createdBy;

  /**
   * 更新人
   */
  private Long updatedBy;

  /**
   * 前续报文
   */
  private Long preMessageId;

  /**
   * 创建时间
   */
  private LocalDateTime createdAt;

  /**
   * 更新时间
   */
  private LocalDateTime updatedAt;

  /**
   * 是否被删除
   */
  private Integer isDeleted;

  /**
   * 删除时间
   */
  private LocalDateTime deletedAt;

  /**
   * 删除人
   */
  private Long deletedBy;

  /**
   * 扩展信息
   */
  private String extraInfo;

}
