package com.bihu.swifttrack.mybatis.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bihu.swifttrack.common.enums.SystemRoleEnum;
import com.bihu.swifttrack.dto.UserInfoDTO;
import com.bihu.swifttrack.mybatis.repository.dto.UserRoleDTO;
import com.bihu.swifttrack.mybatis.repository.po.UserRolePO;
import java.util.List;

/**
 * <p>
 * 用户角色关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-07
 */
public interface UserRoleRepository extends IService<UserRolePO> {

  UserInfoDTO getUserRoles(String loginName);

  List<UserRoleDTO> listRoles(Long userId);

  /**
   * 判断是否用户存在对应的角色
   *
   * @param userId
   * @param role
   * @return
   */
  boolean userExistRole(Long userId, SystemRoleEnum role);
}
