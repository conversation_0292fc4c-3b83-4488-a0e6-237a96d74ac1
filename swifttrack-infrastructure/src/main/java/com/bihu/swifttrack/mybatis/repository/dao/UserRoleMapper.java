package com.bihu.swifttrack.mybatis.repository.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bihu.swifttrack.dto.UserInfoDTO;
import com.bihu.swifttrack.mybatis.repository.dto.UserRoleDTO;
import com.bihu.swifttrack.mybatis.repository.po.UserRolePO;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 用户角色关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-07
 */
public interface UserRoleMapper extends BaseMapper<UserRolePO> {

  @Select(
      "SELECT u.user_id, u.name as username,u.email, u.phone, u.password, u.organization_id, u.verify_method, u.organization_id, u.is_deleted, u.extra_info as secretKey, GROUP_CONCAT(r.name ORDER BY r.name SEPARATOR ',') as roles "
          +
          "FROM user u " +
          "JOIN user_role ur ON u.user_id = ur.user_id " +
          "JOIN role r ON ur.role_id = r.id " +
          "WHERE u.phone = #{loginName} OR u.email = #{loginName} OR (u.name = #{loginName} AND u.verify_method IS NULL) "
          +
          "GROUP BY u.user_id")
  UserInfoDTO selectUserRoles(@Param("loginName") String loginName);

  @Select("SELECT r.name FROM user_role ur LEFT JOIN role r ON ur.role_id = r.id WHERE ur.user_id = #{userId}")
  List<UserRoleDTO> listRoles(@Param("userId") Long userId);

  @Select("SELECT count(*) FROM user_role ur LEFT JOIN role r on ur.role_id = r.id WHERE ur.user_id = #{userId} AND r.name = #{roleName}")
  Integer userExistRole(@Param("userId") Long userId, @Param("roleName") String roleName);
}
