spring:
  profiles:
    active: test
  
  # 数据库配置 - 使用内存数据库进行测试
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
    
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        format_sql: false
        
  # Redis配置 - 使用嵌入式Redis进行测试
  redis:
    host: localhost
    port: 6379
    timeout: 2000ms
    database: 1
    
  # 缓存配置
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=100,expireAfterWrite=10m

# MyBatis配置
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*.xml
  type-aliases-package: com.bihu.swifttrack.mybatis.repository.po
  configuration:
    cache-enabled: false
    lazy-loading-enabled: false
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl

# 消息路由配置
message:
  routing:
    endpoints:
      - bicPattern: "GFIN*"
        type: "GFIN"
        url: "http://test-gfin.example.com/api/send"
        timeoutMs: 5000
      - bicPattern: "BTCBCNBJ*"
        type: "GFIN"
        url: "http://test-btcb.example.com/api/send"
        timeoutMs: 3000
      - bicPattern: "*"
        type: "DATABASE"
        tableName: "outbound_messages"

# 日志配置
logging:
  level:
    com.bihu.swifttrack: DEBUG
    org.springframework.cache: DEBUG
    org.mybatis: ERROR
    com.zaxxer.hikari: WARN
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 测试专用配置
test:
  # 禁用某些自动配置
  auto-configuration:
    exclude:
      - org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration
      
  # 模拟外部服务
  mock:
    gfin:
      enabled: true
      delay: 10ms
    database:
      enabled: true
      delay: 5ms 