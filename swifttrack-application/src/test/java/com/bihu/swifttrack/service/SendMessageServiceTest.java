package com.bihu.swifttrack.service;

import com.bihu.swifttrack.common.exception.BusinessException;
import com.bihu.swifttrack.common.exception.ServerCode;
import com.bihu.swifttrack.config.EndpointConfig;
import com.bihu.swifttrack.context.MessageSendContext;
import com.bihu.swifttrack.context.MessageSendContextBuilder;
import com.bihu.swifttrack.service.impl.SendMessageServiceImpl;
import com.bihu.swifttrack.service.strategy.MessageSendException;
import com.bihu.swifttrack.service.strategy.MessageSendStrategy;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * SendMessageService单元测试
 *
 * <AUTHOR>
 * @date 2024/12/20
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("SendMessageService单元测试")
class SendMessageServiceTest {

    @Mock
    private MessageSendStrategy mockStrategy1;
    
    @Mock
    private MessageSendStrategy mockStrategy2;
    
    @Mock
    private MessageSendContextBuilder mockContextBuilder;
    
    private SendMessageService sendMessageService;
    
    private List<MessageSendStrategy> strategies;

    @BeforeEach
    void setUp() {
        strategies = Arrays.asList(mockStrategy1, mockStrategy2);
        sendMessageService = new SendMessageServiceImpl(strategies, mockContextBuilder);
    }

    @Test
    @DisplayName("正常发送消息 - 成功场景")
    void sendMessage_Success() {
        // Given
        Long messageId = 12345L;
        MessageSendContext context = createMockContext(messageId, "BTCBCNBJ001");
        
        when(mockContextBuilder.buildContext(messageId)).thenReturn(context);
        when(mockStrategy1.canHandle(context)).thenReturn(true);
        when(mockStrategy1.getPriority()).thenReturn(1);
        when(mockStrategy1.getStrategyName()).thenReturn("TestStrategy1");
        when(mockStrategy2.canHandle(context)).thenReturn(false);
        
        // When & Then
        assertDoesNotThrow(() -> sendMessageService.sendMessage(messageId));
        
        verify(mockContextBuilder).buildContext(messageId);
        verify(mockStrategy1).canHandle(context);
        verify(mockStrategy1).sendMessage(context);
        verify(mockStrategy2).canHandle(context);
        verify(mockStrategy2, never()).sendMessage(any());
    }

    @Test
    @DisplayName("选择优先级最高的策略")
    void sendMessage_SelectHighestPriorityStrategy() {
        // Given
        Long messageId = 12345L;
        MessageSendContext context = createMockContext(messageId, "BTCBCNBJ001");
        
        when(mockContextBuilder.buildContext(messageId)).thenReturn(context);
        when(mockStrategy1.canHandle(context)).thenReturn(true);
        when(mockStrategy1.getPriority()).thenReturn(2);
        when(mockStrategy1.getStrategyName()).thenReturn("TestStrategy1");
        when(mockStrategy2.canHandle(context)).thenReturn(true);
        when(mockStrategy2.getPriority()).thenReturn(1); // 更高优先级
        when(mockStrategy2.getStrategyName()).thenReturn("TestStrategy2");
        
        // When
        sendMessageService.sendMessage(messageId);
        
        // Then
        verify(mockStrategy2).sendMessage(context); // 应该选择优先级更高的策略
        verify(mockStrategy1, never()).sendMessage(any());
    }

    @Test
    @DisplayName("参数验证失败 - messageId为null")
    void sendMessage_NullMessageId() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> sendMessageService.sendMessage(null));
        
        assertEquals(ServerCode.INPUT_ERROR, exception.getServerCode());
        assertTrue(exception.getDesc().contains("参数验证失败"));
        
        verify(mockContextBuilder, never()).buildContext(any());
    }

    @Test
    @DisplayName("参数验证失败 - messageId为负数")
    void sendMessage_NegativeMessageId() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> sendMessageService.sendMessage(-1L));
        
        assertEquals(ServerCode.INPUT_ERROR, exception.getServerCode());
        assertTrue(exception.getDesc().contains("参数验证失败"));
    }

    @Test
    @DisplayName("没有合适的发送策略")
    void sendMessage_NoSuitableStrategy() {
        // Given
        Long messageId = 12345L;
        MessageSendContext context = createMockContext(messageId, "UNKNOWN001");
        
        when(mockContextBuilder.buildContext(messageId)).thenReturn(context);
        when(mockStrategy1.canHandle(context)).thenReturn(false);
        when(mockStrategy2.canHandle(context)).thenReturn(false);
        
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> sendMessageService.sendMessage(messageId));
        
        assertEquals(ServerCode.BUSINESS_ERROR, exception.getServerCode());
        assertTrue(exception.getDesc().contains("No suitable send strategy found"));
        
        verify(mockStrategy1, never()).sendMessage(any());
        verify(mockStrategy2, never()).sendMessage(any());
    }

    @Test
    @DisplayName("策略执行失败 - MessageSendException")
    void sendMessage_StrategyExecutionFailed() {
        // Given
        Long messageId = 12345L;
        MessageSendContext context = createMockContext(messageId, "BTCBCNBJ001");
        MessageSendException sendException = new MessageSendException("SEND_001", "发送失败");
        
        when(mockContextBuilder.buildContext(messageId)).thenReturn(context);
        when(mockStrategy1.canHandle(context)).thenReturn(true);
        when(mockStrategy1.getPriority()).thenReturn(1);
        when(mockStrategy1.getStrategyName()).thenReturn("TestStrategy1");
        doThrow(sendException).when(mockStrategy1).sendMessage(context);
        
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> sendMessageService.sendMessage(messageId));
        
        assertEquals(ServerCode.BUSINESS_ERROR, exception.getServerCode());
        assertTrue(exception.getDesc().contains("消息发送失败"));
    }

    @Test
    @DisplayName("构建上下文失败")
    void sendMessage_ContextBuildFailed() {
        // Given
        Long messageId = 12345L;
        RuntimeException buildException = new RuntimeException("构建上下文失败");
        
        when(mockContextBuilder.buildContext(messageId)).thenThrow(buildException);
        
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> sendMessageService.sendMessage(messageId));
        
        assertEquals(ServerCode.SERVER_ERROR, exception.getServerCode());
        assertTrue(exception.getDesc().contains("消息发送过程中发生异常"));
    }

    @Test
    @DisplayName("空策略列表")
    void sendMessage_EmptyStrategyList() {
        // Given
        SendMessageService serviceWithEmptyStrategies = new SendMessageServiceImpl(
            Collections.emptyList(), mockContextBuilder);
        Long messageId = 12345L;
        MessageSendContext context = createMockContext(messageId, "BTCBCNBJ001");
        
        when(mockContextBuilder.buildContext(messageId)).thenReturn(context);
        
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> serviceWithEmptyStrategies.sendMessage(messageId));
        
        assertEquals(ServerCode.BUSINESS_ERROR, exception.getServerCode());
        assertTrue(exception.getDesc().contains("No suitable send strategy found"));
    }

    @Test
    @DisplayName("策略执行过程中发生未预期异常")
    void sendMessage_UnexpectedException() throws Exception {
        // Given
        Long messageId = 12345L;
        MessageSendContext context = createMockContext(messageId, "BTCBCNBJ001");
        RuntimeException unexpectedException = new RuntimeException("未预期的异常");
        
        when(mockContextBuilder.buildContext(messageId)).thenReturn(context);
        when(mockStrategy1.canHandle(context)).thenReturn(true);
        when(mockStrategy1.getPriority()).thenReturn(1);
        when(mockStrategy1.getStrategyName()).thenReturn("TestStrategy1");
        doThrow(unexpectedException).when(mockStrategy1).sendMessage(context);
        
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> sendMessageService.sendMessage(messageId));
        
        assertEquals(ServerCode.SERVER_ERROR, exception.getServerCode());
        assertTrue(exception.getDesc().contains("消息发送过程中发生异常"));
    }

    /**
     * 创建模拟的MessageSendContext
     */
    private MessageSendContext createMockContext(Long messageId, String receiverBIC) {
        MessageSendContext context = new MessageSendContext();
        context.setMessageId(messageId);
        context.setReceiverBIC(receiverBIC);
        
        EndpointConfig endpointConfig = new EndpointConfig();
        endpointConfig.setType("GFIN");
        endpointConfig.setUrl("http://test.example.com");
        context.setEndpointConfig(endpointConfig);
        
        return context;
    }
} 