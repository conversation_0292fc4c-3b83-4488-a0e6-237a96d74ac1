package com.bihu.swifttrack.service;

import com.bihu.swifttrack.common.exception.BusinessException;
import com.bihu.swifttrack.common.exception.ServerCode;
import com.bihu.swifttrack.routing.config.EndpointConfig;
import com.bihu.swifttrack.common.enums.MessageFormat;
import com.bihu.swifttrack.mybatis.repository.po.MessageDetailsPO;
import com.bihu.swifttrack.mybatis.repository.po.MessagesPO;
import com.bihu.swifttrack.mybatis.repository.po.ReceiverPO;
import com.bihu.swifttrack.mybatis.repository.po.SenderPO;
import com.bihu.swifttrack.context.MessageSendContext;
import com.bihu.swifttrack.context.MessageSendContextBuilder;
import com.bihu.swifttrack.service.impl.SendMessageServiceImpl;
import com.bihu.swifttrack.service.strategy.MessageSendException;
import com.bihu.swifttrack.service.strategy.MessageSendStrategy;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * SendMessageService单元测试
 *
 * <AUTHOR>
 * @date 2024/12/20
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("SendMessageService单元测试")
class SendMessageServiceTest {

    @Mock
    private MessageSendStrategy mockStrategy1;
    
    @Mock
    private MessageSendStrategy mockStrategy2;
    
    @Mock
    private MessageSendContextBuilder mockContextBuilder;
    
    private SendMessageService sendMessageService;
    
    private List<MessageSendStrategy> strategies;

    @BeforeEach
    void setUp() {
        strategies = Arrays.asList(mockStrategy1, mockStrategy2);
        sendMessageService = new SendMessageServiceImpl(strategies, mockContextBuilder);
    }

    @Test
    @DisplayName("正常发送消息 - 成功场景")
    void sendMessage_Success() {
        // Given
        Long messageId = 12345L;
        MessageSendContext context = createMockContext(messageId, "BTCBCNBJ001");
        
        when(mockContextBuilder.buildContext(messageId)).thenReturn(context);
        when(mockStrategy1.canHandle(context)).thenReturn(true);
        when(mockStrategy1.getPriority()).thenReturn(1);
        when(mockStrategy1.getStrategyName()).thenReturn("TestStrategy1");
        when(mockStrategy2.canHandle(context)).thenReturn(false);
        
        // When & Then
        assertDoesNotThrow(() -> sendMessageService.sendMessage(messageId));
        
        verify(mockContextBuilder).buildContext(messageId);
        verify(mockStrategy1).canHandle(context);
        try {
            verify(mockStrategy1).sendMessage(context);
        } catch (MessageSendException e) {
            // This should not happen in this test
        }
        verify(mockStrategy2).canHandle(context);
        try {
            verify(mockStrategy2, never()).sendMessage(any());
        } catch (MessageSendException e) {
            // This should not happen in this test
        }
    }

    @Test
    @DisplayName("选择优先级最高的策略")
    void sendMessage_SelectHighestPriorityStrategy() {
        // Given
        Long messageId = 12345L;
        MessageSendContext context = createMockContext(messageId, "BTCBCNBJ001");
        
        when(mockContextBuilder.buildContext(messageId)).thenReturn(context);
        when(mockStrategy1.canHandle(context)).thenReturn(true);
        when(mockStrategy1.getPriority()).thenReturn(2);
        when(mockStrategy1.getStrategyName()).thenReturn("TestStrategy1");
        when(mockStrategy2.canHandle(context)).thenReturn(true);
        when(mockStrategy2.getPriority()).thenReturn(1); // 更高优先级
        when(mockStrategy2.getStrategyName()).thenReturn("TestStrategy2");
        
        // When
        sendMessageService.sendMessage(messageId);
        
        // Then
        verify(mockStrategy2).sendMessage(context); // 应该选择优先级更高的策略
        verify(mockStrategy1, never()).sendMessage(any());
    }

    @Test
    @DisplayName("参数验证失败 - messageId为null")
    void sendMessage_NullMessageId() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> sendMessageService.sendMessage(null));
        
        assertEquals(ServerCode.INPUT_ERROR, exception.getServerCode());
        assertTrue(exception.getDesc().contains("参数验证失败"));
        
        verify(mockContextBuilder, never()).buildContext(any());
    }

    @Test
    @DisplayName("参数验证失败 - messageId为负数")
    void sendMessage_NegativeMessageId() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> sendMessageService.sendMessage(-1L));
        
        assertEquals(ServerCode.INPUT_ERROR, exception.getServerCode());
        assertTrue(exception.getDesc().contains("参数验证失败"));
    }

    @Test
    @DisplayName("没有合适的发送策略")
    void sendMessage_NoSuitableStrategy() {
        // Given
        Long messageId = 12345L;
        MessageSendContext context = createMockContext(messageId, "UNKNOWN001");
        
        when(mockContextBuilder.buildContext(messageId)).thenReturn(context);
        when(mockStrategy1.canHandle(context)).thenReturn(false);
        when(mockStrategy2.canHandle(context)).thenReturn(false);
        
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> sendMessageService.sendMessage(messageId));
        
        assertEquals(ServerCode.BUSINESS_ERROR, exception.getServerCode());
        assertTrue(exception.getDesc().contains("No suitable send strategy found"));
        
        verify(mockStrategy1, never()).sendMessage(any());
        verify(mockStrategy2, never()).sendMessage(any());
    }

    @Test
    @DisplayName("策略执行失败 - MessageSendException")
    void sendMessage_StrategyExecutionFailed() {
        // Given
        Long messageId = 12345L;
        MessageSendContext context = createMockContext(messageId, "BTCBCNBJ001");
        MessageSendException sendException = new MessageSendException("SEND_001", "发送失败");
        
        when(mockContextBuilder.buildContext(messageId)).thenReturn(context);
        when(mockStrategy1.canHandle(context)).thenReturn(true);
        when(mockStrategy1.getPriority()).thenReturn(1);
        when(mockStrategy1.getStrategyName()).thenReturn("TestStrategy1");
        doThrow(sendException).when(mockStrategy1).sendMessage(context);
        
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> sendMessageService.sendMessage(messageId));
        
        assertEquals(ServerCode.BUSINESS_ERROR, exception.getServerCode());
        assertTrue(exception.getDesc().contains("消息发送失败"));
    }

    @Test
    @DisplayName("构建上下文失败")
    void sendMessage_ContextBuildFailed() {
        // Given
        Long messageId = 12345L;
        RuntimeException buildException = new RuntimeException("构建上下文失败");
        
        when(mockContextBuilder.buildContext(messageId)).thenThrow(buildException);
        
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> sendMessageService.sendMessage(messageId));
        
        assertEquals(ServerCode.SERVER_ERROR, exception.getServerCode());
        assertTrue(exception.getDesc().contains("消息发送过程中发生异常"));
    }

    @Test
    @DisplayName("空策略列表")
    void sendMessage_EmptyStrategyList() {
        // Given
        SendMessageService serviceWithEmptyStrategies = new SendMessageServiceImpl(
            Collections.emptyList(), mockContextBuilder);
        Long messageId = 12345L;
        MessageSendContext context = createMockContext(messageId, "BTCBCNBJ001");
        
        when(mockContextBuilder.buildContext(messageId)).thenReturn(context);
        
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> serviceWithEmptyStrategies.sendMessage(messageId));
        
        assertEquals(ServerCode.BUSINESS_ERROR, exception.getServerCode());
        assertTrue(exception.getDesc().contains("No suitable send strategy found"));
    }

    @Test
    @DisplayName("策略执行过程中发生未预期异常")
    void sendMessage_UnexpectedException() throws Exception {
        // Given
        Long messageId = 12345L;
        MessageSendContext context = createMockContext(messageId, "BTCBCNBJ001");
        RuntimeException unexpectedException = new RuntimeException("未预期的异常");
        
        when(mockContextBuilder.buildContext(messageId)).thenReturn(context);
        when(mockStrategy1.canHandle(context)).thenReturn(true);
        when(mockStrategy1.getPriority()).thenReturn(1);
        when(mockStrategy1.getStrategyName()).thenReturn("TestStrategy1");
        doThrow(unexpectedException).when(mockStrategy1).sendMessage(context);
        
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> sendMessageService.sendMessage(messageId));
        
        assertEquals(ServerCode.SERVER_ERROR, exception.getServerCode());
        assertTrue(exception.getDesc().contains("消息发送过程中发生异常"));
    }

    /**
     * 创建模拟的MessageSendContext
     */
    private MessageSendContext createMockContext(Long messageId, String receiverBIC) {
        // 创建模拟的消息对象
        MessagesPO message = createMockMessage(messageId);

        // 创建模拟的发送方对象
        SenderPO sender = createMockSender(messageId);

        // 创建模拟的接收方对象
        ReceiverPO receiver = createMockReceiver(messageId, receiverBIC);

        // 创建模拟的消息详情列表
        List<MessageDetailsPO> messageDetails = createMockMessageDetails(messageId);

        // 创建模拟的端点配置
        EndpointConfig endpointConfig = createMockEndpointConfig();

        // 使用Builder模式创建MessageSendContext
        return MessageSendContext.builder()
                .messageId(messageId)
                .receiverBIC(receiverBIC)
                .messageContent("Test message content for " + messageId)
                .messageFormat(MessageFormat.MT)
                .message(message)
                .sender(sender)
                .receiver(receiver)
                .messageDetails(messageDetails)
                .endpointConfig(endpointConfig)
                .build();
    }

    /**
     * 创建模拟的消息对象
     */
    private MessagesPO createMockMessage(Long messageId) {
        MessagesPO message = new MessagesPO();
        message.setId(messageId);
        message.setMessageType("MT103");
        message.setStatus(1);
        message.setDirection(1);
        message.setAmount(new BigDecimal("1000.00"));
        message.setCreatedAt(LocalDateTime.now());
        message.setUpdatedAt(LocalDateTime.now());
        return message;
    }

    /**
     * 创建模拟的发送方对象
     */
    private SenderPO createMockSender(Long messageId) {
        SenderPO sender = new SenderPO();
        sender.setId(1L);
        sender.setMessageId(messageId);
        sender.setBicCode("TESTBIC001");
        sender.setName("Test Sender");
        sender.setInstitution("Test Bank");
        sender.setAccount("*********");
        sender.setCountry("CN");
        sender.setAddress("Test Address");
        sender.setCreatedAt(LocalDateTime.now());
        sender.setUpdatedAt(LocalDateTime.now());
        return sender;
    }

    /**
     * 创建模拟的接收方对象
     */
    private ReceiverPO createMockReceiver(Long messageId, String receiverBIC) {
        ReceiverPO receiver = new ReceiverPO();
        receiver.setId(2L);
        receiver.setMessageId(messageId);
        receiver.setBicCode(receiverBIC);
        receiver.setName("Test Receiver");
        receiver.setInstitution("Receiver Bank");
        receiver.setAccount("*********");
        receiver.setCountry("US");
        receiver.setAddress("Receiver Address");
        receiver.setCreatedAt(LocalDateTime.now());
        receiver.setUpdatedAt(LocalDateTime.now());
        return receiver;
    }

    /**
     * 创建模拟的消息详情列表
     */
    private List<MessageDetailsPO> createMockMessageDetails(Long messageId) {
        MessageDetailsPO detail1 = new MessageDetailsPO();
        detail1.setId(1L);
        detail1.setMessageId(messageId);
        detail1.setFieldName("20");
        detail1.setFieldValue("TRN" + messageId);
        detail1.setCreatedAt(LocalDateTime.now());
        detail1.setUpdatedAt(LocalDateTime.now());

        MessageDetailsPO detail2 = new MessageDetailsPO();
        detail2.setId(2L);
        detail2.setMessageId(messageId);
        detail2.setFieldName("32A");
        detail2.setFieldValue("240101USD1000,00");
        detail2.setCreatedAt(LocalDateTime.now());
        detail2.setUpdatedAt(LocalDateTime.now());

        return Arrays.asList(detail1, detail2);
    }

    /**
     * 创建模拟的端点配置
     */
    private EndpointConfig createMockEndpointConfig() {
        EndpointConfig endpointConfig = new EndpointConfig();
        endpointConfig.setId("test-endpoint");
        endpointConfig.setType("GFIN");
        endpointConfig.setName("Test Endpoint");
        endpointConfig.setPriority(1);
        endpointConfig.setEnabled(true);
        return endpointConfig;
    }
} 