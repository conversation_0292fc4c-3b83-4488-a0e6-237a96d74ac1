package com.bihu.swifttrack.service;

import com.bihu.swifttrack.common.exception.BusinessException;
import com.bihu.swifttrack.config.EndpointConfig;
import com.bihu.swifttrack.config.RoutingConfig;
import com.bihu.swifttrack.context.MessageSendContext;
import com.bihu.swifttrack.context.MessageSendContextBuilder;
import com.bihu.swifttrack.resolver.BICRoutingResolver;
import com.bihu.swifttrack.service.strategy.DatabaseEndpointStrategy;
import com.bihu.swifttrack.service.strategy.GfinRemoteEndpointStrategy;
import com.bihu.swifttrack.service.strategy.MessageSendStrategy;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * SendMessageService集成测试
 * 
 * <p>测试完整的消息发送流程，包括策略选择和执行</p>
 *
 * <AUTHOR>
 * @date 2024/12/20
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
@DisplayName("SendMessageService集成测试")
class SendMessageServiceIntegrationTest {

    @Resource
    private SendMessageService sendMessageService;
    
    @Resource
    private List<MessageSendStrategy> messageStrategies;
    
    @MockBean
    private BICRoutingResolver bicRoutingResolver;
    
    @SpyBean
    private MessageSendContextBuilder contextBuilder;
    
    @SpyBean
    private GfinRemoteEndpointStrategy gfinStrategy;
    
    @SpyBean
    private DatabaseEndpointStrategy databaseStrategy;

    @BeforeEach
    void setUp() {
        // 重置所有mock和spy
        reset(bicRoutingResolver, gfinStrategy, databaseStrategy);
    }

    @Test
    @DisplayName("集成测试 - GFIN远程端点策略")
    void sendMessage_GfinRemoteEndpoint_Success() {
        // Given
        Long messageId = 1001L;
        String receiverBIC = "GFINSGSG001";
        
        EndpointConfig gfinEndpoint = new EndpointConfig();
        gfinEndpoint.setType("GFIN");
        gfinEndpoint.setUrl("http://gfin.test.com/api/send");
        gfinEndpoint.setTimeoutMs(5000);
        
        when(bicRoutingResolver.resolveEndpoint(receiverBIC)).thenReturn(gfinEndpoint);
        doNothing().when(gfinStrategy).sendMessage(any(MessageSendContext.class));
        
        // When & Then
        assertDoesNotThrow(() -> sendMessageService.sendMessage(messageId));
        
        // 验证调用
        verify(contextBuilder).buildContext(messageId);
        verify(bicRoutingResolver).resolveEndpoint(receiverBIC);
        verify(gfinStrategy).sendMessage(any(MessageSendContext.class));
        verify(databaseStrategy, never()).sendMessage(any());
    }

    @Test
    @DisplayName("集成测试 - 数据库端点策略")
    void sendMessage_DatabaseEndpoint_Success() {
        // Given
        Long messageId = 1002L;
        String receiverBIC = "LOCALDB001";
        
        EndpointConfig dbEndpoint = new EndpointConfig();
        dbEndpoint.setType("DATABASE");
        dbEndpoint.setTableName("outbound_messages");
        
        when(bicRoutingResolver.resolveEndpoint(receiverBIC)).thenReturn(dbEndpoint);
        doNothing().when(databaseStrategy).sendMessage(any(MessageSendContext.class));
        
        // When & Then
        assertDoesNotThrow(() -> sendMessageService.sendMessage(messageId));
        
        // 验证调用
        verify(contextBuilder).buildContext(messageId);
        verify(bicRoutingResolver).resolveEndpoint(receiverBIC);
        verify(databaseStrategy).sendMessage(any(MessageSendContext.class));
        verify(gfinStrategy, never()).sendMessage(any());
    }

    @Test
    @DisplayName("集成测试 - 策略优先级选择")
    void sendMessage_StrategyPrioritySelection() {
        // Given
        Long messageId = 1003L;
        String receiverBIC = "TESTBIC001";
        
        // 配置两个策略都能处理的端点
        EndpointConfig endpoint = new EndpointConfig();
        endpoint.setType("GFIN");
        endpoint.setUrl("http://test.com");
        
        when(bicRoutingResolver.resolveEndpoint(receiverBIC)).thenReturn(endpoint);
        
        // 验证策略优先级
        assertTrue(gfinStrategy.getPriority() < databaseStrategy.getPriority(), 
            "GFIN策略应该有更高的优先级（更小的数值）");
        
        // When
        sendMessageService.sendMessage(messageId);
        
        // Then - 应该选择优先级更高的GFIN策略
        verify(gfinStrategy).sendMessage(any(MessageSendContext.class));
        verify(databaseStrategy, never()).sendMessage(any());
    }

    @Test
    @DisplayName("集成测试 - BIC路由解析失败")
    void sendMessage_BICResolutionFailed() {
        // Given
        Long messageId = 1004L;
        String receiverBIC = "UNKNOWN001";
        
        when(bicRoutingResolver.resolveEndpoint(receiverBIC)).thenReturn(null);
        
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> sendMessageService.sendMessage(messageId));
        
        assertTrue(exception.getDesc().contains("No suitable send strategy found") ||
                  exception.getDesc().contains("消息发送过程中发生异常"));
        
        verify(bicRoutingResolver).resolveEndpoint(receiverBIC);
    }

    @Test
    @DisplayName("集成测试 - 策略执行异常处理")
    void sendMessage_StrategyExecutionException() {
        // Given
        Long messageId = 1005L;
        String receiverBIC = "GFINSGSG001";
        
        EndpointConfig gfinEndpoint = new EndpointConfig();
        gfinEndpoint.setType("GFIN");
        gfinEndpoint.setUrl("http://gfin.test.com/api/send");
        
        when(bicRoutingResolver.resolveEndpoint(receiverBIC)).thenReturn(gfinEndpoint);
        doThrow(new RuntimeException("网络连接失败")).when(gfinStrategy).sendMessage(any());
        
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> sendMessageService.sendMessage(messageId));
        
        assertTrue(exception.getDesc().contains("消息发送过程中发生异常"));
        verify(gfinStrategy).sendMessage(any(MessageSendContext.class));
    }

    @Test
    @DisplayName("集成测试 - 验证策略注册")
    void verifyStrategiesRegistered() {
        // 验证所有策略都已注册
        assertNotNull(messageStrategies);
        assertTrue(messageStrategies.size() >= 2, "至少应该有2个策略注册");
        
        // 验证策略类型
        boolean hasGfinStrategy = messageStrategies.stream()
            .anyMatch(s -> s instanceof GfinRemoteEndpointStrategy);
        boolean hasDatabaseStrategy = messageStrategies.stream()
            .anyMatch(s -> s instanceof DatabaseEndpointStrategy);
        
        assertTrue(hasGfinStrategy, "应该包含GFIN远程端点策略");
        assertTrue(hasDatabaseStrategy, "应该包含数据库端点策略");
    }

    @Test
    @DisplayName("集成测试 - 上下文构建验证")
    void sendMessage_ContextBuilding() {
        // Given
        Long messageId = 1006L;
        String receiverBIC = "TESTBIC002";
        
        EndpointConfig endpoint = new EndpointConfig();
        endpoint.setType("DATABASE");
        endpoint.setTableName("outbound_messages");
        
        when(bicRoutingResolver.resolveEndpoint(receiverBIC)).thenReturn(endpoint);
        doNothing().when(databaseStrategy).sendMessage(any());
        
        // When
        sendMessageService.sendMessage(messageId);
        
        // Then - 验证上下文构建
        verify(contextBuilder).buildContext(messageId);
        
        // 验证传递给策略的上下文包含正确信息
        verify(databaseStrategy).sendMessage(argThat(context -> 
            context.getMessageId().equals(messageId) &&
            context.getReceiverBIC().equals(receiverBIC) &&
            context.getEndpointConfig().equals(endpoint)
        ));
    }

    @Test
    @DisplayName("集成测试 - 多个消息并发发送")
    void sendMessage_ConcurrentSending() {
        // Given
        Long[] messageIds = {2001L, 2002L, 2003L};
        String receiverBIC = "CONCTEST001";
        
        EndpointConfig endpoint = new EndpointConfig();
        endpoint.setType("DATABASE");
        endpoint.setTableName("outbound_messages");
        
        when(bicRoutingResolver.resolveEndpoint(receiverBIC)).thenReturn(endpoint);
        doNothing().when(databaseStrategy).sendMessage(any());
        
        // When - 并发发送多个消息
        for (Long messageId : messageIds) {
            assertDoesNotThrow(() -> sendMessageService.sendMessage(messageId));
        }
        
        // Then
        verify(contextBuilder, times(messageIds.length)).buildContext(any());
        verify(databaseStrategy, times(messageIds.length)).sendMessage(any());
    }
} 