package com.bihu.swifttrack.service;

import com.bihu.swifttrack.routing.config.EndpointConfig;
import com.bihu.swifttrack.routing.BICRoutingResolver;
import com.bihu.swifttrack.service.strategy.DatabaseEndpointStrategy;
import com.bihu.swifttrack.service.strategy.GfinRemoteEndpointStrategy;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * SendMessageService性能测试
 * 
 * <p>测试消息发送服务在高并发和大量数据场景下的性能表现</p>
 *
 * <AUTHOR>
 * @date 2024/12/20
 */
@SpringBootTest
@ActiveProfiles("test")
@DisplayName("SendMessageService性能测试")
class SendMessageServicePerformanceTest {

    @Resource
    private SendMessageService sendMessageService;
    
    @MockBean
    private BICRoutingResolver bicRoutingResolver;
    
    @SpyBean
    private GfinRemoteEndpointStrategy gfinStrategy;
    
    @SpyBean
    private DatabaseEndpointStrategy databaseStrategy;
    
    private ExecutorService executorService;

    @BeforeEach
    void setUp() {
        // 重置mock
        reset(bicRoutingResolver, gfinStrategy, databaseStrategy);
        
        // 创建线程池
        executorService = Executors.newFixedThreadPool(10);
        
        // 配置默认端点
        EndpointConfig defaultEndpoint = new EndpointConfig();
        defaultEndpoint.setType("DATABASE");
        defaultEndpoint.setTableName("outbound_messages");
        
        when(bicRoutingResolver.resolveEndpoint(anyString())).thenReturn(defaultEndpoint);
        doNothing().when(databaseStrategy).sendMessage(any());
    }

    @Test
    @DisplayName("性能测试 - 单线程批量发送")
    void performanceTest_SingleThreadBatch() {
        // Given
        int messageCount = 1000;
        List<Long> messageIds = new ArrayList<>();
        for (int i = 1; i <= messageCount; i++) {
            messageIds.add((long) i);
        }
        
        // When
        Instant start = Instant.now();
        
        for (Long messageId : messageIds) {
            sendMessageService.sendMessage(messageId);
        }
        
        Instant end = Instant.now();
        Duration duration = Duration.between(start, end);
        
        // Then
        System.out.printf("单线程发送 %d 条消息耗时: %d ms%n", messageCount, duration.toMillis());
        System.out.printf("平均每条消息耗时: %.2f ms%n", (double) duration.toMillis() / messageCount);
        
        // 验证所有消息都被处理
        verify(databaseStrategy, times(messageCount)).sendMessage(any());
        
        // 性能断言 - 平均每条消息处理时间应该小于10ms
        assertTrue(duration.toMillis() / messageCount < 10, 
            "平均每条消息处理时间应该小于10ms");
    }

    @Test
    @DisplayName("性能测试 - 多线程并发发送")
    void performanceTest_MultiThreadConcurrent() throws InterruptedException {
        // Given
        int threadCount = 10;
        int messagesPerThread = 100;
        int totalMessages = threadCount * messagesPerThread;
        
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch endLatch = new CountDownLatch(threadCount);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);
        
        List<Future<Void>> futures = new ArrayList<>();
        
        // When
        Instant start = Instant.now();
        
        for (int t = 0; t < threadCount; t++) {
            final int threadIndex = t;
            Future<Void> future = executorService.submit(() -> {
                try {
                    startLatch.await(); // 等待统一开始
                    
                    for (int i = 1; i <= messagesPerThread; i++) {
                        Long messageId = (long) (threadIndex * messagesPerThread + i);
                        try {
                            sendMessageService.sendMessage(messageId);
                            successCount.incrementAndGet();
                        } catch (Exception e) {
                            errorCount.incrementAndGet();
                            System.err.printf("发送消息失败: messageId=%d, error=%s%n", 
                                messageId, e.getMessage());
                        }
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    endLatch.countDown();
                }
                return null;
            });
            futures.add(future);
        }
        
        startLatch.countDown(); // 开始执行
        boolean completed = endLatch.await(30, TimeUnit.SECONDS); // 等待完成
        
        Instant end = Instant.now();
        Duration duration = Duration.between(start, end);
        
        // Then
        assertTrue(completed, "所有线程应该在30秒内完成");
        
        System.out.printf("多线程并发发送 %d 条消息耗时: %d ms%n", totalMessages, duration.toMillis());
        System.out.printf("成功: %d, 失败: %d%n", successCount.get(), errorCount.get());
        System.out.printf("吞吐量: %.2f 消息/秒%n", 
            (double) successCount.get() / duration.toSeconds());
        
        // 验证结果
        assertEquals(totalMessages, successCount.get(), "所有消息都应该成功发送");
        assertEquals(0, errorCount.get(), "不应该有失败的消息");
        
        // 性能断言 - 吞吐量应该大于100消息/秒
        double throughput = (double) successCount.get() / duration.toSeconds();
        assertTrue(throughput > 100, "吞吐量应该大于100消息/秒");
    }

    @Test
    @DisplayName("性能测试 - 缓存效果验证")
    void performanceTest_CacheEffectiveness() {
        // Given
        String[] bicCodes = {"BTCBCNBJ001", "GFINSGSG001", "LOCALDB001"};
        int repeatCount = 100;
        
        // 第一轮 - 冷启动
        Instant coldStart = Instant.now();
        for (int i = 0; i < repeatCount; i++) {
            for (String bic : bicCodes) {
                Long messageId = (long) (i * bicCodes.length + java.util.Arrays.asList(bicCodes).indexOf(bic) + 1);
                sendMessageService.sendMessage(messageId);
            }
        }
        Instant coldEnd = Instant.now();
        Duration coldDuration = Duration.between(coldStart, coldEnd);
        
        // 重置mock计数
        reset(bicRoutingResolver);
        when(bicRoutingResolver.resolveEndpoint(anyString())).thenReturn(createDefaultEndpoint());
        
        // 第二轮 - 热启动（缓存生效）
        Instant warmStart = Instant.now();
        for (int i = 0; i < repeatCount; i++) {
            for (String bic : bicCodes) {
                Long messageId = (long) (i * bicCodes.length + java.util.Arrays.asList(bicCodes).indexOf(bic) + 1000);
                sendMessageService.sendMessage(messageId);
            }
        }
        Instant warmEnd = Instant.now();
        Duration warmDuration = Duration.between(warmStart, warmEnd);
        
        // Then
        System.out.printf("冷启动耗时: %d ms%n", coldDuration.toMillis());
        System.out.printf("热启动耗时: %d ms%n", warmDuration.toMillis());
        System.out.printf("性能提升: %.2f%%%n", 
            ((double) (coldDuration.toMillis() - warmDuration.toMillis()) / coldDuration.toMillis()) * 100);
        
        // 缓存应该提升性能（热启动应该更快）
        assertTrue(warmDuration.toMillis() <= coldDuration.toMillis(), 
            "缓存应该提升性能，热启动应该不慢于冷启动");
    }

    @Test
    @DisplayName("性能测试 - 内存使用监控")
    void performanceTest_MemoryUsage() {
        // Given
        Runtime runtime = Runtime.getRuntime();
        int messageCount = 5000;
        
        // 执行GC并记录初始内存
        System.gc();
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();
        
        // When - 发送大量消息
        for (int i = 1; i <= messageCount; i++) {
            sendMessageService.sendMessage((long) i);
            
            // 每1000条消息检查一次内存
            if (i % 1000 == 0) {
                long currentMemory = runtime.totalMemory() - runtime.freeMemory();
                System.out.printf("发送 %d 条消息后内存使用: %.2f MB%n", 
                    i, (currentMemory - initialMemory) / 1024.0 / 1024.0);
            }
        }
        
        // 最终内存检查
        System.gc();
        long finalMemory = runtime.totalMemory() - runtime.freeMemory();
        long memoryIncrease = finalMemory - initialMemory;
        
        // Then
        System.out.printf("总内存增长: %.2f MB%n", memoryIncrease / 1024.0 / 1024.0);
        System.out.printf("平均每条消息内存开销: %.2f KB%n", 
            (double) memoryIncrease / messageCount / 1024.0);
        
        // 内存增长应该在合理范围内（小于100MB）
        assertTrue(memoryIncrease < 100 * 1024 * 1024, 
            "内存增长应该小于100MB");
    }

    @Test
    @DisplayName("性能测试 - 错误处理性能")
    void performanceTest_ErrorHandlingPerformance() {
        // Given
        int errorMessageCount = 100;
        
        // 配置会导致错误的场景
        when(bicRoutingResolver.resolveEndpoint(anyString())).thenReturn(null);
        
        // When
        Instant start = Instant.now();
        
        int errorCount = 0;
        for (int i = 1; i <= errorMessageCount; i++) {
            try {
                sendMessageService.sendMessage((long) i);
            } catch (Exception e) {
                errorCount++;
            }
        }
        
        Instant end = Instant.now();
        Duration duration = Duration.between(start, end);
        
        // Then
        System.out.printf("错误处理 %d 条消息耗时: %d ms%n", errorMessageCount, duration.toMillis());
        System.out.printf("平均每条错误消息处理时间: %.2f ms%n", 
            (double) duration.toMillis() / errorMessageCount);
        
        assertEquals(errorMessageCount, errorCount, "所有消息都应该产生错误");
        
        // 错误处理也应该保持高性能
        assertTrue(duration.toMillis() / errorMessageCount < 50, 
            "平均每条错误消息处理时间应该小于50ms");
    }

    private EndpointConfig createDefaultEndpoint() {
        EndpointConfig endpoint = new EndpointConfig();
        endpoint.setType("DATABASE");
        endpoint.setTableName("outbound_messages");
        return endpoint;
    }
} 