package com.bihu.swifttrack.service;

import com.bihu.swifttrack.dto.UserInfoDTO;
import com.bihu.swifttrack.mybatis.repository.po.UserPO;

/**
 * <AUTHOR>
 * @date 2024/6/4
 */
public interface UserService {

  /**
   * 添加一个新的用户
   *
   * @param userPO
   * @return 用户 id
   */
  Long addUser(UserPO userPO);

  /**
   * 获取用户信息
   *
   * @param loginName
   * @return
   */
  UserInfoDTO getUserInfo(String loginName);


  /**
   * 是否用户邮箱已经存在
   *
   * @param email
   * @return
   */
  boolean isUserEmailExist(String email);

  /**
   * 是否用户手机号码已经存在
   *
   * @param phone
   * @return
   */
  boolean isUserPhoneExist(String phone);

  /**
   * 修改密码
   *
   * @param username
   * @param oldPassword
   * @param newPassword
   */
  void changePassword(String username, String oldPassword, String newPassword);


  /**
   * 获取上级用户 id
   *
   * @param operator
   * @return
   */
  Long getParentId(Long operator);
}
