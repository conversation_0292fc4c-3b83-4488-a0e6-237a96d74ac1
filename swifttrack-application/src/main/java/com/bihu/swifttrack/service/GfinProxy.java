package com.bihu.swifttrack.service;

import com.bihu.swifttrack.common.page.PageData;
import com.bihu.swifttrack.gfin.dto.MessageResponse;
import java.io.IOException;

/**
 * GfinProxy
 *
 * <p></p>
 *
 * <AUTHOR>
 * @date 2024/08/28
 */
public interface GfinProxy {

//  String authenticate(String username, String password) throws IOException;

  MessageResponse getMessage(String messageId) throws IOException;

  void sendMessage(String content, String msgFormat) throws IOException;

  PageData<MessageResponse> getMessages(String messageType, String receiver, String sender,
      String status, int page, int size) throws IOException;

  MessageResponse updateMessageStatus(String messageId, String status) throws IOException;
}
