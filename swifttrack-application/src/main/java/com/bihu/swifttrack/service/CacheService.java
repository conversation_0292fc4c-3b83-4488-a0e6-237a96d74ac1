package com.bihu.swifttrack.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Service;

import java.util.Collection;

/**
 * 缓存管理服务
 * 
 * <p>提供缓存清理和监控功能</p>
 *
 * <AUTHOR>
 * @date 2024/12/20
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CacheService {
    
    private final CacheManager cacheManager;
    
    /**
     * 清理所有缓存
     */
    public void clearAllCaches() {
        Collection<String> cacheNames = cacheManager.getCacheNames();
        log.info("Clearing all caches: {}", cacheNames);
        
        cacheNames.forEach(cacheName -> {
            var cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                cache.clear();
                log.debug("Cleared cache: {}", cacheName);
            }
        });
        
        log.info("All caches cleared successfully");
    }
    
    /**
     * 清理指定缓存
     * @param cacheName 缓存名称
     */
    public void clearCache(String cacheName) {
        var cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            cache.clear();
            log.info("Cache '{}' cleared successfully", cacheName);
        } else {
            log.warn("Cache '{}' not found", cacheName);
        }
    }
    
    /**
     * 获取所有缓存名称
     * @return 缓存名称集合
     */
    public Collection<String> getCacheNames() {
        return cacheManager.getCacheNames();
    }
} 