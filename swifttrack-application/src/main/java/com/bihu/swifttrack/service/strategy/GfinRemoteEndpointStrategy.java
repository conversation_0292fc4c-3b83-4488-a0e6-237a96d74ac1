package com.bihu.swifttrack.service.strategy;

import com.bihu.swifttrack.common.enums.MessageFormat;
import com.bihu.swifttrack.context.MessageSendContext;
import com.bihu.swifttrack.service.GfinProxy;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * GFIN远程端点发送策略
 * 
 * <p>通过GFIN代理发送消息到远程端点</p>
 *
 * <AUTHOR>
 * @date 2024/12/20
 */
@Component
@RequiredArgsConstructor
public class GfinRemoteEndpointStrategy extends AbstractMessageSendStrategy {
    
    private final GfinProxy gfinProxy;
    
    @Override
    protected String getSupportedEndpointType() {
        return MessageSendConstants.EndpointType.GFIN_REMOTE;
    }
    
    @Override
    protected void doSendMessage(MessageSendContext context) throws MessageSendException {
        try {
            String formatName = context.getMessageFormat() == MessageFormat.MT 
                ? context.getMessageFormat().name() : "MX";
            
            gfinProxy.sendMessage(context.getMessageContent(), formatName);
        } catch (Exception e) {
            throw new MessageSendException.StrategyExecutionException("GFIN发送失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public String getStrategyName() {
        return MessageSendConstants.EndpointType.GFIN_REMOTE;
    }
    
    @Override
    public int getPriority() {
        return MessageSendConstants.Priority.GFIN_REMOTE;
    }
} 