package com.bihu.swifttrack.service.strategy;

import com.bihu.swifttrack.context.MessageSendContext;
import lombok.extern.slf4j.Slf4j;

/**
 * 抽象消息发送策略基类
 * 
 * <p>提供通用的策略实现逻辑，减少重复代码</p>
 *
 * <AUTHOR>
 * @date 2024/12/20
 */
@Slf4j
public abstract class AbstractMessageSendStrategy implements MessageSendStrategy {
    
    @Override
    public final boolean canHandle(MessageSendContext context) {
        if (context == null || context.getEndpointConfig() == null) {
            return false;
        }
        
        return getSupportedEndpointType().equals(context.getEndpointConfig().getType());
    }
    
    @Override
    public final void sendMessage(MessageSendContext context) throws MessageSendException {
        try {
            // 验证上下文
            ValidationUtils.validateContext(context);
            
            log.info("Sending message via {} strategy, messageId: {}, receiverBIC: {}", 
                getStrategyName(), context.getMessageId(), context.getReceiverBIC());
            
            // 执行具体的发送逻辑
            doSendMessage(context);
            
            log.info("Message sent successfully via {} strategy, messageId: {}", 
                getStrategyName(), context.getMessageId());
                
        } catch (MessageSendException e) {
            log.error("Failed to send message via {} strategy, messageId: {}, error: {}", 
                getStrategyName(), context.getMessageId(), e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error in {} strategy, messageId: {}, error: {}", 
                getStrategyName(), context.getMessageId(), e.getMessage(), e);
            throw new MessageSendException.StrategyExecutionException(
                getStrategyName() + "策略执行失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取支持的端点类型
     * @return 端点类型
     */
    protected abstract String getSupportedEndpointType();
    
    /**
     * 执行具体的发送逻辑
     * @param context 消息发送上下文
     * @throws MessageSendException 发送失败时抛出
     */
    protected abstract void doSendMessage(MessageSendContext context) throws MessageSendException;
} 