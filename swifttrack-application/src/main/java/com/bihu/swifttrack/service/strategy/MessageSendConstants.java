package com.bihu.swifttrack.service.strategy;

/**
 * 消息发送相关常量
 * 
 * <p>统一管理消息发送过程中使用的常量值</p>
 *
 * <AUTHOR>
 * @date 2024/12/20
 */
public final class MessageSendConstants {
    
    private MessageSendConstants() {
        // 工具类，禁止实例化
    }
    
    /**
     * 端点类型
     */
    public static final class EndpointType {
        public static final String GFIN_REMOTE = "GFIN_REMOTE";
        public static final String LOCAL_DATABASE = "LOCAL_DATABASE";
        
        private EndpointType() {}
    }
    
    /**
     * 消息状态
     */
    public static final class MessageStatus {
        public static final String PENDING = "PENDING";
        public static final String SENT = "SENT";
        public static final String FAILED = "FAILED";
        
        private MessageStatus() {}
    }
    
    /**
     * 策略优先级
     */
    public static final class Priority {
        public static final int GFIN_REMOTE = 10;
        public static final int LOCAL_DATABASE = 20;
        public static final int DEFAULT = 100;
        
        private Priority() {}
    }
} 