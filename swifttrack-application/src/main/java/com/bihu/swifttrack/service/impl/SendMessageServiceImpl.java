package com.bihu.swifttrack.service.impl;

import com.bihu.swifttrack.common.exception.BusinessException;
import com.bihu.swifttrack.common.exception.ServerCode;
import com.bihu.swifttrack.context.MessageSendContext;
import com.bihu.swifttrack.context.MessageSendContextBuilder;
import com.bihu.swifttrack.service.SendMessageService;
import com.bihu.swifttrack.service.strategy.MessageSendException;
import com.bihu.swifttrack.service.strategy.MessageSendStrategy;
import com.bihu.swifttrack.service.strategy.ValidationUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;

/**
 * SendMessageServiceImpl
 * 
 * <p>重构后的消息发送服务实现，支持多种发送策略</p>
 *
 * <AUTHOR>
 * @date 2024/08/26
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SendMessageServiceImpl implements SendMessageService {

    private final List<MessageSendStrategy> sendStrategies;
    private final MessageSendContextBuilder contextBuilder;

    @Override
    public void sendMessage(Long messageId) {
        try {
            // 参数验证
            ValidationUtils.validateMessageId(messageId);
            
            log.info("Starting to send message, messageId: {}", messageId);
            
            // 构建发送上下文
            MessageSendContext context = contextBuilder.buildContext(messageId);
            log.debug("Built send context for messageId: {}, receiverBIC: {}, endpointType: {}", 
                messageId, context.getReceiverBIC(), 
                context.getEndpointConfig() != null ? context.getEndpointConfig().getType() : "UNKNOWN");
            
            // 选择合适的发送策略
            MessageSendStrategy strategy = sendStrategies.stream()
                .filter(s -> s.canHandle(context))
                .min(Comparator.comparing(MessageSendStrategy::getPriority))
                .orElseThrow(() -> new BusinessException(ServerCode.BUSINESS_ERROR, "No suitable send strategy found for messageId: " + messageId));
            
            log.info("Selected strategy: {} for messageId: {}", strategy.getStrategyName(), messageId);
            
            // 执行发送
            strategy.sendMessage(context);
            
            log.info("Message sent successfully, messageId: {}, strategy: {}", 
                messageId, strategy.getStrategyName());
            
        } catch (IllegalArgumentException e) {
            log.error("Invalid parameter for messageId: {}, error: {}", messageId, e.getMessage());
            throw new BusinessException(ServerCode.INPUT_ERROR, "参数验证失败: " + e.getMessage());
        } catch (MessageSendException e) {
            log.error("Failed to send message, messageId: {}, errorCode: {}, error: {}", 
                messageId, e.getErrorCode(), e.getMessage(), e);
            throw new BusinessException(ServerCode.BUSINESS_ERROR, "消息发送失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error while sending message, messageId: {}", messageId, e);
            throw new BusinessException(ServerCode.SERVER_ERROR, "消息发送过程中发生异常: " + e.getMessage());
        }
    }
}
