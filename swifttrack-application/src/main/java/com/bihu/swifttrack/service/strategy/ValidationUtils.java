package com.bihu.swifttrack.service.strategy;

import com.bihu.swifttrack.context.MessageSendContext;
import org.springframework.util.StringUtils;

/**
 * 参数验证工具类
 * 
 * <p>提供消息发送相关的参数验证方法</p>
 *
 * <AUTHOR>
 * @date 2024/12/20
 */
public final class ValidationUtils {
    
    private ValidationUtils() {
        // 工具类，禁止实例化
    }
    
    /**
     * 验证消息ID
     * @param messageId 消息ID
     * @throws IllegalArgumentException 如果消息ID无效
     */
    public static void validateMessageId(Long messageId) {
        if (messageId == null || messageId <= 0) {
            throw new IllegalArgumentException("Message ID must be positive number");
        }
    }
    
    /**
     * 验证BIC代码
     * @param bic BIC代码
     * @throws IllegalArgumentException 如果BIC代码无效
     */
    public static void validateBIC(String bic) {
        if (!StringUtils.hasText(bic)) {
            throw new IllegalArgumentException("BIC code cannot be null or empty");
        }
        if (bic.length() < 8 || bic.length() > 11) {
            throw new IllegalArgumentException("BIC code length must be between 8 and 11 characters");
        }
    }
    
    /**
     * 验证消息发送上下文
     * @param context 消息发送上下文
     * @throws MessageSendException.ContextBuildException 如果上下文无效
     */
    public static void validateContext(MessageSendContext context) throws MessageSendException.ContextBuildException {
        if (context == null) {
            throw new MessageSendException.ContextBuildException("Message send context cannot be null");
        }
        
        if (context.getMessageId() == null || context.getMessageId() <= 0) {
            throw new MessageSendException.ContextBuildException("Message ID must be positive number");
        }
        
        if (!StringUtils.hasText(context.getReceiverBIC())) {
            throw new MessageSendException.ContextBuildException("Receiver BIC cannot be null or empty");
        }
        
        if (!StringUtils.hasText(context.getMessageContent())) {
            throw new MessageSendException.ContextBuildException("Message content cannot be null or empty");
        }
        
        if (context.getMessageFormat() == null) {
            throw new MessageSendException.ContextBuildException("Message format cannot be null");
        }
    }
} 