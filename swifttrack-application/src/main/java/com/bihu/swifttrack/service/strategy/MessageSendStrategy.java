package com.bihu.swifttrack.service.strategy;

import com.bihu.swifttrack.context.MessageSendContext;

/**
 * 消息发送策略接口
 * 
 * <p>定义了消息发送的统一接口，支持多种发送方式</p>
 *
 * <AUTHOR>
 * @date 2024/12/20
 */
public interface MessageSendStrategy {
    
    /**
     * 判断该策略是否能处理指定的发送上下文
     * @param context 消息发送上下文
     * @return true 如果能处理，false 否则
     */
    boolean canHandle(MessageSendContext context);
    
    /**
     * 发送消息
     * @param context 消息发送上下文
     * @throws MessageSendException 发送失败时抛出（运行时异常）
     */
    void sendMessage(MessageSendContext context);
    
    /**
     * 获取策略名称
     * @return 策略名称
     */
    String getStrategyName();
    
    /**
     * 获取策略优先级，数值越小优先级越高
     * @return 优先级
     */
    default int getPriority() {
        return 100;
    }
} 