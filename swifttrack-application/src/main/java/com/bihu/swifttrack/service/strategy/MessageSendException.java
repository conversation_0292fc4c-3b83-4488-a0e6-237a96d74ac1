package com.bihu.swifttrack.service.strategy;

/**
 * 消息发送异常
 * 
 * <p>用于封装消息发送过程中的各种异常情况</p>
 *
 * <AUTHOR>
 * @date 2024/12/20
 */
public class MessageSendException extends Exception {
    
    private final String errorCode;
    
    public MessageSendException(String message) {
        super(message);
        this.errorCode = "UNKNOWN";
    }
    
    public MessageSendException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "UNKNOWN";
    }
    
    public MessageSendException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public MessageSendException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    /**
     * 上下文构建异常
     */
    public static class ContextBuildException extends MessageSendException {
        public ContextBuildException(String message) {
            super("CONTEXT_BUILD_ERROR", message);
        }
        
        public ContextBuildException(String message, Throwable cause) {
            super("CONTEXT_BUILD_ERROR", message, cause);
        }
    }
    
    /**
     * 路由解析异常
     */
    public static class RoutingException extends MessageSendException {
        public RoutingException(String message) {
            super("ROUTING_ERROR", message);
        }
        
        public RoutingException(String message, Throwable cause) {
            super("ROUTING_ERROR", message, cause);
        }
    }
    
    /**
     * 策略执行异常
     */
    public static class StrategyExecutionException extends MessageSendException {
        public StrategyExecutionException(String message) {
            super("STRATEGY_EXECUTION_ERROR", message);
        }
        
        public StrategyExecutionException(String message, Throwable cause) {
            super("STRATEGY_EXECUTION_ERROR", message, cause);
        }
    }
} 