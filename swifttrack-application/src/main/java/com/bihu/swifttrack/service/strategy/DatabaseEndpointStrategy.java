package com.bihu.swifttrack.service.strategy;

import com.bihu.swifttrack.context.MessageSendContext;
import com.bihu.swifttrack.mybatis.repository.OutboundMessagesRepository;
import com.bihu.swifttrack.mybatis.repository.po.OutboundMessagesPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 本地数据库端点发送策略
 * 
 * <p>将消息保存到本地数据库</p>
 *
 * <AUTHOR>
 * @date 2024/12/20
 */
@Component
@RequiredArgsConstructor
public class DatabaseEndpointStrategy extends AbstractMessageSendStrategy {
    
    private final OutboundMessagesRepository outboundMessageRepository;
    
    @Override
    protected String getSupportedEndpointType() {
        return MessageSendConstants.EndpointType.LOCAL_DATABASE;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doSendMessage(MessageSendContext context) throws MessageSendException {
        try {
            OutboundMessagesPO outboundMessage = OutboundMessagesPO.builder()
                .messageId(context.getMessageId())
                .receiverBic(context.getReceiverBIC())
                .messageContent(context.getMessageContent())
                .messageFormat(context.getMessageFormat().name())
                .endpointType(MessageSendConstants.EndpointType.LOCAL_DATABASE)
                .status(MessageSendConstants.MessageStatus.SENT)
                .createdAt(LocalDateTime.now())
                .build();
            
            outboundMessageRepository.save(outboundMessage);
        } catch (Exception e) {
            throw new MessageSendException.StrategyExecutionException("数据库保存失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public String getStrategyName() {
        return MessageSendConstants.EndpointType.LOCAL_DATABASE;
    }
    
    @Override
    public int getPriority() {
        return MessageSendConstants.Priority.LOCAL_DATABASE;
    }
} 