package com.bihu.swifttrack.context;

import com.bihu.swifttrack.common.enums.MessageFormat;
import com.bihu.swifttrack.mybatis.repository.po.MessageDetailsPO;
import com.bihu.swifttrack.mybatis.repository.po.MessagesPO;
import com.bihu.swifttrack.mybatis.repository.po.ReceiverPO;
import com.bihu.swifttrack.mybatis.repository.po.SenderPO;
import com.bihu.swifttrack.routing.config.EndpointConfig;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 消息发送上下文
 * 
 * <p>包含发送消息所需的所有信息</p>
 *
 * <AUTHOR>
 * @date 2024/12/20
 */
@Data
@Builder
public class MessageSendContext {
    
    /** 消息ID */
    private Long messageId;
    
    /** 接收方BIC代码 */
    private String receiverBIC;
    
    /** 消息内容 */
    private String messageContent;
    
    /** 消息格式 */
    private MessageFormat messageFormat;
    
    /** 消息对象 */
    private MessagesPO message;
    
    /** 发送方对象 */
    private SenderPO sender;
    
    /** 接收方对象 */
    private ReceiverPO receiver;
    
    /** 消息详情列表 */
    private List<MessageDetailsPO> messageDetails;
    
    /** 路由端点配置 */
    private EndpointConfig endpointConfig;
} 