/**
 * <AUTHOR>
 * @date 2024/06/06
 */
package com.bihu.swifttrack.controller;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.bihu.swifttrack.bo.MessagingBO;
import com.bihu.swifttrack.bo.MessagingRowBO;
import com.bihu.swifttrack.bo.MessagingStatBO;
import com.bihu.swifttrack.common.enums.MessageFormat;
import com.bihu.swifttrack.common.enums.MessagingType;
import com.bihu.swifttrack.common.exception.BusinessException;
import com.bihu.swifttrack.common.exception.ServerCode;
import com.bihu.swifttrack.common.page.PageData;
import com.bihu.swifttrack.common.response.CommonResponse;
import com.bihu.swifttrack.param.AddMessagingParam;
import com.bihu.swifttrack.request.message.DraftValidation;
import com.bihu.swifttrack.request.message.MessageQueryRequest;
import com.bihu.swifttrack.request.message.MessageRequest;
import com.bihu.swifttrack.service.MessageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Optional;
import javax.annotation.Resource;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
@RequestMapping("messages")
@Tag(
    name = "SWIFT Messages",
    description = "API endpoints for SWIFT message processing, including pacs.008 and pacs.009")
public class MessageController {

  @Resource MessageService messageService;

  /**
   * 银行制单员操作报文
   *
   * @param messageRequest
   * @return
   * @throws Exception
   */
  @PostMapping("bank")
  public CommonResponse<String> bankSendMessaging(
      @RequestBody @Validated({DraftValidation.class}) MessageRequest messageRequest)
      throws Exception {
    Boolean issueProcess = Optional.ofNullable(messageRequest.getInitialProcess()).orElse(false);
    if (ObjectUtils.isEmpty(messageRequest.getMessagingType())) {
      throw new BusinessException(ServerCode.INPUT_ERROR);
    }
    MessagingType messagingType = MessagingType.toMessagingType(messageRequest.getMessagingType());

    if (issueProcess) {
      return CommonResponse.success(
          messageService.submit(messageRequest.of(messagingType, false)) + "");
    }

    return CommonResponse.success(messageService.save(messageRequest.of(messagingType, true)) + "");
  }

  /**
   * 银行制单员预览草稿报文内容
   *
   * @return 返回标准格式的报文内容
   */
  @PostMapping("preview")
  @Operation(
      summary = "Preview SWIFT message",
      description = "Generate a preview of pacs.008 or pacs.009 message in the specified format")
  @ApiResponses({
    @ApiResponse(
        responseCode = "200",
        description = "Message preview generated successfully",
        content =
            @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = String.class))),
    @ApiResponse(
        responseCode = "400",
        description = "Invalid request parameters",
        content = @Content(mediaType = "application/json"))
  })
  public CommonResponse<String> previewMessages(
      @Parameter(description = "Message request containing pacs.008 or pacs.009 details")
          @RequestBody
          MessageRequest messageRequest,
      @Parameter(description = "Format of the preview (0: default, 1: XML, 2: JSON, etc.)")
          @RequestParam(name = "format", required = false, defaultValue = "0")
          Integer format)
      throws Exception {
    MessageFormat messageFormat = MessageFormat.toFormat(format);
    if (ObjectUtils.isEmpty(messageRequest.getMessagingType())) {
      throw new BusinessException(ServerCode.INPUT_ERROR);
    }
    MessagingType messagingType = MessagingType.toMessagingType(messageRequest.getMessagingType());

    return CommonResponse.success(messageRequest.getStandardFormat(messageFormat, messagingType));
  }

  /**
   * 企业制单员操作报文
   *
   * @param messageRequest
   * @return
   * @throws Exception
   */
  @PostMapping("company")
  public CommonResponse<String> companyRemittance(@RequestBody MessageRequest messageRequest)
      throws Exception {
    Boolean issueProcess = Optional.ofNullable(messageRequest.getInitialProcess()).orElse(false);
    if (issueProcess) {
      return CommonResponse.success(
          messageService.submit(messageRequest.of(MessagingType.CA103, false)) + "");
    }
    return CommonResponse.success(
        messageService.save(messageRequest.of(MessagingType.CA103, true)) + "");
  }

  /**
   * 银行制单员提交被驳回报文
   *
   * @param messageRequest
   * @return
   * @throws Exception
   */
  @PostMapping("bank/resubmit")
  public CommonResponse<String> resubmitBankMessage(@RequestBody MessageRequest messageRequest)
      throws Exception {
    if (ObjectUtils.isEmpty(messageRequest.getMessagingType())) {
      throw new BusinessException(ServerCode.INPUT_ERROR);
    }
    MessagingType messagingType = MessagingType.toMessagingType(messageRequest.getMessagingType());
    AddMessagingParam addMessagingParam = messageRequest.of(messagingType, false);
    if (ObjectUtils.isEmpty(addMessagingParam.getMessagesPo().getId())) {
      throw new BusinessException(ServerCode.INPUT_ERROR);
    }

    return CommonResponse.success(messageService.resubmit(addMessagingParam) + "");
  }

  /**
   * 银行制单员提交被驳回报文
   *
   * @param messageRequest
   * @return
   * @throws Exception
   */
  @PostMapping("company/resubmit")
  public CommonResponse<String> resubmitCompanyMessage(@RequestBody MessageRequest messageRequest)
      throws Exception {
    if (ObjectUtils.isEmpty(messageRequest.getMessagingType())) {
      throw new BusinessException(ServerCode.INPUT_ERROR);
    }

    MessagingType messagingType = MessagingType.toMessagingType(messageRequest.getMessagingType());
    AddMessagingParam addMessagingParam = messageRequest.of(messagingType, false);
    if (ObjectUtils.isEmpty(addMessagingParam.getMessagesPo().getId())) {
      throw new BusinessException(ServerCode.INPUT_ERROR);
    }

    return CommonResponse.success(messageService.resubmit(addMessagingParam) + "");
  }

  /**
   * 报文签收
   *
   * @param messageId
   * @return
   */
  @PostMapping("{messageId}/claim")
  public CommonResponse<Void> claim(@PathVariable Long messageId) {
    messageService.checkMessage(messageId);
    return CommonResponse.success();
  }

  /**
   * 获取报文详情接口
   *
   * @param messageId
   * @return
   * @throws Exception
   */
  @GetMapping("{messageId}")
  public CommonResponse<MessagingBO> detail(
      @PathVariable Long messageId,
      @RequestParam(name = "format", required = false, defaultValue = "0") Integer format)
      throws Exception {
    MessageFormat messageFormat = MessageFormat.toFormat(format);
    return CommonResponse.success(messageService.detail(messageId, messageFormat));
  }

  /**
   * 删除草稿报文接口
   *
   * @param messageId
   * @return
   * @throws Exception
   */
  @DeleteMapping("{messageId}")
  public CommonResponse<Integer> delete(@PathVariable Long messageId) throws Exception {
    messageService.deleteMessage(messageId);
    return CommonResponse.success(1);
  }

  /**
   * 获取报文列表接口
   *
   * @param messagingQueryRequest
   * @return
   */
  @PostMapping("list")
  public CommonResponse<PageData<MessagingRowBO>> messagingList(
      @RequestBody MessageQueryRequest messagingQueryRequest) {
    return CommonResponse.success(
        messageService.listByFilter(MessageQueryRequest.toParam(messagingQueryRequest)));
  }

  /**
   * 根据用户角色查询当前需要处理的报文数目
   *
   * @return
   */
  @GetMapping("stat")
  public CommonResponse<MessagingStatBO> messagingStat() {
    return CommonResponse.success(messageService.messageStat());
  }

  @PostMapping("/{messageId}/copy")
  public CommonResponse<String> copy(@PathVariable Long messageId) {
    messageService.copyMessage(messageId);
    return CommonResponse.success();
  }
}
