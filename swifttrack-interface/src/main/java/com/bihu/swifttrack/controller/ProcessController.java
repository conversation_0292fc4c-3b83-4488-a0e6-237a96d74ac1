package com.bihu.swifttrack.controller;

import com.bihu.swifttrack.common.context.UserContext;
import com.bihu.swifttrack.common.exception.BusinessException;
import com.bihu.swifttrack.common.exception.ServerCode;
import com.bihu.swifttrack.common.response.CommonResponse;
import com.bihu.swifttrack.request.ProcessRequest;
import com.bihu.swifttrack.service.ProcessService;
import com.bihu.swifttrack.vo.HistorySumVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Optional;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工作流控制器，处理工作流相关的操作
 *
 * <AUTHOR>
 * @date 2024/6/6
 */
@RestController
@RequestMapping("/workflows")
@AllArgsConstructor
@Slf4j
@Tag(name = "工作流管理", description = "工作流和流程管理相关的接口，包括审批、驳回、认领等操作")
public class ProcessController {

  private final ProcessService processService;

  /**
   * 同意审批接口
   *
   * @param messageId 消息ID
   * @param processRequest 流程请求参数
   * @return 消息ID
   */
  @Operation(summary = "同意审批", description = "对指定消息ID的流程进行同意审批操作")
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "200",
            description = "审批成功",
            content = @Content(schema = @Schema(implementation = Long.class))),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
      })
  @PostMapping("/{message_id}/agree")
  public CommonResponse<Long> agree(
      @Parameter(description = "消息ID", required = true) @PathVariable(name = "message_id")
          Long messageId,
      @Parameter(description = "流程请求参数", required = true) @RequestBody
          ProcessRequest processRequest) {
    processService.approve(messageId, Optional.ofNullable(processRequest.getNodeId()));
    return CommonResponse.success(messageId);
  }

  /**
   * 审批驳回接口
   *
   * @param messageId 消息ID
   * @param processRequest 流程请求参数
   * @return 消息ID
   */
  @Operation(summary = "审批驳回", description = "对指定消息ID的流程进行驳回操作")
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "200",
            description = "驳回成功",
            content = @Content(schema = @Schema(implementation = Long.class))),
        @ApiResponse(
            responseCode = "400",
            description = "请求参数错误",
            content = @Content(schema = @Schema(implementation = BusinessException.class))),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
      })
  @PostMapping("/{message_id}/reject")
  public CommonResponse<Long> reject(
      @Parameter(description = "消息ID", required = true) @PathVariable(name = "message_id")
          Long messageId,
      @Parameter(description = "流程请求参数，必须包含驳回原因", required = true) @RequestBody
          ProcessRequest processRequest) {
    String rejectStr = processRequest.getDescription();
    if (rejectStr == null) {
      throw new BusinessException(ServerCode.INPUT_ERROR);
    }
    processService.reject(messageId, rejectStr, Optional.ofNullable(processRequest.getNodeId()));
    return CommonResponse.success(messageId);
  }

  /**
   * 流程签收
   *
   * @param messageId 消息ID
   * @param processRequest 流程请求参数
   * @return 无返回值
   */
  @Operation(summary = "流程签收", description = "签收指定消息ID的流程节点")
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "签收成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(
            responseCode = "409",
            description = "流程已被签收",
            content = @Content(schema = @Schema(implementation = BusinessException.class)))
      })
  @PostMapping("/{message_id}/claim")
  public CommonResponse<Void> claim(
      @Parameter(description = "消息ID", required = true) @PathVariable(name = "message_id")
          Long messageId,
      @Parameter(description = "流程请求参数，包含节点ID", required = true) @RequestBody
          ProcessRequest processRequest) {
    if (!processService.claimProcessNode(messageId, processRequest.getNodeId())) {
      throw new BusinessException(ServerCode.PROCESS_ALREADY_CLAIMED);
    }
    return CommonResponse.success();
  }

  /**
   * 启动流程接口
   *
   * @param messageId 消息ID
   * @return 消息ID
   */
  @Operation(summary = "启动流程", description = "启动指定消息ID的流程")
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "200",
            description = "启动成功",
            content = @Content(schema = @Schema(implementation = Long.class))),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
      })
  @PostMapping("/{message_id}/start")
  public CommonResponse<Long> start(
      @Parameter(description = "消息ID", required = true) @PathVariable(name = "message_id")
          Long messageId) {
    processService.startProcessByBusinessKey(messageId);
    return CommonResponse.success(messageId);
  }

  /**
   * 审批历史接口
   *
   * @param messageId 消息ID
   * @return 历史汇总信息
   */
  @Operation(summary = "获取审批历史", description = "获取指定消息ID的流程审批历史记录")
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "200",
            description = "获取成功",
            content = @Content(schema = @Schema(implementation = HistorySumVO.class))),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "404", description = "流程不存在")
      })
  @GetMapping("/{message_id}/history")
  public CommonResponse<HistorySumVO> history(
      @Parameter(description = "消息ID", required = true) @PathVariable(name = "message_id")
          Long messageId) {
    return CommonResponse.success(processService.getHistory(messageId));
  }

  /**
   * 已审批消息ID集合
   *
   * @return 已审批的消息ID集合
   */
  @Operation(summary = "获取已审批消息", description = "获取当前用户已审批的所有消息ID列表")
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "200",
            description = "获取成功",
            content = @Content(schema = @Schema(implementation = Set.class))),
        @ApiResponse(responseCode = "401", description = "用户未登录")
      })
  @GetMapping("/done")
  public CommonResponse<Set<Long>> done() {
    return CommonResponse.success(processService.done(UserContext.getUserId()));
  }
}
