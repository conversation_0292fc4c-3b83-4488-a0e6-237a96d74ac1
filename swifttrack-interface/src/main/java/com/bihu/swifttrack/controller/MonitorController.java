package com.bihu.swifttrack.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 系统监控接口
 *
 * <AUTHOR>
 * @date 2021/5/17
 */
@RequestMapping("/monitor")
@RestController
@Tag(name = "系统监控", description = "系统监控和健康检查相关的接口")
public class MonitorController {

  /** 心跳检测 */
  @GetMapping("/health")
  public String monitor() {
    return "success";
  }
}
