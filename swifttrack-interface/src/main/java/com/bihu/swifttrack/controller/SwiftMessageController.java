package com.bihu.swifttrack.controller;

import com.bihu.swifttrack.common.context.UserContext;
import com.bihu.swifttrack.common.exception.ServerCode;
import com.bihu.swifttrack.common.page.PageData;
import com.bihu.swifttrack.common.response.CommonResponse;
import com.bihu.swifttrack.config.SwiftProperties;
import com.bihu.swifttrack.dto.LoginInfoDTO;
import com.bihu.swifttrack.gfin.dto.MessageResponse;
import com.bihu.swifttrack.gfin.dto.SendMessageRequest;
import com.bihu.swifttrack.request.SwiftDownloadRequest;
import com.bihu.swifttrack.request.UpdateStatusRequest;
import com.bihu.swifttrack.service.GfinProxy;
import com.bihu.swifttrack.service.SwiftMessageService;
import com.bihu.swifttrack.vo.SwiftFileVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.io.IOException;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/swift")
@RequiredArgsConstructor
@Slf4j
@Tag(
    name = "SWIFT Standard Messages",
    description = "API endpoints for standard SWIFT message processing")
public class SwiftMessageController {

  private final SwiftMessageService swiftMessageService;

  private final GfinProxy gfinProxy;

  private final SwiftProperties swiftProperties;

  /**
   * swift标准报文格式
   *
   * @param request
   * @return
   */
  @PostMapping("/download")
  @Operation(
      summary = "Download SWIFT message files",
      description = "Download SWIFT message files in the specified format")
  public ResponseEntity<byte[]> downloadBatchFiles(@RequestBody SwiftDownloadRequest request) {
    SwiftFileVO fileVO =
        swiftMessageService.download(request.getMessageId(), request.getMessageFormat());
    // 设置响应头
    HttpHeaders headers = new HttpHeaders();
    headers.add(
        HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileVO.getFileName() + ".txt");
    // 返回文件内容
    return ResponseEntity.ok()
        .headers(headers)
        .contentType(MediaType.TEXT_PLAIN)
        .body(fileVO.getContent());
  }

  @GetMapping("/messages/{messageId}")
  @Operation(
      summary = "Get SWIFT message by ID",
      description = "Retrieve a specific SWIFT message by its ID")
  public ResponseEntity<CommonResponse<MessageResponse>> getMessage(
      @PathVariable String messageId) {
    try {
      MessageResponse message = gfinProxy.getMessage(messageId);
      return ResponseEntity.ok(CommonResponse.success(message));
    } catch (IOException e) {
      log.error("Failed to get message", e);
      return ResponseEntity.status(HttpStatus.OK)
          .body(CommonResponse.error(ServerCode.SERVER_ERROR, "Failed to get message"));
    }
  }

  @PostMapping("/messages")
  @Operation(
      summary = "Send SWIFT message",
      description = "Send a new SWIFT message with the specified content and format")
  public ResponseEntity<CommonResponse<Void>> sendMessage(@RequestBody SendMessageRequest request) {
    try {
      gfinProxy.sendMessage(request.getContent(), request.getMsgFormat());
      return ResponseEntity.ok(CommonResponse.success());
    } catch (IOException e) {
      log.error("Failed to send message", e);
      return ResponseEntity.status(HttpStatus.OK)
          .body(
              CommonResponse.error(
                  ServerCode.SERVER_ERROR, "Failed to send message " + e.getMessage()));
    }
  }

  @GetMapping("/messages")
  @Operation(
      summary = "List SWIFT messages",
      description = "Retrieve a paginated list of SWIFT messages with optional filtering")
  public ResponseEntity<CommonResponse<PageData<MessageResponse>>> getMessages(
      @RequestParam(required = false) String messageType,
      @RequestParam(required = false) String receiver,
      @RequestParam(required = false) String sender,
      @RequestParam(required = false) String status,
      @RequestParam(defaultValue = "1") int page,
      @RequestParam(defaultValue = "10") int size) {
    try {
      LoginInfoDTO user = UserContext.getLoginUser();
      if (StringUtils.isBlank(receiver)) {
        receiver = getBankReceiver(user.getEmail());
      }
      PageData<MessageResponse> messages =
          gfinProxy.getMessages(messageType, receiver, sender, status, page - 1, size);
      return ResponseEntity.ok(CommonResponse.success(messages));
    } catch (IOException e) {
      log.error("Failed to get messages", e);
      return ResponseEntity.status(HttpStatus.OK)
          .body(CommonResponse.error(ServerCode.SERVER_ERROR, "Failed to get messages"));
    }
  }

  private String getBankReceiver(String email) {
    return swiftProperties.getBankReceivers().entrySet().stream()
        .filter(entry -> email.endsWith(entry.getKey()))
        .map(Map.Entry::getValue)
        .findFirst()
        .orElse(null);
  }

  @PostMapping("/messages/{messageId}/status")
  public ResponseEntity<CommonResponse<MessageResponse>> updateMessageStatus(
      @PathVariable String messageId, @RequestBody UpdateStatusRequest request) {
    try {
      MessageResponse updatedMessage =
          gfinProxy.updateMessageStatus(messageId, request.getNewStatus());
      return ResponseEntity.ok(CommonResponse.success(updatedMessage));
    } catch (IOException e) {
      log.error("Failed to update message status", e);
      return ResponseEntity.status(HttpStatus.OK)
          .body(CommonResponse.error(ServerCode.SERVER_ERROR, "Failed to update message status"));
    }
  }
}
