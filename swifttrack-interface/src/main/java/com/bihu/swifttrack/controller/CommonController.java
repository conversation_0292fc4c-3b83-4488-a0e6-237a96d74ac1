package com.bihu.swifttrack.controller;

import com.bihu.swifttrack.common.constants.Constants.Yes1No0;
import com.bihu.swifttrack.common.response.CommonResponse;
import com.bihu.swifttrack.dto.MaintainDTO;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * CommonController
 *
 * <p>
 *
 * <AUTHOR>
 * @date 2024/11/21
 */
@RestController
@RequestMapping("/common")
@AllArgsConstructor
public class CommonController {

  /**
   * 获取维护时间
   *
   * @return CommonResponse
   */
  @GetMapping("maintain")
  public CommonResponse<MaintainDTO> getMaintain() {
    MaintainDTO vo = new MaintainDTO();
    vo.setIsMaintain(Yes1No0.NO);
    Map<String, String> message = new HashMap<String, String>();
    message.put("zh",
        "系统将于2025年04月24日18:00-23:00进行功能升级维护, 新增 pacs.008和 pacs.009报文类型支持，期间融联易云报文通信通道暂时关闭。给您带来的不便，敬请谅解");
    message.put("en",
        "The system will undergo a functional upgrade and maintenance from 18:00 to 23:00 on April 24, 2025. Support for pacs.008 and pacs.009 message types will be added. During this period, the Ronglian EasyCloud messaging channel will be temporarily closed. We apologize for any inconvenience caused and appreciate your understanding.");
    message.put("ru",
        "Система будет проходить функциональное обновление и техническое обслуживание 24 апреля 2025 года с 18:00 до 23:00. Будет добавлена поддержка типов сообщений pacs.008 и pacs.009. В этот период канал обмена сообщениями Ronglian EasyCloud будет временно закрыт. Приносим извинения за возможные неудобства и благодарим за понимание.");
    vo.setMessage(message);
    return CommonResponse.success(vo);
  }


}
