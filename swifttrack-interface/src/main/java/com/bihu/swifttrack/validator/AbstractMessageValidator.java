package com.bihu.swifttrack.validator;

import com.bihu.swifttrack.common.context.HeaderContext;
import com.bihu.swifttrack.common.exception.ServerCode;
import com.bihu.swifttrack.request.message.mx.Account;
import com.bihu.swifttrack.request.message.mx.Agent;
import com.bihu.swifttrack.request.message.mx.BusinessApplicationHeader;
import com.bihu.swifttrack.request.message.mx.CamtGroupHeader;
import com.bihu.swifttrack.request.message.mx.ChargesInformation;
import com.bihu.swifttrack.request.message.mx.GroupHeader;
import com.bihu.swifttrack.request.message.mx.Other;
import com.bihu.swifttrack.request.message.mx.Party;
import com.bihu.swifttrack.request.message.mx.PaymentIdentification;
import com.bihu.swifttrack.request.message.mx.PreviousInstrictingInfo;
import com.bihu.swifttrack.request.message.mx.ReportEntry10;
import com.bihu.swifttrack.request.message.mx.SettlementTimeIndication;
import com.bihu.swifttrack.request.message.mx.TypedValue;
import com.bihu.swifttrack.swift.SwiftConstants;
import com.bihu.swifttrack.utils.CbprValidator;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.regex.Pattern;
import org.apache.commons.lang3.StringUtils;

/**
 * AbstractMessageValidator
 *
 * <p>
 *
 * <AUTHOR>
 * @date 2024/11/06
 */
public abstract class AbstractMessageValidator<T> {

  private static final Pattern UETR_PATTERN =
      Pattern.compile(
          "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$");

  public final ValidationResult validate(T message) {
    ValidationResult result = new ValidationResult();
    if (message == null) {
      result.addError("message", "Message cannot be null");
      return result;
    }
    doValidate(message, result);
    return result;
  }

  protected abstract void doValidate(T message, ValidationResult result);

  // Common validation methods
  protected void validateBIC(
      String bic, String fieldName, String pattern, ValidationResult result) {
    if (StringUtils.isBlank(bic) || !bic.matches(pattern)) {
      result.addError(fieldName, ServerCode.VALIDATION_BIC_INVALID, bic);
    }
  }

  protected void validateIBAN(String iban, String fieldName, ValidationResult result) {
    if (StringUtils.isBlank(iban) || !iban.matches("^[A-Z]{2}[0-9]{2}[A-Z0-9]{1,30}$")) {
      result.addError(fieldName, ServerCode.VALIDATION_IBAN_INVALID, iban);
    }
  }

  protected void validateCurrency(String currency, String fieldName, ValidationResult result) {
    if (StringUtils.isBlank(currency) || !currency.matches("^[A-Z]{3}$")) {
      result.addError(
          fieldName, ServerCode.VALIDATION_CURRENCY_INVALID.getDesc(HeaderContext.getLanguage()));
    }
  }

  protected void validateAmount(BigDecimal amount, String fieldName, ValidationResult result) {
    if (amount == null || amount.compareTo(BigDecimal.ZERO) >= 0) {
      return;
    }
    result.addError(
        fieldName, ServerCode.VALIDATION_AMOUNT_INVALID.getDesc(HeaderContext.getLanguage()));
  }

  protected void validateNotBlank(String value, String fieldName, ValidationResult result) {
    if (StringUtils.isBlank(value)) {
      result.addError(
          fieldName, ServerCode.VALIDATION_FIELD_BLANK.getDesc(HeaderContext.getLanguage()));
    }
  }

  protected void validateNotNull(Object value, String fieldName, ValidationResult result) {
    if (value == null) {
      result.addError(
          fieldName, ServerCode.VALIDATION_FIELD_NULL.getDesc(HeaderContext.getLanguage()));
    }
  }

  protected void validateLength(
      String value, int minLength, int maxLength, String fieldName, ValidationResult result) {
    if (value == null) {
      return;
    }
    if (StringUtils.isBlank(value) || value.length() < minLength || value.length() > maxLength) {
      result.addError(fieldName, ServerCode.VALIDATION_LENGTH_INVALID, minLength, maxLength);
    }
  }

  protected void validateBusinessApplicationHeader(
      BusinessApplicationHeader header, ValidationResult result) {
    validateNotNull(header, "BusinessApplicationHeader", result);
    if (header == null) {
      return;
    }
    validateCbpr(header, result);
    validateBIC(header.getFrom(), "From BIC", SwiftConstants.BIC_PATTERN, result);
    validateBIC(header.getTo(), "To BIC", SwiftConstants.BIC_PATTERN, result);
    validateLength(header.getBizMsgIdr(), 1, 35, "Business Message Identifier", result);
    validateLength(header.getMsgDefIdr(), 1, 35, "Message Definition Identifier", result);
    validateNotNull(header.getCreDt(), "Creation Date", result);
  }

  protected void validateCamtGroupHeader(CamtGroupHeader header, ValidationResult result) {
    validateNotNull(header, "CamtGroupHeader", result);
    if (header == null) {
      return;
    }
    validateCbpr(header, result);
    validateLength(header.getMsgId(), 1, 35, "Message Identifier", result);
    validateNotNull(header.getCreDtTm(), "Creation Date Time", result);

    // Validate optional pagination if present
    Optional.ofNullable(header.getMsgPgntn())
        .ifPresent(
            pagination -> {
              validateNotBlank(pagination.getPgNb(), "Page Number", result);
              validateNotNull(pagination.getLastPgInd(), "Last Page Indicator", result);
            });
  }

  protected void validatePacsGroupHeader(GroupHeader header, ValidationResult result) {
    validateNotNull(header, "GroupHeader", result);
    if (header == null) {
      return;
    }
    validateLength(header.getMsgId(), 1, 35, "Message Identifier", result);
    validateNotNull(header.getCreDtTm(), "Creation Date Time", result);
  }

  protected void validateSettlementTimeIndication(
      SettlementTimeIndication sttlmTmIndctn, ValidationResult result) {
    validateNotNull(sttlmTmIndctn, "Settlement Time Indication", result);

    if (sttlmTmIndctn != null) {
      // 如果两个日期时间都为空，则报错
      if (sttlmTmIndctn.getDbtDtTm() == null && sttlmTmIndctn.getCdtDtTm() == null) {
        throw new IllegalArgumentException(
            "Settlement time indication must have at least one of debit date time or credit date"
                + " time");
      }

      // 如果两个日期时间都不为空，则检查借记日期时间是否早于贷记日期时间
      if (sttlmTmIndctn.getDbtDtTm() != null && sttlmTmIndctn.getCdtDtTm() != null) {
        if (sttlmTmIndctn.getDbtDtTm().isAfter(sttlmTmIndctn.getCdtDtTm())) {
          throw new IllegalArgumentException("Debit date time must be before credit date time");
        }
      }
    }
  }

  protected void validateAccount(Account account, ValidationResult result) {
    if (account == null) {
      return;
    }
    // Validate IBAN if present
    if (StringUtils.isNotBlank(account.getIban())) {
      validateIBAN(account.getIban(), "Account IBAN", result);
    }

    // Validate Other account identification if present
    if (account.getOther() != null) {
      validateOtherAccountIdentification(account.getOther(), result);
    }

    // Validate currency if present
    if (StringUtils.isNotBlank(account.getCcy())) {
      validateCurrency(account.getCcy(), "Account Currency", result);
    }

    // Validate account servicer if present
    if (account.getSvcr() != null) {
      validateAgent(account.getSvcr(), "Account Servicer", result);
    }
  }

  protected void validateOtherAccountIdentification(Other other, ValidationResult result) {
    if (other == null) {
      return;
    }
    validateNotNull(other, "Other Account Identification", result);
    validateNotBlank(other.getId(), "Other Account ID", result);

    // Validate scheme name
    validateNotNull(other.getSchmeNm(), "Scheme Name", result);
    validateNotNull(other.getSchmeNm().getType(), "Scheme Name Type", result);
    validateNotBlank(other.getSchmeNm().getValue(), "Scheme Name Value", result);

    // Validate scheme name type
    if (other.getSchmeNm().getType() == TypedValue.ValueType.CODE) {
      validateLength(other.getSchmeNm().getValue(), 1, 4, "Scheme Name Code", result);
    } else {
      validateLength(other.getSchmeNm().getValue(), 1, 35, "Scheme Name Proprietary", result);
    }

    // Validate issuer if present
    if (StringUtils.isNotBlank(other.getIssr())) {
      validateLength(other.getIssr(), 1, 35, "Issuer", result);
    }
  }

  protected void validateParty(Party party, String partyType, ValidationResult result) {
    if (party == null) {
      return;
    }
    validateNotNull(party, partyType, result);
    validateNotBlank(party.getNm(), partyType + " Name", result);
    validateNotBlank(party.getAcctId(), partyType + " Account ID", result);
  }

  protected void validateAgent(Agent agent, String agentType, ValidationResult result) {
    if (agent == null) {
      return;
    }
    validateBIC(agent.getBicfi(), agentType + " BICFI", SwiftConstants.COMMON_BIC_PATTERN, result);
  }

  protected void validateChargeBearer(
      String chargeBearer, List<ChargesInformation> chargsInf, ValidationResult result) {
    validateNotNull(chargeBearer, "Charge Bearer", result);
    if (!Arrays.asList("DEBT", "CRED", "SHAR", "SLEV").contains(chargeBearer)) {
      result.addError("chargeBearer", ServerCode.VALIDATION_CHARGE_BEARER_INVALID, chargeBearer);
    }
    if ("CRED".equals(chargeBearer)) {
      validateNotNull(chargsInf, "Charges Information", result);
    }
  }

  protected void validatePaymentIdentification(
      PaymentIdentification pmtId, ValidationResult result) {
    validateNotNull(pmtId, "Payment Identification", result);
    if (pmtId == null) {
      return;
    }
    validateLength(pmtId.getInstrId(), 1, 35, "Instruction Identification", result);
    validateLength(pmtId.getEndToEndId(), 1, 35, "End To End Identification", result);
    if (StringUtils.isNotBlank(pmtId.getTxId())) {
      validateLength(pmtId.getTxId(), 1, 35, "Transaction Identification", result);
    }

    if (pmtId.getUetr() != null) {
      validateUETR(pmtId.getUetr(), result);
    }
  }

  protected void validateUETR(String uetr, ValidationResult result) {
    if (!UETR_PATTERN.matcher(uetr).matches()) {
      result.addError("UETR", ServerCode.VALIDATION_UETR_INVALID, uetr);
    }
  }

  protected void validateReportEntry(ReportEntry10 entry, ValidationResult result) {
    validateNotNull(entry, "Report Entry", result);
    if (entry == null) {
      return;
    }
    validateNotNull(entry.getAmt(), "Entry Amount", result);
    validateAmount(entry.getAmt().getValue(), "Entry Amount", result);
    validateCurrency(entry.getAmt().getCcy(), "Entry Currency", result);
    validateNotNull(entry.getCdtDbtInd(), "Entry Credit Debit Indicator", result);
    validateNotNull(entry.getSts(), "Entry Status", result);
    // Validate booking date if present
    Optional.ofNullable(entry.getBookgDt())
        .ifPresent(
            date ->
                validateNotNull(
                    date.getDt() != null || date.getDtTm() != null,
                    "Booking Date or DateTime",
                    result));

    // Validate value date if present
    Optional.ofNullable(entry.getValDt())
        .ifPresent(
            date ->
                validateNotNull(
                    date.getDt() != null || date.getDtTm() != null,
                    "Value Date or DateTime",
                    result));
  }

  protected void validatePreviousInfos(
      List<PreviousInstrictingInfo> previousInstrictingInfos, ValidationResult result) {
    // 验证前置指示行信息
    if (previousInstrictingInfos != null) {
      for (PreviousInstrictingInfo info : previousInstrictingInfos) {
        if ((info.getPrvsInstgAgt() == null && info.getPrvsInstgAgtAcct() != null)
            || (info.getPrvsInstgAgt() != null && info.getPrvsInstgAgtAcct() == null)) {
          result.addError(
              "Previous Instricting Info",
              ServerCode.VALIDATION_FIELD_INVALID,
              "Agent and Agent Account must both be present");
        }
        if (info.getPrvsInstgAgtAcct() != null && info.getPrvsInstgAgtAcct().getIban() != null) {
          validateIBAN(info.getPrvsInstgAgtAcct().getIban(), "previous agent account", result);
        }
      }
    }
  }

  protected void validateCbpr(Object request, ValidationResult result) {
    CbprValidator.validate(request, result);
  }
}
