package com.bihu.swifttrack.validator.mx;

import com.bihu.swifttrack.common.exception.ServerCode;
import com.bihu.swifttrack.request.message.mx.MXPacs009RequestInfo;
import com.bihu.swifttrack.request.message.mx.MXPacs009RequestInfo.CreditTransferTransactionInformation36;
import com.bihu.swifttrack.validator.AbstractMessageValidator;
import com.bihu.swifttrack.validator.ValidationResult;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * Pacs009Validator
 *
 * <p>
 *
 * <AUTHOR>
 * @date 2024/11/06
 */
public class Pacs009Validator extends AbstractMessageValidator<MXPacs009RequestInfo> {

  @Override
  protected void doValidate(MXPacs009RequestInfo message, ValidationResult result) {
    validateBusinessApplicationHeader(message.getBusinessApplicationHeader(), result);
    validatePacsGroupHeader(message.getGroupHeader(), result);
    validateCreditTransferTransactionInformation(
        Collections.singletonList(message.getCreditTransferTransactionInformation()), result);
  }

  private void validateCreditTransferTransactionInformation(
      List<CreditTransferTransactionInformation36> transactionInformation, ValidationResult result) {

    validateNotNull(transactionInformation, "Credit Transfer Transaction Information", result);
    if (transactionInformation.isEmpty()) {
      result.addError("Credit Transfer Transaction Information", ServerCode.VALIDATION_LIST_EMPTY);
      return;
    }

    for (CreditTransferTransactionInformation36 ti : transactionInformation) {
      validateTransactionInformation(ti, result);
    }
  }

  private void validateTransactionInformation(
      CreditTransferTransactionInformation36 ti, ValidationResult result) {
    validateCbpr(ti, result);

    // 验证支付标识
    validatePaymentIdentification(ti.getPmtId(), result);

    // 验证结算金额和日期
    validateNotNull(ti.getIntrBkSttlmAmt(), "Interbank Settlement Amount", result);
    validateAmount(ti.getIntrBkSttlmAmt().getAmount(), "Interbank Settlement Amount", result);
    validateCurrency(ti.getIntrBkSttlmAmt().getCurrency(), "Interbank Settlement Currency", result);
    validateNotNull(ti.getIntrBkSttlmDt(), "Interbank Settlement Date", result);

    // 验证代理信息
    validateAgent(ti.getDbtrAgt(), "Debtor Agent", result);
    validateAgent(ti.getCdtrAgt(), "Creditor Agent", result);

    // 验证交易方信息
    validateAgent(ti.getDebtor(), "Debtor", result);
    validateAgent(ti.getCreditor(), "Creditor", result);

    Optional.ofNullable(ti.getPurp()).ifPresent(purp -> purp.validate("Purp for pacs.009", result));

    // 验证汇款信息
    if (ti.getRmtInf() != null) {
      ti.getRmtInf()
          .forEach(info -> validateLength(info, 1, 140, "Remittance Information", result));
    }

    validatePreviousInfos(ti.getPreviousInstrictingInfos(), result);
  }


}
