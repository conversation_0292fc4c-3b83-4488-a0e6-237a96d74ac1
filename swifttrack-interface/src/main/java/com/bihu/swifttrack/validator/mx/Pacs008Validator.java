package com.bihu.swifttrack.validator.mx;

import com.bihu.swifttrack.common.exception.ServerCode;
import com.bihu.swifttrack.request.message.mx.MXPacs008RequestInfo;
import com.bihu.swifttrack.request.message.mx.MXPacs008RequestInfo.CreditTransferTransactionInformation39;
import com.bihu.swifttrack.validator.AbstractMessageValidator;
import com.bihu.swifttrack.validator.ValidationResult;
import java.util.Arrays;
import java.util.List;

/**
 * Pacs008Validator
 *
 * <p>
 *
 * <AUTHOR>
 * @date 2024/11/06
 */
public class Pacs008Validator extends AbstractMessageValidator<MXPacs008RequestInfo> {

  @Override
  protected void doValidate(MXPacs008RequestInfo message, ValidationResult result) {
    validateBusinessApplicationHeader(message.getBusinessApplicationHeader(), result);
    validatePacsGroupHeader(message.getGroupHeader(), result);
    validateCreditTransferTransactionInformation(
        Arrays.asList(message.getCreditTransferTransactionInformation()), result);
  }

  private void validateCreditTransferTransactionInformation(
      List<CreditTransferTransactionInformation39> transactionInformation, ValidationResult result) {

    validateNotNull(transactionInformation, "Credit Transfer Transaction Information", result);
    if (transactionInformation.isEmpty()) {
      result.addError("Credit Transfer Transaction Information", ServerCode.VALIDATION_LIST_EMPTY);
      return;
    }

    for (CreditTransferTransactionInformation39 ti : transactionInformation) {
      validateTransactionInformation(ti, result);
    }
  }

  private void validateTransactionInformation(
      CreditTransferTransactionInformation39 ti, ValidationResult result) {

    validateNotNull(ti, "Credit Transfer Transaction Information", result);

    validateCbpr(ti, result);

    // 验证支付标识
    validatePaymentIdentification(ti.getPmtId(), result);

    // 验证结算金额和日期
    validateNotNull(ti.getIntrBkSttlmAmt(), "Interbank Settlement Amount", result);
    validateAmount(ti.getIntrBkSttlmAmt().getAmount(), "Interbank Settlement Amount", result);
    validateCurrency(ti.getIntrBkSttlmAmt().getCurrency(), "Interbank Settlement Currency", result);

    validateChargeBearer(ti.getChrgBr(), ti.getChrgsInf(), result);

    // 验证交易方信息
    validateParty(ti.getDebtor(), "Debtor", result);
    validateAgent(ti.getDbtrAgt(), "Debtor Agent", result);
    validateAgent(ti.getCdtrAgt(), "Creditor Agent", result);
    validateParty(ti.getCreditor(), "Creditor", result);

    validatePreviousInfos(ti.getPreviousInstrictingInfos(), result);

    // 验证汇款信息
    if (ti.getRmtInf() != null) {
      ti.getRmtInf()
          .forEach(info -> validateLength(info, 1, 140, "Remittance Information", result));
    }
  }
}
