package com.bihu.swifttrack.request.message.fillers.mx;

import static com.bihu.swifttrack.utils.FieldUtils.setFieldToMessageDetails;

import com.bihu.swifttrack.mybatis.repository.po.MessageDetailsPO;
import com.bihu.swifttrack.request.message.MessageContentFiller;
import com.bihu.swifttrack.request.message.MessageRequest;
import com.bihu.swifttrack.request.message.mx.CreditTransferTransaction37;
import com.bihu.swifttrack.request.message.mx.GroupHeader;
import com.bihu.swifttrack.request.message.mx.MXPacs009RequestInfo;
import com.bihu.swifttrack.request.message.mx.MXPacs009RequestInfo.CreditTransferTransactionInformation36;
import com.bihu.swifttrack.request.message.mx.Party;
import com.bihu.swifttrack.request.message.mx.PreviousInstrictingInfo;
import com.bihu.swifttrack.request.message.mx.SettlementTimeIndication;
import com.bihu.swifttrack.request.message.mx.TypedValue;
import com.bihu.swifttrack.utils.MXMessageValidator;
import com.prowidesoftware.swift.model.MxSwiftMessage;
import com.prowidesoftware.swift.model.mx.MxPacs00900108;
import com.prowidesoftware.swift.model.mx.dic.AccountIdentification4Choice;
import com.prowidesoftware.swift.model.mx.dic.BranchAndFinancialInstitutionIdentification6;
import com.prowidesoftware.swift.model.mx.dic.CashAccount38;
import com.prowidesoftware.swift.model.mx.dic.CreditTransferTransaction36;
import com.prowidesoftware.swift.model.mx.dic.FinancialInstitutionCreditTransferV08;
import com.prowidesoftware.swift.model.mx.dic.FinancialInstitutionIdentification18;
import com.prowidesoftware.swift.model.mx.dic.GenericAccountIdentification1;
import com.prowidesoftware.swift.model.mx.dic.GroupHeader93;
import com.prowidesoftware.swift.model.mx.dic.Instruction4Code;
import com.prowidesoftware.swift.model.mx.dic.Instruction5Code;
import com.prowidesoftware.swift.model.mx.dic.InstructionForCreditorAgent2;
import com.prowidesoftware.swift.model.mx.dic.InstructionForNextAgent1;
import com.prowidesoftware.swift.model.mx.dic.Purpose2Choice;
import com.prowidesoftware.swift.model.mx.dic.RemittanceInformation2;
import com.prowidesoftware.swift.model.mx.dic.SettlementDateTimeIndication1;
import com.prowidesoftware.swift.model.mx.dic.SettlementInstruction7;
import com.prowidesoftware.swift.model.mx.dic.SettlementTimeRequest2;
import java.time.OffsetTime;
import java.time.ZoneOffset;
import java.util.List;
import org.springframework.util.CollectionUtils;

/**
 * MXPacs008ContentFiller
 *
 * <p>
 *
 * <AUTHOR>
 * @date 2024/10/08
 */
public class MXPacs009ContentFiller implements MessageContentFiller {

  @Override
  public void fillContent(List<MessageDetailsPO> lists, MessageRequest messageRequest)
      throws Exception {
    if (lists == null) {
      throw new IllegalArgumentException("MessageDetailsPO list cannot be null");
    }
    if (messageRequest == null) {
      throw new IllegalArgumentException("MessageRequest cannot be null");
    }

    MxPacs00900108 mxPacs009 = new MxPacs00900108();
    MXPacs009RequestInfo requestInfo = messageRequest.getMxPacs009RequestInfo();

    MXMessageValidator.validateMXPacs009RequestInfo(requestInfo);
    fillMxPacs009(mxPacs009, requestInfo);

    MxSwiftMessage mxSwiftMessage =
        new MxSwiftMessage(
            mxPacs009.message(MxHelper.getWriteConfiguration(null, null, false)));

    setFieldToMessageDetails("mxPacs009", mxSwiftMessage.message(), lists);
  }

  private void fillMxPacs009(MxPacs00900108 mxPacs009, MXPacs009RequestInfo requestInfo) {
    mxPacs009.setAppHdr(
        BusinessApplicationHeaderConverter.convert(requestInfo.getBusinessApplicationHeader()));

    FinancialInstitutionCreditTransferV08 creditTransfer =
        createFinancialInstitutionCreditTransfer(requestInfo);

    // 设置消息级别的补充数据
    if (requestInfo.getSplmtryData() != null && !requestInfo.getSplmtryData().isEmpty()) {
      requestInfo
          .getSplmtryData()
          .forEach(
              splmtryData -> {
                com.prowidesoftware.swift.model.mx.dic.SupplementaryData1 supplementaryData =
                    new com.prowidesoftware.swift.model.mx.dic.SupplementaryData1();
                supplementaryData.setPlcAndNm(splmtryData.getPlcAndNm());

                // 如果有信封数据，可以在这里设置
                if (splmtryData.getEnvelope() != null) {
                  com.prowidesoftware.swift.model.mx.dic.SupplementaryDataEnvelope1 envelope =
                      new com.prowidesoftware.swift.model.mx.dic.SupplementaryDataEnvelope1();
                  supplementaryData.setEnvlp(envelope);
                }

                creditTransfer.addSplmtryData(supplementaryData);
              });
    }

    mxPacs009.setFICdtTrf(creditTransfer);
  }

  private FinancialInstitutionCreditTransferV08 createFinancialInstitutionCreditTransfer(
      MXPacs009RequestInfo requestInfo) {
    return new FinancialInstitutionCreditTransferV08()
        .setGrpHdr(createGroupHeader(requestInfo.getGroupHeader()))
        .addCdtTrfTxInf(
            createCreditTransferTransactionInformation(
                requestInfo.getCreditTransferTransactionInformation()));
  }

  private GroupHeader93 createGroupHeader(GroupHeader gh) {
    return new GroupHeader93()
        .setMsgId(gh.getMsgId())
        .setCreDtTm(MxHelper.convertToOffsetDateTime(gh.getCreDtTm()))
        .setNbOfTxs(gh.getNbOfTxs())
        .setSttlmInf(createSettlementInstruction(gh));
  }

  private SettlementInstruction7 createSettlementInstruction(GroupHeader groupHeader) {
    if (groupHeader.getSttlmInf() == null) {
      return null;
    }
    return MxHelper.createSettlementInstruction(groupHeader.getSttlmInf());
  }

  private CreditTransferTransaction36 createCreditTransferTransactionInformation(
      CreditTransferTransactionInformation36 ctti) {
    CreditTransferTransaction36 cdtTrfTxInf =
        new CreditTransferTransaction36()
            .setPmtId(MxHelper.createPaymentIdentification(ctti.getPmtId()))
            .setPmtTpInf(MxHelper.createPaymentTypeInformation(ctti.getPmtTpInf()))
            .setIntrBkSttlmAmt(MxHelper.createActiveCurrencyAndAmount(ctti.getIntrBkSttlmAmt()))
            .setIntrBkSttlmDt(ctti.getIntrBkSttlmDt())
            .setDbtr(MxHelper.createFinancialInstitution(ctti.getDebtor()))
            .setDbtrAcct(MxHelper.createCashAccount(ctti.getDbtrAcct()))
            .setDbtrAgt(MxHelper.createFinancialInstitution(ctti.getDbtrAgt()))
            .setCdtr(MxHelper.createFinancialInstitution(ctti.getCreditor()))
            .setCdtrAcct(MxHelper.createCashAccount(ctti.getCdtrAcct()))
            .setCdtrAgt(MxHelper.createFinancialInstitution(ctti.getCdtrAgt()))
            .setRmtInf(createRemittanceInformation(ctti.getRmtInf()))
            .setPurp(createPurpose(ctti.getPurp()));

    setOptionalFields(cdtTrfTxInf, ctti);
    setAgentsInformation(cdtTrfTxInf, ctti);
    setSettlementInformation(cdtTrfTxInf, ctti);

    return cdtTrfTxInf;
  }

  private void setOptionalFields(
      CreditTransferTransaction36 cdtTrfTxInf,
      CreditTransferTransactionInformation36 ctti) {
    if (ctti.getInstgAgt() != null) {
      cdtTrfTxInf.setInstgAgt(MxHelper.createFinancialInstitution(ctti.getInstgAgt()));
    }
    if (ctti.getInstdAgt() != null) {
      cdtTrfTxInf.setInstdAgt(MxHelper.createFinancialInstitution(ctti.getInstdAgt()));
    }

    // 设置最终债务人
    if (ctti.getUltmtDbtr() != null) {
      cdtTrfTxInf.setUltmtDbtr(MxHelper.createFinancialInstitution(ctti.getUltmtDbtr()));
    }

    // 设置最终债权人
    if (ctti.getUltmtCdtr() != null) {
      cdtTrfTxInf.setUltmtCdtr(MxHelper.createFinancialInstitution(ctti.getUltmtCdtr()));
    }

    // 设置债务人代理账户
    if (ctti.getDbtrAgtAcct() != null) {
      cdtTrfTxInf.setDbtrAgtAcct(MxHelper.createCashAccount(ctti.getDbtrAgtAcct()));
    }

    // 设置债权人代理账户
    if (ctti.getCdtrAgtAcct() != null) {
      cdtTrfTxInf.setCdtrAgtAcct(MxHelper.createCashAccount(ctti.getCdtrAgtAcct()));
    }

    // 设置交易级别的补充数据
    if (ctti.getSplmtryData() != null && !ctti.getSplmtryData().isEmpty()) {
      ctti.getSplmtryData()
          .forEach(
              splmtryData -> {
                com.prowidesoftware.swift.model.mx.dic.SupplementaryData1 supplementaryData =
                    new com.prowidesoftware.swift.model.mx.dic.SupplementaryData1();
                supplementaryData.setPlcAndNm(splmtryData.getPlcAndNm());

                // 如果有信封数据，可以在这里设置
                if (splmtryData.getEnvelope() != null) {
                  com.prowidesoftware.swift.model.mx.dic.SupplementaryDataEnvelope1 envelope =
                      new com.prowidesoftware.swift.model.mx.dic.SupplementaryDataEnvelope1();
                  supplementaryData.setEnvlp(envelope);
                }

                cdtTrfTxInf.addSplmtryData(supplementaryData);
              });
    }

    // 设置底层客户信用转账
    if (ctti.getUndrlygCstmrCdtTrf() != null) {
      cdtTrfTxInf.setUndrlygCstmrCdtTrf(
          createUnderlyingCustomerCreditTransfer(ctti.getUndrlygCstmrCdtTrf()));
    }

    setInstructionsForAgents(cdtTrfTxInf, ctti);
  }

  private com.prowidesoftware.swift.model.mx.dic.CreditTransferTransaction37
  createUnderlyingCustomerCreditTransfer(CreditTransferTransaction37 undrlygCstmrCdtTrf) {
    com.prowidesoftware.swift.model.mx.dic.CreditTransferTransaction37 result =
        new com.prowidesoftware.swift.model.mx.dic.CreditTransferTransaction37();

    // 设置最终债务人
    if (undrlygCstmrCdtTrf.getUltmtDbtr() != null) {
      result.setUltmtDbtr(MxHelper.createPartyIdentification(undrlygCstmrCdtTrf.getUltmtDbtr()));
    }

    // 设置发起方
    if (undrlygCstmrCdtTrf.getInitgPty() != null) {
      result.setInitgPty(MxHelper.createPartyIdentification(undrlygCstmrCdtTrf.getInitgPty()));
    }

    // 设置债务人
    if (undrlygCstmrCdtTrf.getDbtr() != null) {
      result.setDbtr(MxHelper.createPartyIdentification(undrlygCstmrCdtTrf.getDbtr()));
    }

    // 设置债务人账户
    if (undrlygCstmrCdtTrf.getDbtrAcct() != null) {
      result.setDbtrAcct(MxHelper.createCashAccount(undrlygCstmrCdtTrf.getDbtrAcct()));
    }

    // 设置债务人代理机构
    if (undrlygCstmrCdtTrf.getDbtrAgt() != null) {
      result.setDbtrAgt(MxHelper.createFinancialInstitution(undrlygCstmrCdtTrf.getDbtrAgt()));
    }

    // 设置债务人代理账户
    if (undrlygCstmrCdtTrf.getDbtrAgtAcct() != null) {
      result.setDbtrAgtAcct(MxHelper.createCashAccount(undrlygCstmrCdtTrf.getDbtrAgtAcct()));
    }

    // 设置前置指示代理机构和账户
    if (undrlygCstmrCdtTrf.getPrvsInstgAgt1() != null) {
      result.setPrvsInstgAgt1(
          MxHelper.createFinancialInstitution(undrlygCstmrCdtTrf.getPrvsInstgAgt1()));
    }
    if (undrlygCstmrCdtTrf.getPrvsInstgAgt1Acct() != null) {
      result.setPrvsInstgAgt1Acct(
          MxHelper.createCashAccount(undrlygCstmrCdtTrf.getPrvsInstgAgt1Acct()));
    }

    if (undrlygCstmrCdtTrf.getPrvsInstgAgt2() != null) {
      result.setPrvsInstgAgt2(
          MxHelper.createFinancialInstitution(undrlygCstmrCdtTrf.getPrvsInstgAgt2()));
    }
    if (undrlygCstmrCdtTrf.getPrvsInstgAgt2Acct() != null) {
      result.setPrvsInstgAgt2Acct(
          MxHelper.createCashAccount(undrlygCstmrCdtTrf.getPrvsInstgAgt2Acct()));
    }

    if (undrlygCstmrCdtTrf.getPrvsInstgAgt3() != null) {
      result.setPrvsInstgAgt3(
          MxHelper.createFinancialInstitution(undrlygCstmrCdtTrf.getPrvsInstgAgt3()));
    }
    if (undrlygCstmrCdtTrf.getPrvsInstgAgt3Acct() != null) {
      result.setPrvsInstgAgt3Acct(
          MxHelper.createCashAccount(undrlygCstmrCdtTrf.getPrvsInstgAgt3Acct()));
    }

    // 设置中间代理机构和账户
    if (undrlygCstmrCdtTrf.getIntrmyAgt1() != null) {
      result.setIntrmyAgt1(MxHelper.createFinancialInstitution(undrlygCstmrCdtTrf.getIntrmyAgt1()));
    }
    if (undrlygCstmrCdtTrf.getIntrmyAgt1Acct() != null) {
      result.setIntrmyAgt1Acct(MxHelper.createCashAccount(undrlygCstmrCdtTrf.getIntrmyAgt1Acct()));
    }

    if (undrlygCstmrCdtTrf.getIntrmyAgt2() != null) {
      result.setIntrmyAgt2(MxHelper.createFinancialInstitution(undrlygCstmrCdtTrf.getIntrmyAgt2()));
    }
    if (undrlygCstmrCdtTrf.getIntrmyAgt2Acct() != null) {
      result.setIntrmyAgt2Acct(MxHelper.createCashAccount(undrlygCstmrCdtTrf.getIntrmyAgt2Acct()));
    }

    if (undrlygCstmrCdtTrf.getIntrmyAgt3() != null) {
      result.setIntrmyAgt3(MxHelper.createFinancialInstitution(undrlygCstmrCdtTrf.getIntrmyAgt3()));
    }
    if (undrlygCstmrCdtTrf.getIntrmyAgt3Acct() != null) {
      result.setIntrmyAgt3Acct(MxHelper.createCashAccount(undrlygCstmrCdtTrf.getIntrmyAgt3Acct()));
    }

    // 设置债权人代理机构和账户
    if (undrlygCstmrCdtTrf.getCdtrAgt() != null) {
      result.setCdtrAgt(MxHelper.createFinancialInstitution(undrlygCstmrCdtTrf.getCdtrAgt()));
    }
    if (undrlygCstmrCdtTrf.getCdtrAgtAcct() != null) {
      result.setCdtrAgtAcct(MxHelper.createCashAccount(undrlygCstmrCdtTrf.getCdtrAgtAcct()));
    }

    // 设置债权人和账户
    if (undrlygCstmrCdtTrf.getCdtr() != null) {
      result.setCdtr(MxHelper.createPartyIdentification(undrlygCstmrCdtTrf.getCdtr()));
    }
    if (undrlygCstmrCdtTrf.getCdtrAcct() != null) {
      result.setCdtrAcct(MxHelper.createCashAccount(undrlygCstmrCdtTrf.getCdtrAcct()));
    }

    // 设置最终债权人
    if (undrlygCstmrCdtTrf.getUltmtCdtr() != null) {
      result.setUltmtCdtr(MxHelper.createPartyIdentification(undrlygCstmrCdtTrf.getUltmtCdtr()));
    }

    // 设置债权人代理指令
    if (undrlygCstmrCdtTrf.getInstrForCdtrAgt() != null
        && !undrlygCstmrCdtTrf.getInstrForCdtrAgt().isEmpty()) {
      undrlygCstmrCdtTrf
          .getInstrForCdtrAgt()
          .forEach(
              instr -> {
                com.prowidesoftware.swift.model.mx.dic.InstructionForCreditorAgent1 instruction =
                    new com.prowidesoftware.swift.model.mx.dic.InstructionForCreditorAgent1();

                if (instr.getCd() != null) {
                  instruction.setCd(
                      com.prowidesoftware.swift.model.mx.dic.Instruction3Code.valueOf(
                          instr.getCd()));
                }

                if (instr.getInstrInf() != null) {
                  instruction.setInstrInf(instr.getInstrInf());
                }

                result.addInstrForCdtrAgt(instruction);
              });
    }

    // 设置下一代理指令
    if (undrlygCstmrCdtTrf.getInstrForNxtAgt() != null
        && !undrlygCstmrCdtTrf.getInstrForNxtAgt().isEmpty()) {
      undrlygCstmrCdtTrf
          .getInstrForNxtAgt()
          .forEach(
              instr -> {
                com.prowidesoftware.swift.model.mx.dic.InstructionForNextAgent1 instruction =
                    new com.prowidesoftware.swift.model.mx.dic.InstructionForNextAgent1();

                if (instr.getCd() != null) {
                  instruction.setCd(
                      com.prowidesoftware.swift.model.mx.dic.Instruction4Code.valueOf(
                          instr.getCd()));
                }

                if (instr.getInstrInf() != null) {
                  instruction.setInstrInf(instr.getInstrInf());
                }

                result.addInstrForNxtAgt(instruction);
              });
    }

    // 设置汇款信息
    if (undrlygCstmrCdtTrf.getRmtInf() != null && !undrlygCstmrCdtTrf.getRmtInf().isEmpty()) {
      com.prowidesoftware.swift.model.mx.dic.RemittanceInformation16 rmtInf =
          new com.prowidesoftware.swift.model.mx.dic.RemittanceInformation16();

      undrlygCstmrCdtTrf.getRmtInf().forEach(info -> rmtInf.addUstrd(info));

      result.setRmtInf(rmtInf);
    }

    // 设置指示金额
    if (undrlygCstmrCdtTrf.getInstdAmt() != null) {
      com.prowidesoftware.swift.model.mx.dic.ActiveOrHistoricCurrencyAndAmount amount =
          new com.prowidesoftware.swift.model.mx.dic.ActiveOrHistoricCurrencyAndAmount();

      amount.setCcy(undrlygCstmrCdtTrf.getInstdAmt().getCurrency());
      amount.setValue(undrlygCstmrCdtTrf.getInstdAmt().getAmount());

      result.setInstdAmt(amount);
    }

    return result;
  }

  private void setInstructionsForAgents(
      CreditTransferTransaction36 cdtTrfTxInf,
      CreditTransferTransactionInformation36 ctti) {
    if (!CollectionUtils.isEmpty(ctti.getInstrForCdtrAgt())) {
      ctti.getInstrForCdtrAgt()
          .forEach(
              instr ->
                  cdtTrfTxInf.addInstrForCdtrAgt(
                      new InstructionForCreditorAgent2()
                          .setCd(Instruction5Code.valueOf(instr.getCd()))
                          .setInstrInf(instr.getInstrInf())));
    }

    if (!CollectionUtils.isEmpty(ctti.getInstrForNxtAgt())) {
      ctti.getInstrForNxtAgt()
          .forEach(
              instr ->
                  cdtTrfTxInf.addInstrForNxtAgt(
                      new InstructionForNextAgent1()
                          .setCd(Instruction4Code.valueOf(instr.getCd()))
                          .setInstrInf(instr.getInstrInf())));
    }
  }

  private void setAgentsInformation(
      CreditTransferTransaction36 cdtTrfTxInf,
      CreditTransferTransactionInformation36 ctti) {
    if (!CollectionUtils.isEmpty(ctti.getPreviousInstrictingInfos())) {
      setPreviousInstructingAgents(cdtTrfTxInf, ctti.getPreviousInstrictingInfos());
    }
    setIntermediaryAgents(cdtTrfTxInf, ctti);
  }

  private void setSettlementInformation(
      CreditTransferTransaction36 cdtTrfTxInf,
      CreditTransferTransactionInformation36 ctti) {
    setSettlementTimeIndication(cdtTrfTxInf, ctti.getSettlementTimeIndication());
    setSettlementTimeRequest(cdtTrfTxInf, ctti.getSettlementTimeRequest());
  }

  private void setSettlementTimeIndication(
      CreditTransferTransaction36 cdtTrfTxInf, SettlementTimeIndication sti) {
    if (sti != null) {
      SettlementDateTimeIndication1 sttlmTmIndctn = new SettlementDateTimeIndication1();

      if (sti.getDbtDtTm() != null) {
        sttlmTmIndctn.setDbtDtTm(sti.getDbtDtTm());
      }

      if (sti.getCdtDtTm() != null) {
        sttlmTmIndctn.setCdtDtTm(sti.getCdtDtTm());
      }

      cdtTrfTxInf.setSttlmTmIndctn(sttlmTmIndctn);
    }
  }

  private void setSettlementTimeRequest(
      CreditTransferTransaction36 cdtTrfTxInf,
      CreditTransferTransactionInformation36.SettlementTimeRequest str) {
    if (str != null) {
      SettlementTimeRequest2 sttlmTmReq = new SettlementTimeRequest2();
      if (str.getClsTime() != null) {
        sttlmTmReq.setCLSTm(OffsetTime.of(str.getClsTime(), ZoneOffset.UTC));
      }
      if (str.getTillTime() != null) {
        sttlmTmReq.setTillTm(OffsetTime.of(str.getTillTime(), ZoneOffset.UTC));
      }
      if (str.getFromTime() != null) {
        sttlmTmReq.setFrTm(OffsetTime.of(str.getFromTime(), ZoneOffset.UTC));
      }
      if (str.getRejectTime() != null) {
        sttlmTmReq.setRjctTm(OffsetTime.of(str.getRejectTime(), ZoneOffset.UTC));
      }
      cdtTrfTxInf.setSttlmTmReq(sttlmTmReq);
    }
  }

  private BranchAndFinancialInstitutionIdentification6 createParty(Party party) {
    return new BranchAndFinancialInstitutionIdentification6()
        .setFinInstnId(new FinancialInstitutionIdentification18().setNm(party.getNm()));
  }

  private CashAccount38 createCashAccount(Party party) {
    return new CashAccount38()
        .setId(
            new AccountIdentification4Choice()
                .setOthr(new GenericAccountIdentification1().setId(party.getAcctId())))
        .setNm(party.getNm());
  }

  private RemittanceInformation2 createRemittanceInformation(List<String> remittanceInformations) {
    RemittanceInformation2 rmtInf = new RemittanceInformation2();
    if (!CollectionUtils.isEmpty(remittanceInformations)) {
      remittanceInformations.forEach(rmtInf::addUstrd);
    }
    return rmtInf;
  }

  private void setPreviousInstructingAgents(
      CreditTransferTransaction36 cdtTrfTxInf, List<PreviousInstrictingInfo> prvsInstgInfoList) {
    for (int i = 0; i < Math.min(prvsInstgInfoList.size(), 3); i++) {
      PreviousInstrictingInfo prvsInstgInfo = prvsInstgInfoList.get(i);
      if (prvsInstgInfo == null) {
        continue;
      }

      switch (i) {
        case 0:
          if (prvsInstgInfo.getPrvsInstgAgt() != null) {
            cdtTrfTxInf.setPrvsInstgAgt1(
                MxHelper.createFinancialInstitution(prvsInstgInfo.getPrvsInstgAgt()));
          }
          if (prvsInstgInfo.getPrvsInstgAgtAcct() != null) {
            cdtTrfTxInf.setPrvsInstgAgt1Acct(
                MxHelper.createCashAccount(prvsInstgInfo.getPrvsInstgAgtAcct()));
          }
          break;
        case 1:
          if (prvsInstgInfo.getPrvsInstgAgt() != null) {
            cdtTrfTxInf.setPrvsInstgAgt2(
                MxHelper.createFinancialInstitution(prvsInstgInfo.getPrvsInstgAgt()));
          }
          if (prvsInstgInfo.getPrvsInstgAgtAcct() != null) {
            cdtTrfTxInf.setPrvsInstgAgt2Acct(
                MxHelper.createCashAccount(prvsInstgInfo.getPrvsInstgAgtAcct()));
          }
          break;
        case 2:
          if (prvsInstgInfo.getPrvsInstgAgt() != null) {
            cdtTrfTxInf.setPrvsInstgAgt3(
                MxHelper.createFinancialInstitution(prvsInstgInfo.getPrvsInstgAgt()));
          }
          if (prvsInstgInfo.getPrvsInstgAgtAcct() != null) {
            cdtTrfTxInf.setPrvsInstgAgt3Acct(
                MxHelper.createCashAccount(prvsInstgInfo.getPrvsInstgAgtAcct()));
          }
          break;
      }
    }
  }

  private void setIntermediaryAgents(
      CreditTransferTransaction36 cdtTrfTxInf,
      CreditTransferTransactionInformation36 ctti) {
    // Set intermediary agent 1
    if (ctti.getIntrmyAgt1() != null) {
      cdtTrfTxInf.setIntrmyAgt1(MxHelper.createFinancialInstitution(ctti.getIntrmyAgt1()));
    }
    if (ctti.getIntrmyAgt1Acct() != null) {
      cdtTrfTxInf.setIntrmyAgt1Acct(MxHelper.createCashAccount(ctti.getIntrmyAgt1Acct()));
    }

    // Set intermediary agent 2
    if (ctti.getIntrmyAgt2() != null) {
      cdtTrfTxInf.setIntrmyAgt2(MxHelper.createFinancialInstitution(ctti.getIntrmyAgt2()));
    }
    if (ctti.getIntrmyAgt2Acct() != null) {
      cdtTrfTxInf.setIntrmyAgt2Acct(MxHelper.createCashAccount(ctti.getIntrmyAgt2Acct()));
    }

    // Set intermediary agent 3
    if (ctti.getIntrmyAgt3() != null) {
      cdtTrfTxInf.setIntrmyAgt3(MxHelper.createFinancialInstitution(ctti.getIntrmyAgt3()));
    }
    if (ctti.getIntrmyAgt3Acct() != null) {
      cdtTrfTxInf.setIntrmyAgt3Acct(MxHelper.createCashAccount(ctti.getIntrmyAgt3Acct()));
    }
  }

  private Purpose2Choice createPurpose(TypedValue purp) {
    if (purp == null) {
      return null;
    }
    Purpose2Choice purpose = new Purpose2Choice();
    if (purp.getType() == TypedValue.ValueType.CODE) {
      purpose.setCd(purp.getValue());
    } else if (purp.getType() == TypedValue.ValueType.PROPRIETARY) {
      purpose.setPrtry(purp.getValue());
    }
    return purpose;
  }
}
