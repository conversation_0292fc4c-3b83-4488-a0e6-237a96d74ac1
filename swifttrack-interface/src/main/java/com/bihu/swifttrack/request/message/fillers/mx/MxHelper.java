package com.bihu.swifttrack.request.message.fillers.mx;

import com.bihu.swifttrack.dto.Amount;
import com.bihu.swifttrack.request.message.mx.Account;
import com.bihu.swifttrack.request.message.mx.Account.Identification;
import com.bihu.swifttrack.request.message.mx.Agent;
import com.bihu.swifttrack.request.message.mx.CamtGroupHeader;
import com.bihu.swifttrack.request.message.mx.Other;
import com.bihu.swifttrack.request.message.mx.Party;
import com.bihu.swifttrack.request.message.mx.PaymentIdentification;
import com.bihu.swifttrack.request.message.mx.PaymentTypeInformation;
import com.bihu.swifttrack.request.message.mx.PostalAddress;
import com.bihu.swifttrack.request.message.mx.ReportEntry10;
import com.bihu.swifttrack.request.message.mx.SettlementInformation;
import com.bihu.swifttrack.request.message.mx.TypedValue;
import com.bihu.swifttrack.request.message.mx.TypedValue.ValueType;
import com.bihu.swifttrack.utils.MXMessageValidator;
import com.prowidesoftware.swift.model.mx.MxWriteConfiguration;
import com.prowidesoftware.swift.model.mx.dic.AccountIdentification4Choice;
import com.prowidesoftware.swift.model.mx.dic.AccountInterest4;
import com.prowidesoftware.swift.model.mx.dic.AccountSchemeName1Choice;
import com.prowidesoftware.swift.model.mx.dic.ActiveCurrencyAndAmount;
import com.prowidesoftware.swift.model.mx.dic.AmountAndCurrencyExchange3;
import com.prowidesoftware.swift.model.mx.dic.AmountAndCurrencyExchangeDetails3;
import com.prowidesoftware.swift.model.mx.dic.AmountAndCurrencyExchangeDetails4;
import com.prowidesoftware.swift.model.mx.dic.BranchAndFinancialInstitutionIdentification6;
import com.prowidesoftware.swift.model.mx.dic.CashAccount38;
import com.prowidesoftware.swift.model.mx.dic.CashAccount39;
import com.prowidesoftware.swift.model.mx.dic.CashAccountType2Choice;
import com.prowidesoftware.swift.model.mx.dic.CategoryPurpose1Choice;
import com.prowidesoftware.swift.model.mx.dic.ClearingChannel2Code;
import com.prowidesoftware.swift.model.mx.dic.ClearingSystemIdentification2Choice;
import com.prowidesoftware.swift.model.mx.dic.ClearingSystemIdentification3Choice;
import com.prowidesoftware.swift.model.mx.dic.ClearingSystemMemberIdentification2;
import com.prowidesoftware.swift.model.mx.dic.Contact4;
import com.prowidesoftware.swift.model.mx.dic.CurrencyExchange5;
import com.prowidesoftware.swift.model.mx.dic.DateAndDateTime2Choice;
import com.prowidesoftware.swift.model.mx.dic.DateAndPlaceOfBirth1;
import com.prowidesoftware.swift.model.mx.dic.DateTimePeriod1;
import com.prowidesoftware.swift.model.mx.dic.FinancialInstitutionIdentification18;
import com.prowidesoftware.swift.model.mx.dic.GenericAccountIdentification1;
import com.prowidesoftware.swift.model.mx.dic.GenericOrganisationIdentification1;
import com.prowidesoftware.swift.model.mx.dic.GenericPersonIdentification1;
import com.prowidesoftware.swift.model.mx.dic.GroupHeader81;
import com.prowidesoftware.swift.model.mx.dic.InterestType1Choice;
import com.prowidesoftware.swift.model.mx.dic.InterestType1Code;
import com.prowidesoftware.swift.model.mx.dic.LocalInstrument2Choice;
import com.prowidesoftware.swift.model.mx.dic.OrganisationIdentification29;
import com.prowidesoftware.swift.model.mx.dic.Pagination1;
import com.prowidesoftware.swift.model.mx.dic.Party38Choice;
import com.prowidesoftware.swift.model.mx.dic.PartyIdentification135;
import com.prowidesoftware.swift.model.mx.dic.PaymentIdentification7;
import com.prowidesoftware.swift.model.mx.dic.PaymentTypeInformation28;
import com.prowidesoftware.swift.model.mx.dic.PersonIdentification13;
import com.prowidesoftware.swift.model.mx.dic.PersonIdentificationSchemeName1Choice;
import com.prowidesoftware.swift.model.mx.dic.PostalAddress24;
import com.prowidesoftware.swift.model.mx.dic.Priority2Code;
import com.prowidesoftware.swift.model.mx.dic.ServiceLevel8Choice;
import com.prowidesoftware.swift.model.mx.dic.SettlementInstruction7;
import com.prowidesoftware.swift.model.mx.dic.SettlementMethod1Code;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

/**
 * MxHelper
 *
 * <p>
 *
 * <AUTHOR>
 * @date 2024/10/29
 */
public class MxHelper {

  /**
   * This function returns a pre-configured instance of {@link MxWriteConfiguration} with specific
   * settings.
   *
   * <p>The returned {@link MxWriteConfiguration} instance is configured with the following
   * properties:
   *
   * <ul>
   *   <li>{@code headerPrefix}: Set to "head"
   *   <li>{@code documentPrefix}: Set to "pacs"
   *   <li>{@code includeXMLDeclaration}: Set to false
   * </ul>
   *
   * <p>This configuration can be used when writing MX messages using the Prowide Software library.
   *
   * @return A pre-configured instance of {@link MxWriteConfiguration} with specific settings.
   */
  public static MxWriteConfiguration getWriteConfiguration(
      String headerPrefix, String documentPrefix, boolean includeXMLDeclaration) {
    MxWriteConfiguration configuration = new MxWriteConfiguration();
    configuration.headerPrefix = headerPrefix;
    configuration.documentPrefix = documentPrefix;
    configuration.includeXMLDeclaration = includeXMLDeclaration;
    return configuration;
  }

  public static BranchAndFinancialInstitutionIdentification6 createFinancialInstitution(
      Agent agent) {
    if (agent == null) {
      return null;
    }
    FinancialInstitutionIdentification18 finInstnId =
        new FinancialInstitutionIdentification18()
            .setBICFI(agent.getBicfi())
            .setLEI(agent.getLei())
            .setNm(agent.getNm())
            .setPstlAdr(createPostalAddress(agent.getPstlAdr()));

    if (agent.getClrSysId() != null && StringUtils.isNotBlank(agent.getClrSysMmbId())) {
      ClearingSystemIdentification2Choice choice = new ClearingSystemIdentification2Choice();
      if (ValueType.CODE.equals(agent.getClrSysId().getType())) {
        choice.setCd(agent.getClrSysId().getValue());
      } else {
        choice.setPrtry(agent.getClrSysId().getValue());
      }
      finInstnId.setClrSysMmbId(
          new ClearingSystemMemberIdentification2()
              .setClrSysId(choice)
              .setMmbId(agent.getClrSysMmbId()));
    }

    return new BranchAndFinancialInstitutionIdentification6().setFinInstnId(finInstnId);
  }

  public static CashAccount38 createCashAccount(Account account) {
    if (account == null) {
      return null;
    }

    Identification accountId = account.getId();

    if (accountId != null
        && "IBAN".equals(accountId.getType())
        && StringUtils.isNotEmpty(accountId.getValue())) {
      account.setIban(accountId.getValue());
    }

    if (accountId != null
        && "OTHER".equals(accountId.getType())
        && StringUtils.isNotEmpty(accountId.getValue())) {
      Other other = new Other();
      other.setId(accountId.getValue());
      account.setOther(other);
    }

    return getCashAccount38(account);
  }

  @NotNull
  static CashAccount38 getCashAccount38(Account account) {
    CashAccount38 cashAccount = new CashAccount38();
    AccountIdentification4Choice id = new AccountIdentification4Choice();
    if (StringUtils.isNotEmpty(account.getIban())) {
      id.setIBAN(account.getIban());
      cashAccount.setId(id);
    } else if (account.getOther() != null) {
      seGenericAccountIdentification(account, id);
      cashAccount.setId(id);
    }

    if (account.getAcctTp() != null) {
      CashAccountType2Choice type = new CashAccountType2Choice();
      type.setPrtry(account.getAcctTp());
      cashAccount.setTp(type);
    }

    if (account.getCcy() != null) {
      cashAccount.setCcy(account.getCcy());
    }

    if (account.getNm() != null) {
      cashAccount.setNm(account.getNm());
    }

    return cashAccount;
  }

  public static void seGenericAccountIdentification(
      Account account, AccountIdentification4Choice id) {
    GenericAccountIdentification1 other = new GenericAccountIdentification1();
    other.setId(account.getOther().getId());
    if (account.getOther().getSchmeNm() != null) {
      AccountSchemeName1Choice schemeName = new AccountSchemeName1Choice();
      if (account.getOther().getSchmeNm().getType() == TypedValue.ValueType.CODE) {
        schemeName.setCd(account.getOther().getSchmeNm().getValue());
      } else {
        schemeName.setPrtry(account.getOther().getSchmeNm().getValue());
      }
      other.setSchmeNm(schemeName);
    }
    other.setIssr(account.getOther().getIssr());
    id.setOthr(other);
  }

  public static GroupHeader81 createCamtGroupHeader(CamtGroupHeader header) {
    GroupHeader81 grpHdr =
        new GroupHeader81()
            .setMsgId(header.getMsgId())
            .setCreDtTm(convertToOffsetDateTime(header.getCreDtTm()));

    // Add original business query if present
    Optional.ofNullable(header.getOrgnlBizQry())
        .ifPresent(
            origQuery -> {
              com.prowidesoftware.swift.model.mx.dic.OriginalBusinessQuery1 originalQuery =
                  new com.prowidesoftware.swift.model.mx.dic.OriginalBusinessQuery1();

              // Set message id
              MXMessageValidator.validateLength(origQuery.getMsgId(), 1, 35, "Original Message ID");
              originalQuery.setMsgId(origQuery.getMsgId());

              // Set message name id
              MXMessageValidator.validateLength(
                  origQuery.getMsgNmId(), 1, 35, "Original Message Name ID");
              originalQuery.setMsgNmId(origQuery.getMsgNmId());

              // Set creation date time
              if (origQuery.getCreDtTm() != null) {
                originalQuery.setCreDtTm(origQuery.getCreDtTm().atOffset(ZoneOffset.UTC));
              }

              grpHdr.setOrgnlBizQry(originalQuery);
            });

    Optional.ofNullable(header.getMsgRcpt())
        .ifPresent(
            rcpt -> {
              PartyIdentification135 msgRcpt = new PartyIdentification135().setNm(rcpt.getNm());
              Optional.ofNullable(rcpt.getPstlAdr())
                  .ifPresent(adr -> msgRcpt.setPstlAdr(MxHelper.createPostalAddress(adr)));
              Optional.ofNullable(rcpt.getId())
                  .ifPresent(id -> msgRcpt.setId(MxHelper.createParty38Choice(id)));
              Optional.ofNullable(rcpt.getCtctDtls())
                  .ifPresent(
                      contactInfo ->
                          msgRcpt.setCtctDtls(MxHelper.createContactDetails(contactInfo)));
              Optional.ofNullable(rcpt.getCtryOfRes()).ifPresent(msgRcpt::setCtryOfRes);
              grpHdr.setMsgRcpt(msgRcpt);
            });

    Optional.ofNullable(header.getMsgPgntn())
        .ifPresent(
            pgntn -> {
              Pagination1 pagination =
                  new Pagination1().setPgNb(pgntn.getPgNb()).setLastPgInd(pgntn.getLastPgInd());
              grpHdr.setMsgPgntn(pagination);
            });

    Optional.ofNullable(header.getAddtlInf())
        .ifPresent(addInf -> grpHdr.setAddtlInf(header.getAddtlInf()));

    return grpHdr;
  }

  public static ActiveCurrencyAndAmount createActiveCurrencyAndAmount(Amount amount) {
    return new ActiveCurrencyAndAmount().setCcy(amount.getCurrency()).setValue(amount.getAmount());
  }

  public static PaymentIdentification7 createPaymentIdentification(PaymentIdentification pmtId) {

    PaymentIdentification7 paymentIdentification7 = new PaymentIdentification7();
    paymentIdentification7.setInstrId(pmtId.getInstrId());
    paymentIdentification7.setEndToEndId(pmtId.getEndToEndId());
    paymentIdentification7.setTxId(pmtId.getTxId());
    paymentIdentification7.setUETR(
        pmtId.getUetr() == null ? java.util.UUID.randomUUID().toString() : pmtId.getUetr());
    return paymentIdentification7;
  }

  public static PaymentTypeInformation28 createPaymentTypeInformation(
      PaymentTypeInformation pmtTpInf) {
    if (pmtTpInf == null) {
      return null;
    }
    PaymentTypeInformation28 result = new PaymentTypeInformation28();

    if (StringUtils.isNotBlank(pmtTpInf.getInstrPrty())) {
      result.setInstrPrty(Priority2Code.valueOf(pmtTpInf.getInstrPrty()));
    }

    if (StringUtils.isNotBlank(pmtTpInf.getClrChanl())) {
      result.setClrChanl(ClearingChannel2Code.valueOf(pmtTpInf.getClrChanl()));
    }

    if (pmtTpInf.getSvcLvl() != null) {
      pmtTpInf.getSvcLvl().stream()
          .map(MxHelper::createServiceLevelChoice)
          .forEach(result::addSvcLvl);
    }

    if (pmtTpInf.getLclInstrm() != null) {
      result.setLclInstrm(createLocalInstrument(pmtTpInf.getLclInstrm()));
    }

    if (pmtTpInf.getCtgyPurp() != null) {
      result.setCtgyPurp(createCategoryPurpose(pmtTpInf.getCtgyPurp()));
    }

    return result;
  }

  public static ServiceLevel8Choice createServiceLevelChoice(TypedValue typedValue) {
    ServiceLevel8Choice choice = new ServiceLevel8Choice();
    if (typedValue.getType() == ValueType.CODE) {
      choice.setCd(typedValue.getValue());
    } else {
      choice.setPrtry(typedValue.getValue());
    }
    return choice;
  }

  /**
   * 创建服务级别对象
   *
   * @param typedValue 类型化值
   * @return 服务级别对象
   */
  public static ServiceLevel8Choice createServiceLevel(TypedValue typedValue) {
    return createServiceLevelChoice(typedValue);
  }

  public static LocalInstrument2Choice createLocalInstrument(TypedValue lclInstrm) {
    if (lclInstrm == null) {
      return null;
    }
    LocalInstrument2Choice choice = new LocalInstrument2Choice();
    if (lclInstrm.getType() == TypedValue.ValueType.CODE) {
      choice.setCd(lclInstrm.getValue());
    } else {
      choice.setPrtry(lclInstrm.getValue());
    }
    return choice;
  }

  public static CategoryPurpose1Choice createCategoryPurpose(TypedValue ctgyPurp) {
    if (ctgyPurp == null) {
      return null;
    }
    CategoryPurpose1Choice choice = new CategoryPurpose1Choice();
    if (ctgyPurp.getType() == TypedValue.ValueType.CODE) {
      choice.setCd(ctgyPurp.getValue());
    } else {
      choice.setPrtry(ctgyPurp.getValue());
    }
    return choice;
  }

  public static PostalAddress24 createPostalAddress(PostalAddress address) {
    if (address == null) {
      return null;
    }
    PostalAddress24 postalAddress24 =
        new PostalAddress24()
            .setDept(address.getDept())
            .setSubDept(address.getSubDept())
            .setStrtNm(address.getStrtNm())
            .setBldgNb(address.getBldgNb())
            .setBldgNm(address.getBldgNm())
            .setFlr(address.getFlr())
            .setPstBx(address.getPstBx())
            .setRoom(address.getRoom())
            .setPstCd(address.getPstCd())
            .setTwnNm(address.getTwnNm())
            .setTwnLctnNm(address.getTwnLctnNm())
            .setDstrctNm(address.getDstrctNm())
            .setCtrySubDvsn(address.getCtrySubDvsn())
            .setCtry(address.getCtry());

    Optional.ofNullable(address.getAddrLine1()).ifPresent(postalAddress24::addAdrLine);
    Optional.ofNullable(address.getAddrLine2()).ifPresent(postalAddress24::addAdrLine);
    Optional.ofNullable(address.getAddrLine3()).ifPresent(postalAddress24::addAdrLine);

    if (address.getAdrLine() != null) {
      for (String line : address.getAdrLine()) {
        postalAddress24.addAdrLine(line);
      }
    }

    return postalAddress24;
  }

  public static Contact4 createContactDetails(Party.ContactInfo contactInfo) {
    if (contactInfo == null) {
      return null;
    }
    return new Contact4()
        .setMobNb(contactInfo.getMobileNumber())
        .setEmailAdr(contactInfo.getEmailAddress());
  }

  public static PersonIdentification13 createPersonIdentification(
      Party.PrivateIdentification prvtId) {
    PersonIdentification13 personId = new PersonIdentification13();

    if (prvtId.getDtAndPlcOfBirth() != null) {
      Party.DateAndPlaceOfBirth birthInfo = prvtId.getDtAndPlcOfBirth();
      personId.setDtAndPlcOfBirth(
          new DateAndPlaceOfBirth1()
              .setBirthDt(birthInfo.getBirthDt())
              .setPrvcOfBirth(birthInfo.getPrvcOfBirth())
              .setCityOfBirth(birthInfo.getCityOfBirth())
              .setCtryOfBirth(birthInfo.getCtryOfBirth()));
    }

    if (prvtId.getOthr() != null && !prvtId.getOthr().isEmpty()) {
      for (Other other : prvtId.getOthr()) {
        personId.addOthr(createGenericPersonIdentification(other));
      }
    }

    return personId;
  }

  public static GenericPersonIdentification1 createGenericPersonIdentification(Other other) {
    GenericPersonIdentification1 genericPersonId =
        new GenericPersonIdentification1().setId(other.getId()).setIssr(other.getIssr());

    if (other.getSchmeNm() != null) {
      PersonIdentificationSchemeName1Choice schemeName =
          new PersonIdentificationSchemeName1Choice();
      if (other.getSchmeNm().getType() == TypedValue.ValueType.CODE) {
        schemeName.setCd(other.getSchmeNm().getValue());
      } else if (other.getSchmeNm().getType() == TypedValue.ValueType.PROPRIETARY) {
        schemeName.setPrtry(other.getSchmeNm().getValue());
      }
      genericPersonId.setSchmeNm(schemeName);
    }

    return genericPersonId;
  }

  public static DateTimePeriod1 convertToDateTimePeriod(
      com.bihu.swifttrack.request.message.mx.DateTimePeriod1 frToDt) {
    DateTimePeriod1 period = new DateTimePeriod1();
    if (frToDt.getFrDtTm() != null) {
      period.setFrDtTm(convertToOffsetDateTime(frToDt.getFrDtTm()));
    }
    if (frToDt.getToDtTm() != null) {
      period.setToDtTm(convertToOffsetDateTime(frToDt.getToDtTm()));
    }
    return period;
  }

  /** Creates a ReportEntry10 from the given entry data */
  public static com.prowidesoftware.swift.model.mx.dic.ReportEntry10 createReportEntry(
      ReportEntry10 entry) {
    // Validate input
    if (entry == null) {
      return null;
    }

    com.prowidesoftware.swift.model.mx.dic.ReportEntry10 reportEntry =
        new com.prowidesoftware.swift.model.mx.dic.ReportEntry10();

    // Set basic entry information with validation
    Optional.ofNullable(entry.getNtryRef()).ifPresent(reportEntry::setNtryRef);

    // Set amount with validation
    Optional.ofNullable(entry.getAmt()).ifPresent(reportEntry::setAmt);

    // Set credit/debit indicator with validation
    Optional.ofNullable(entry.getCdtDbtInd()).ifPresent(reportEntry::setCdtDbtInd);

    // Set other basic fields
    Optional.ofNullable(entry.getRvslInd()).ifPresent(reportEntry::setRvslInd);
    Optional.ofNullable(entry.getSts()).ifPresent(reportEntry::setSts);
    Optional.ofNullable(entry.getAcctSvcrRef()).ifPresent(reportEntry::setAcctSvcrRef);

    // Set bank transaction code with validation
    Optional.ofNullable(entry.getBkTxCd()).ifPresent(reportEntry::setBkTxCd);

    // Set commission waiver indicator
    Optional.ofNullable(entry.getComssnWvrInd()).ifPresent(reportEntry::setComssnWvrInd);

    // Set additional information indicator
    Optional.ofNullable(entry.getAddtlInfInd()).ifPresent(reportEntry::setAddtlInfInd);

    // Set charges
    Optional.ofNullable(entry.getChrgs()).ifPresent(reportEntry::setChrgs);

    // Set technical input channel with validation
    Optional.ofNullable(entry.getTechInptChanl()).ifPresent(reportEntry::setTechInptChanl);

    // Set interest
    Optional.ofNullable(entry.getIntrst()).ifPresent(reportEntry::setIntrst);

    // Set card transaction
    Optional.ofNullable(entry.getCardTx()).ifPresent(reportEntry::setCardTx);

    // Set additional entry information
    Optional.ofNullable(entry.getAddtlNtryInf()).ifPresent(reportEntry::setAddtlNtryInf);

    // Set amount details with validation
    Optional.ofNullable(entry.getAmtDtls())
        .ifPresent(
            amtDtls -> {
              AmountAndCurrencyExchange3 details = createAmountAndCurrencyExchange(amtDtls);
              reportEntry.setAmtDtls(details);
            });

    // Set dates with validation
    setEntryDates(entry, reportEntry);

    // Set entry details
    Optional.ofNullable(entry.getNtryDtls())
        .filter(details -> !details.isEmpty())
        .ifPresent(details -> details.forEach(reportEntry::addNtryDtls));

    // Set availability
    Optional.ofNullable(entry.getAvlbty())
        .filter(avl -> !avl.isEmpty())
        .ifPresent(avl -> avl.forEach(reportEntry::addAvlbty));

    return reportEntry;
  }

  // Helper method to set entry dates
  private static void setEntryDates(
      ReportEntry10 entry, com.prowidesoftware.swift.model.mx.dic.ReportEntry10 reportEntry) {

    // Set booking date
    Optional.ofNullable(entry.getBookgDt())
        .ifPresent(
            dt -> {
              DateAndDateTime2Choice bookingDate = new DateAndDateTime2Choice();
              Optional.ofNullable(dt.getDt())
                  .ifPresent(
                      date -> {
                        MXMessageValidator.validateNotNull(date, "Booking Date");
                        bookingDate.setDt(date);
                      });
              Optional.ofNullable(dt.getDtTm())
                  .ifPresent(
                      dateTm -> {
                        bookingDate.setDtTm(dateTm.atOffset(ZoneOffset.UTC));
                      });
              reportEntry.setBookgDt(bookingDate);
            });

    // Set value date
    Optional.ofNullable(entry.getValDt())
        .ifPresent(
            dt -> {
              DateAndDateTime2Choice valueDate = new DateAndDateTime2Choice();
              Optional.ofNullable(dt.getDt()).ifPresent(valueDate::setDt);
              Optional.ofNullable(dt.getDtTm())
                  .ifPresent(
                      dateTm -> {
                        valueDate.setDtTm(dateTm.atOffset(ZoneOffset.UTC));
                      });
              reportEntry.setValDt(valueDate);
            });
  }

  /** Creates interest information entries from the given interest data */
  public static void addInterestInformation(
      List<com.bihu.swifttrack.request.message.mx.AccountInterest4> interests,
      Consumer<AccountInterest4> interestConsumer) {
    Optional.ofNullable(interests)
        .filter(list -> !list.isEmpty())
        .ifPresent(
            interestList ->
                interestList.forEach(
                    interest -> {
                      AccountInterest4 accountInterest = new AccountInterest4();
                      Optional.ofNullable(interest.getTp())
                          .ifPresent(
                              tp -> {
                                InterestType1Choice choice = new InterestType1Choice();
                                if (ValueType.CODE.equals(tp.getType())) {
                                  choice.setCd(InterestType1Code.valueOf(tp.getValue()));
                                } else {
                                  choice.setPrtry(tp.getValue());
                                }
                                accountInterest.setTp(choice);
                              });
                      Optional.ofNullable(interest.getRsn()).ifPresent(accountInterest::setRsn);
                      Optional.ofNullable(interest.getTax()).ifPresent(accountInterest::setTax);
                      Optional.ofNullable(interest.getFrToDt())
                          .map(MxHelper::convertToDateTimePeriod)
                          .ifPresent(accountInterest::setFrToDt);
                      Optional.ofNullable(interest.getRate())
                          .filter(rates -> !rates.isEmpty())
                          .ifPresent(rates -> rates.forEach(accountInterest::addRate));

                      interestConsumer.accept(accountInterest);
                    }));
  }

  /** Creates AmountAndCurrencyExchange3 from the given amount details */
  public static AmountAndCurrencyExchange3 createAmountAndCurrencyExchange(
      ReportEntry10.AmountAndCurrencyExchange3 amtDtls) {
    if (amtDtls == null) {
      return null;
    }

    AmountAndCurrencyExchange3 amountDetails = new AmountAndCurrencyExchange3();

    // Set instructed amount
    Optional.ofNullable(amtDtls.getInstdAmt())
        .ifPresent(
            instdAmt ->
                amountDetails.setInstdAmt(createAmountAndCurrencyExchangeDetails(instdAmt)));

    // Set transaction amount
    Optional.ofNullable(amtDtls.getTxAmt())
        .ifPresent(txAmt -> amountDetails.setTxAmt(createAmountAndCurrencyExchangeDetails(txAmt)));

    // Set counter value amount
    Optional.ofNullable(amtDtls.getCntrValAmt())
        .ifPresent(
            cntrValAmt ->
                amountDetails.setCntrValAmt(createAmountAndCurrencyExchangeDetails(cntrValAmt)));

    // Set announced posted amount
    Optional.ofNullable(amtDtls.getAnncdPstngAmt())
        .ifPresent(
            anncdPstngAmt ->
                amountDetails.setAnncdPstngAmt(
                    createAmountAndCurrencyExchangeDetails(anncdPstngAmt)));

    // Set proprietary amounts
    Optional.ofNullable(amtDtls.getPrtryAmt())
        .filter(list -> !list.isEmpty())
        .ifPresent(
            prtryAmts ->
                prtryAmts.forEach(
                    prtryAmt -> {
                      AmountAndCurrencyExchangeDetails4 details =
                          new AmountAndCurrencyExchangeDetails4();

                      // Set required fields
                      details.setTp(prtryAmt.getTp());
                      details.setAmt(prtryAmt.getAmt());

                      // Set currency exchange if present
                      Optional.ofNullable(prtryAmt.getCcyXchg())
                          .ifPresent(
                              ccyXchg -> {
                                details.setCcyXchg(createCurrencyExchange5(ccyXchg));
                              });

                      amountDetails.addPrtryAmt(details);
                    }));

    return amountDetails;
  }

  private static CurrencyExchange5 createCurrencyExchange5(
      ReportEntry10.AmountAndCurrencyExchange3.CurrencyExchange5 ccyXchg) {
    CurrencyExchange5 currencyExchange = new CurrencyExchange5();

    // Set required fields
    currencyExchange.setSrcCcy(ccyXchg.getSrcCcy());
    currencyExchange.setTrgtCcy(ccyXchg.getTrgtCcy());
    currencyExchange.setXchgRate(ccyXchg.getXchgRate());

    // Set optional fields
    Optional.ofNullable(ccyXchg.getUnitCcy()).ifPresent(currencyExchange::setUnitCcy);
    Optional.ofNullable(ccyXchg.getCtrctId()).ifPresent(currencyExchange::setCtrctId);
    Optional.ofNullable(ccyXchg.getQtnDt())
        .ifPresent(dt -> currencyExchange.setQtnDt(dt.atOffset(ZoneOffset.UTC)));
    return currencyExchange;
  }

  /** Creates AmountAndCurrencyExchangeDetails3 from the given amount details */
  private static AmountAndCurrencyExchangeDetails3 createAmountAndCurrencyExchangeDetails(
      ReportEntry10.AmountAndCurrencyExchange3.AmountAndCurrencyExchangeDetails3 details) {
    if (details == null) {
      return null;
    }

    AmountAndCurrencyExchangeDetails3 result = new AmountAndCurrencyExchangeDetails3();

    // Set amount
    Optional.ofNullable(details.getAmt()).ifPresent(result::setAmt);

    // Set currency exchange if present
    Optional.ofNullable(details.getCcyXchg())
        .ifPresent(
            ccyXchg -> {
              result.setCcyXchg(createCurrencyExchange5(ccyXchg));
            });

    return result;
  }

  /** Creates CashAccount39 from Account information */
  public static CashAccount39 createCashAccount39(Account account) {
    if (account == null) {
      return null;
    }

    CashAccount39 cashAccount = new CashAccount39();

    // Set Account Identification
    AccountIdentification4Choice accountId = new AccountIdentification4Choice();
    if (StringUtils.isNotEmpty(account.getIban())) {
      accountId.setIBAN(account.getIban());
    } else if (account.getOther() != null) {
      seGenericAccountIdentification(account, accountId);
    }
    cashAccount.setId(accountId);

    // Set Currency
    Optional.ofNullable(account.getCcy()).ifPresent(cashAccount::setCcy);

    // Set Account Owner
    Optional.ofNullable(account.getOwnrNm())
        .ifPresent(name -> cashAccount.setOwnr(new PartyIdentification135().setNm(name)));

    // Set Account Servicer
    Optional.ofNullable(account.getSvcr())
        .ifPresent(
            svcr -> {
              cashAccount.setSvcr(MxHelper.createFinancialInstitution(svcr));
            });

    return cashAccount;
  }

  public static Agent createAgent(String bic) {
    if (StringUtils.isEmpty(bic)) {
      return null;
    }
    Agent agent = new Agent();
    agent.setBicfi(bic);
    return agent;
  }

  public static PartyIdentification135 createPartyIdentification(Party party) {
    PartyIdentification135 partyId =
        new PartyIdentification135()
            .setNm(party.getNm())
            .setCtryOfRes(party.getCtryOfRes())
            .setPstlAdr(MxHelper.createPostalAddress(party.getPstlAdr()))
            .setCtctDtls(MxHelper.createContactDetails(party.getCtcDtls()));

    if (party.getId() != null) {
      partyId.setId(createParty38Choice(party.getId()));
    }
    return partyId;
  }

  private static Party38Choice createParty38Choice(Party.Identification id) {
    if (id == null) {
      return null;
    }
    Party38Choice party38Choice = new Party38Choice();
    if (id.getOrgId() != null) {
      party38Choice.setOrgId(createOrganisationIdentification(id.getOrgId()));
    } else if (id.getPrvtId() != null) {
      party38Choice.setPrvtId(MxHelper.createPersonIdentification(id.getPrvtId()));
    }
    return party38Choice;
  }

  private static OrganisationIdentification29 createOrganisationIdentification(
      Party.OrganisationIdentification orgId) {
    OrganisationIdentification29 organisationId =
        new OrganisationIdentification29().setAnyBIC(orgId.getAnyBic()).setLEI(orgId.getLei());

    if (orgId.getOthr() != null && !orgId.getOthr().isEmpty()) {
      for (Other other : orgId.getOthr()) {
        organisationId.addOthr(createGenericOrganisationIdentification(other));
      }
    }

    return organisationId;
  }

  private static GenericOrganisationIdentification1 createGenericOrganisationIdentification(
      Other other) {
    return new GenericOrganisationIdentification1()
        .setId(other.getId())
        .setSchmeNm(
            new com.prowidesoftware.swift.model.mx.dic.OrganisationIdentificationSchemeName1Choice()
                .setCd(other.getSchmeNm().getValue()))
        .setIssr(other.getIssr());
  }

  public static CashAccount38 createCashAccount(String accountId) {
    return new CashAccount38()
        .setId(
            new AccountIdentification4Choice()
                .setOthr(new GenericAccountIdentification1().setId(accountId)));
  }

  /**
   * 将 SettlementInformation 转换为 SettlementInstruction7
   *
   * @param sttlmInf 结算信息
   * @return SettlementInstruction7 对象
   */
  public static SettlementInstruction7 createSettlementInstruction(SettlementInformation sttlmInf) {
    if (sttlmInf == null) {
      return null;
    }

    SettlementInstruction7 instruction = new SettlementInstruction7();

    // 设置结算方法
    if (StringUtils.isNotBlank(sttlmInf.getSttlmMtd())) {
      instruction.setSttlmMtd(SettlementMethod1Code.valueOf(sttlmInf.getSttlmMtd()));
    }

    // 设置结算账户
    if (sttlmInf.getSttlmAcct() != null) {
      instruction.setSttlmAcct(createCashAccount(sttlmInf.getSttlmAcct()));
    }

    // 设置清算系统
    if (sttlmInf.getClrSys() != null) {
      ClearingSystemIdentification3Choice clrSys = new ClearingSystemIdentification3Choice();
      if (ValueType.CODE.equals(sttlmInf.getClrSys().getType())) {
        clrSys.setCd(sttlmInf.getClrSys().getValue());
      } else {
        clrSys.setPrtry(sttlmInf.getClrSys().getValue());
      }
      instruction.setClrSys(clrSys);
    }

    // 设置发起代理行
    if (sttlmInf.getInstgRmbrsmntAgt() != null) {
      instruction.setInstgRmbrsmntAgt(createFinancialInstitution(sttlmInf.getInstgRmbrsmntAgt()));
    }

    // 设置发起代理行账户
    if (sttlmInf.getInstgRmbrsmntAgtAcct() != null) {
      instruction.setInstgRmbrsmntAgtAcct(createCashAccount(sttlmInf.getInstgRmbrsmntAgtAcct()));
    }

    // 设置接收代理行
    if (sttlmInf.getInstdRmbrsmntAgt() != null) {
      instruction.setInstdRmbrsmntAgt(createFinancialInstitution(sttlmInf.getInstdRmbrsmntAgt()));
    }

    // 设置接收代理行账户
    if (sttlmInf.getInstdRmbrsmntAgtAcct() != null) {
      instruction.setInstdRmbrsmntAgtAcct(createCashAccount(sttlmInf.getInstdRmbrsmntAgtAcct()));
    }

    // 设置第三方代理行
    if (sttlmInf.getThrdRmbrsmntAgt() != null) {
      instruction.setThrdRmbrsmntAgt(createFinancialInstitution(sttlmInf.getThrdRmbrsmntAgt()));
    }

    // 设置第三方代理行账户
    if (sttlmInf.getThrdRmbrsmntAgtAcct() != null) {
      instruction.setThrdRmbrsmntAgtAcct(createCashAccount(sttlmInf.getThrdRmbrsmntAgtAcct()));
    }

    return instruction;
  }

  public static OffsetDateTime convertToOffsetDateTime(LocalDateTime localDateTime) {
    return localDateTime.atZone(ZoneOffset.systemDefault()).toOffsetDateTime();
  }
}
