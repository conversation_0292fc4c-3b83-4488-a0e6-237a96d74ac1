package com.bihu.swifttrack.request.message;

import java.math.BigDecimal;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import lombok.Data;

/**
 * StatementLine
 *
 * <p></p>
 *
 * <AUTHOR>
 * @date 2024/09/04
 */
@Data
public class StatementLine {

  /**
   * 起息日 (YYMMDD格式) 必填
   */
  @NotNull(groups = FullValidation.class)
  @Pattern(regexp = "\\d{6}", groups = {FullValidation.class, DraftValidation.class})
  private String valueDate;

  /**
   * 入账日 (MMDD格式) 可选
   */
  @Pattern(regexp = "\\d{6}", groups = {FullValidation.class, DraftValidation.class})
  private String entryDate;

  /**
   * 借贷标记 (D: 借, C: 贷) 必填，只能是 'D' 或 'C'
   */
  @NotNull(groups = FullValidation.class)
  @Pattern(regexp = "[DC]", groups = {FullValidation.class, DraftValidation.class})
  private String debitCreditMark;

  /**
   * 资金代码 (通常为F) 可选，如果存在只能是 'F'
   */
  @Pattern(regexp = "F")
  private String fundCode;

  @Pattern(regexp = "[A-Z]{3}", groups = {FullValidation.class, DraftValidation.class})
  private String currency;

  /**
   * 交易金额 必填，最多15位数字，小数点后最多2位
   */
  @NotNull(groups = FullValidation.class)
  @Digits(integer = 13, fraction = 2, groups = {FullValidation.class, DraftValidation.class})
  private BigDecimal amount;

  /**
   * 交易类型标识符 必填，3个字符
   */
  @NotNull(groups = FullValidation.class)
  @Size(min = 3, max = 3, groups = {FullValidation.class, DraftValidation.class})
  private String transactionType;

  /**
   * 标识码 必填，最大1个字符
   */
  @Size(max = 1, groups = {FullValidation.class, DraftValidation.class})
  private String identificationCode;

  /**
   * 客户参考号 必填，最多16个字符
   */
  @NotNull(groups = FullValidation.class)
  @Size(max = 16, groups = {FullValidation.class, DraftValidation.class})
  private String referenceForTheAccountOwner;

  /**
   * 银行参考号 可选，最多16个字符
   */
  @Size(max = 16, groups = {FullValidation.class, DraftValidation.class})
  private String referenceOfTheAccountServicingInstitution;

  /**
   * 补充信息 可选，最多34个字符
   */
  @Size(max = 34, groups = {FullValidation.class, DraftValidation.class})
  private String supplementaryDetails;
}
