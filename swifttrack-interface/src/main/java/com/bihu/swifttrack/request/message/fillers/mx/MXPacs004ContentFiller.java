package com.bihu.swifttrack.request.message.fillers.mx;

import static com.bihu.swifttrack.request.message.fillers.mx.MxHelper.createCategoryPurpose;
import static com.bihu.swifttrack.request.message.fillers.mx.MxHelper.createLocalInstrument;
import static com.bihu.swifttrack.utils.FieldUtils.setFieldToMessageDetails;
import static com.bihu.swifttrack.utils.MXMessageValidator.validateMXPacs004RequestInfo;

import com.bihu.swifttrack.dto.Amount;
import com.bihu.swifttrack.mybatis.repository.po.MessageDetailsPO;
import com.bihu.swifttrack.request.message.MessageContentFiller;
import com.bihu.swifttrack.request.message.MessageRequest;
import com.bihu.swifttrack.request.message.mx.Agent;
import com.bihu.swifttrack.request.message.mx.ChargesInformation;
import com.bihu.swifttrack.request.message.mx.GroupHeader;
import com.bihu.swifttrack.request.message.mx.MXPacs004RequestInfo;
import com.bihu.swifttrack.request.message.mx.PaymentTypeInformation;
import com.bihu.swifttrack.request.message.mx.TypedValue.ValueType;
import com.prowidesoftware.swift.model.MxSwiftMessage;
import com.prowidesoftware.swift.model.mx.MxPacs00400109;
import com.prowidesoftware.swift.model.mx.MxWriteConfiguration;
import com.prowidesoftware.swift.model.mx.dic.ActiveCurrencyAndAmount;
import com.prowidesoftware.swift.model.mx.dic.ActiveOrHistoricCurrencyAndAmount;
import com.prowidesoftware.swift.model.mx.dic.ChargeBearerType1Code;
import com.prowidesoftware.swift.model.mx.dic.Charges7;
import com.prowidesoftware.swift.model.mx.dic.ClearingChannel2Code;
import com.prowidesoftware.swift.model.mx.dic.GroupHeader90;
import com.prowidesoftware.swift.model.mx.dic.OriginalGroupInformation29;
import com.prowidesoftware.swift.model.mx.dic.OriginalTransactionReference28;
import com.prowidesoftware.swift.model.mx.dic.Party40Choice;
import com.prowidesoftware.swift.model.mx.dic.PaymentReturnReason6;
import com.prowidesoftware.swift.model.mx.dic.PaymentReturnV09;
import com.prowidesoftware.swift.model.mx.dic.PaymentTransaction112;
import com.prowidesoftware.swift.model.mx.dic.PaymentTypeInformation27;
import com.prowidesoftware.swift.model.mx.dic.Priority2Code;
import com.prowidesoftware.swift.model.mx.dic.RemittanceInformation16;
import com.prowidesoftware.swift.model.mx.dic.ReturnReason5Choice;
import com.prowidesoftware.swift.model.mx.dic.SettlementDateTimeIndication1;
import com.prowidesoftware.swift.model.mx.dic.SettlementInstruction7;
import com.prowidesoftware.swift.model.mx.dic.TransactionParties7;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;

public class MXPacs004ContentFiller implements MessageContentFiller {

  @Override
  public void fillContent(List<MessageDetailsPO> lists, MessageRequest messageRequest)
      throws Exception {
    MXPacs004RequestInfo requestInfo = messageRequest.getMxPacs004RequestInfo();
    validateMXPacs004RequestInfo(requestInfo);

    MxPacs00400109 mxPacs004 = new MxPacs00400109();
    fillMxPacs004(mxPacs004, requestInfo);

    setFieldToMessageDetails(
        "mxPacs004",
        new MxSwiftMessage(mxPacs004.message(MxHelper.getWriteConfiguration(null,null, false)))
            .message(),
        lists);
  }

  private void fillMxPacs004(MxPacs00400109 mxPacs004, MXPacs004RequestInfo requestInfo) {
    mxPacs004.setAppHdr(
        BusinessApplicationHeaderConverter.convert(requestInfo.getBusinessApplicationHeader()));
    mxPacs004.setPmtRtr(createPaymentReturn(requestInfo));
  }

  private PaymentReturnV09 createPaymentReturn(MXPacs004RequestInfo requestInfo) {
    PaymentReturnV09 pmtRtr = new PaymentReturnV09();
    pmtRtr.setGrpHdr(createGroupHeader(requestInfo.getGroupHeader()));
    requestInfo
        .getTransactionInformation()
        .forEach(
            txInfo ->
                pmtRtr.addTxInf(
                    createPaymentTransaction(
                        txInfo,
                        MxHelper.createAgent(requestInfo.getBusinessApplicationHeader().getFrom()),
                        MxHelper.createAgent(requestInfo.getBusinessApplicationHeader().getTo()))));
    return pmtRtr;
  }

  private GroupHeader90 createGroupHeader(GroupHeader groupHeader) {
    return new GroupHeader90()
        .setMsgId(groupHeader.getMsgId())
        .setCreDtTm(groupHeader.getCreDtTm().atZone(ZoneId.systemDefault()).toOffsetDateTime())
        .setNbOfTxs(groupHeader.getNbOfTxs())
        .setSttlmInf(createSettlementInstruction(groupHeader));
  }

  private SettlementInstruction7 createSettlementInstruction(GroupHeader groupHeader) {
    if (groupHeader.getSttlmInf() == null) {
      return null;
    }
    return MxHelper.createSettlementInstruction(groupHeader.getSttlmInf());
  }

  private PaymentTransaction112 createPaymentTransaction(
      MXPacs004RequestInfo.TransactionInformation txInfo, Agent instgAgt, Agent instdAgt) {
    PaymentTransaction112 tx =
        new PaymentTransaction112()
            .setRtrId(txInfo.getRtrId())
            .setOrgnlGrpInf(createOriginalGroupInformation(txInfo.getOrgnlGrpInf()))
            .setOrgnlInstrId(txInfo.getOrgnlInstrId())
            .setOrgnlEndToEndId(txInfo.getOrgnlEndToEndId())
            .setOrgnlUETR(txInfo.getOrgnlUETR())
            .setOrgnlTxId(txInfo.getOrgnlTxId())
            .setOrgnlIntrBkSttlmAmt(
                createActiveOrHistoricCurrencyAndAmount(txInfo.getOrgnlIntrBkSttlmAmt()))
            .setOrgnlIntrBkSttlmDt(txInfo.getOrgnlIntrBkSttlmDt())
            .setRtrdIntrBkSttlmAmt(createActiveCurrencyAndAmount(txInfo.getRtrdIntrBkSttlmAmt()))
            .setIntrBkSttlmDt(txInfo.getIntrBkSttlmDt())
            .setChrgBr(ChargeBearerType1Code.valueOf(txInfo.getChrgBr()));

    // 设置结算时间指示
    if (txInfo.getSttlmTmIndctn() != null) {
      SettlementDateTimeIndication1 sttlmTmIndctn = new SettlementDateTimeIndication1();

      if (txInfo.getSttlmTmIndctn().getDbtDtTm() != null) {
        sttlmTmIndctn.setDbtDtTm(txInfo.getSttlmTmIndctn().getDbtDtTm());
      }

      if (txInfo.getSttlmTmIndctn().getCdtDtTm() != null) {
        sttlmTmIndctn.setCdtDtTm(txInfo.getSttlmTmIndctn().getCdtDtTm());
      }

      tx.setSttlmTmIndctn(sttlmTmIndctn);
    }

    // 设置代理行信息
    if (txInfo.getInstgAgt() != null) {
      tx.setInstgAgt(MxHelper.createFinancialInstitution(txInfo.getInstgAgt()));
    } else if (instgAgt != null) {
      tx.setInstgAgt(MxHelper.createFinancialInstitution(instgAgt));
    }

    if (txInfo.getInstdAgt() != null) {
      tx.setInstdAgt(MxHelper.createFinancialInstitution(txInfo.getInstdAgt()));
    } else if (instdAgt != null) {
      tx.setInstdAgt(MxHelper.createFinancialInstitution(instdAgt));
    }

    if (txInfo.getRtrRsnInf() != null && !txInfo.getRtrRsnInf().isEmpty()) {
      txInfo.getRtrRsnInf().forEach(rsnInfo -> tx.addRtrRsnInf(createPaymentReturnReason(rsnInfo)));
    }
    tx.setOrgnlTxRef(createOriginalTransactionReference(txInfo.getOrgnlTxRef()));
    tx.setRtrChain(createTransactionParties(txInfo.getRtrChain()));

    // Add charges information
    if (txInfo.getChrgsInf() != null && !txInfo.getChrgsInf().isEmpty()) {
      txInfo
          .getChrgsInf()
          .forEach(chargeInfo -> tx.addChrgsInf(createChargesInformation(chargeInfo)));
    }

    Optional.ofNullable(txInfo.getRtrdInstdAmt())
        .ifPresent(
            rtrdInstdAmt ->
                tx.setRtrdInstdAmt(createActiveOrHistoricCurrencyAndAmount(rtrdInstdAmt)));

    return tx;
  }

  private OriginalGroupInformation29 createOriginalGroupInformation(
      MXPacs004RequestInfo.OriginalGroupInfo orgGrpInfo) {
    if (orgGrpInfo == null) {
      throw new IllegalArgumentException("orgGrpInfo cannot be null");
    }

    OriginalGroupInformation29 result =
        new OriginalGroupInformation29()
            .setOrgnlMsgId(orgGrpInfo.getOrgnlMsgId())
            .setOrgnlMsgNmId(orgGrpInfo.getOrgnlMsgNmId());

    // Handle original creation date time if present
    if (orgGrpInfo.getOrgnlCreDtTm() != null) {
      try {
        LocalDateTime dateTime = LocalDateTime.parse(orgGrpInfo.getOrgnlCreDtTm());
        result.setOrgnlCreDtTm(dateTime.atZone(ZoneId.systemDefault()).toOffsetDateTime());
      } catch (Exception ignored) {
      }
    }

    return result;
  }

  private ActiveOrHistoricCurrencyAndAmount createActiveOrHistoricCurrencyAndAmount(Amount amount) {
    if (amount == null) {
      return null;
    }
    return new ActiveOrHistoricCurrencyAndAmount()
        .setCcy(amount.getCurrency())
        .setValue(amount.getAmount());
  }

  private ActiveCurrencyAndAmount createActiveCurrencyAndAmount(Amount amount) {
    if (amount == null) {
      return null;
    }
    return new ActiveCurrencyAndAmount().setCcy(amount.getCurrency()).setValue(amount.getAmount());
  }

  private PaymentReturnReason6 createPaymentReturnReason(
      MXPacs004RequestInfo.ReturnReasonInformation rtrRsnInf) {
    if (rtrRsnInf == null) {
      return null;
    }
    PaymentReturnReason6 reason = new PaymentReturnReason6();
    Optional.ofNullable(rtrRsnInf.getRsn())
        .ifPresent(
            rsn -> {
              ReturnReason5Choice choice = new ReturnReason5Choice();
              if (ValueType.CODE.equals(rtrRsnInf.getRsn().getType())) {
                choice.setCd(rsn.getValue());
              } else {
                choice.setPrtry(rsn.getValue());
              }
              reason.setRsn(choice);
            });

    if (rtrRsnInf.getAddtlInf() != null) {
      Optional.of(rtrRsnInf.getAddtlInf()).ifPresent(inf -> inf.forEach(reason::addAddtlInf));
    }
    return reason;
  }

  private OriginalTransactionReference28 createOriginalTransactionReference(
      MXPacs004RequestInfo.OriginalTransactionReference orgTxRef) {
    OriginalTransactionReference28 txRef = new OriginalTransactionReference28();

    // Set InterBank Settlement Amount and Date
    if (orgTxRef.getIntrBkSttlmAmt() != null) {
      txRef.setIntrBkSttlmAmt(
          createActiveOrHistoricCurrencyAndAmount(orgTxRef.getIntrBkSttlmAmt()));
    }

    if (orgTxRef.getIntrBkSttlmDt() != null) {
      txRef.setIntrBkSttlmDt(orgTxRef.getIntrBkSttlmDt());
    }

    // Set Settlement Information
    if (orgTxRef.getSttlmInf() != null) {
      txRef.setSttlmInf(MxHelper.createSettlementInstruction(orgTxRef.getSttlmInf()));
    }

    // Set Payment Type Information
    if (orgTxRef.getPmtTpInf() != null) {
      txRef.setPmtTpInf(createPaymentTypeInformation(orgTxRef.getPmtTpInf()));
    }

    // Set Remittance Information
    if (orgTxRef.getRmtInf() != null) {
      RemittanceInformation16 rmtInf = new RemittanceInformation16();
      rmtInf.addUstrd(orgTxRef.getRmtInf());
      txRef.setRmtInf(rmtInf);
    }

    // Set Debtor Information
    if (orgTxRef.getDbtr() != null) {
      txRef.setDbtr(
          new Party40Choice().setPty(MxHelper.createPartyIdentification(orgTxRef.getDbtr())));
    }

    // Set Debtor Account
    if (orgTxRef.getDbtrAcct() != null) {
      txRef.setDbtrAcct(MxHelper.createCashAccount(orgTxRef.getDbtrAcct()));
    }

    // Set Debtor Agent
    if (orgTxRef.getDbtrAgt() != null) {
      txRef.setDbtrAgt(MxHelper.createFinancialInstitution(orgTxRef.getDbtrAgt()));
    }

    // Set Creditor Information
    if (orgTxRef.getCdtr() != null) {
      txRef.setCdtr(
          new Party40Choice().setPty(MxHelper.createPartyIdentification(orgTxRef.getCdtr())));
    }

    // Set Creditor Account
    if (orgTxRef.getCdtrAcct() != null) {
      txRef.setCdtrAcct(MxHelper.createCashAccount(orgTxRef.getCdtrAcct()));
    }

    // Set Creditor Agent
    if (orgTxRef.getCdtrAgt() != null) {
      txRef.setCdtrAgt(MxHelper.createFinancialInstitution(orgTxRef.getCdtrAgt()));
    }

    return txRef;
  }

  private PaymentTypeInformation27 createPaymentTypeInformation(PaymentTypeInformation pmtTpInf) {

    PaymentTypeInformation27 result = new PaymentTypeInformation27();

    if (StringUtils.isNotBlank(pmtTpInf.getInstrPrty())) {
      result.setInstrPrty(Priority2Code.valueOf(pmtTpInf.getInstrPrty()));
    }

    if (StringUtils.isNotBlank(pmtTpInf.getClrChanl())) {
      result.setClrChanl(ClearingChannel2Code.valueOf(pmtTpInf.getClrChanl()));
    }

    if (pmtTpInf.getSvcLvl() != null) {
      pmtTpInf.getSvcLvl().stream()
          .map(MxHelper::createServiceLevelChoice)
          .forEach(result::addSvcLvl);
    }

    if (pmtTpInf.getLclInstrm() != null) {
      result.setLclInstrm(createLocalInstrument(pmtTpInf.getLclInstrm()));
    }

    if (pmtTpInf.getCtgyPurp() != null) {
      result.setCtgyPurp(createCategoryPurpose(pmtTpInf.getCtgyPurp()));
    }

    return result;
  }

  private TransactionParties7 createTransactionParties(
      MXPacs004RequestInfo.ReturnChain returnChain) {
    TransactionParties7 parties = new TransactionParties7();

    if (returnChain.getDbtr() != null) {
      parties.setInitgPty(
          new Party40Choice().setPty(MxHelper.createPartyIdentification(returnChain.getDbtr())));
      parties.setDbtr(
          new Party40Choice().setPty(MxHelper.createPartyIdentification(returnChain.getDbtr())));
    }

    if (returnChain.getDbtrAgt() != null) {
      parties.setDbtrAgt(MxHelper.createFinancialInstitution(returnChain.getDbtrAgt()));
    }

    if (returnChain.getCdtr() != null) {
      parties.setCdtr(
          new Party40Choice().setPty(MxHelper.createPartyIdentification(returnChain.getCdtr())));
    }

    if (returnChain.getCdtrAgt() != null) {
      parties.setCdtrAgt(MxHelper.createFinancialInstitution(returnChain.getCdtrAgt()));
    }

    return parties;
  }

  private Charges7 createChargesInformation(ChargesInformation chargeInfo) {
    return new Charges7()
        .setAmt(createActiveOrHistoricCurrencyAndAmount(chargeInfo.getAmt()))
        .setAgt(MxHelper.createFinancialInstitution(chargeInfo.getAgt()));
  }
}
