package com.bihu.swifttrack.request.message.fillers.mx;

import static com.bihu.swifttrack.utils.FieldUtils.setFieldToMessageDetails;

import com.bihu.swifttrack.mybatis.repository.po.MessageDetailsPO;
import com.bihu.swifttrack.request.message.MessageContentFiller;
import com.bihu.swifttrack.request.message.MessageRequest;
import com.bihu.swifttrack.request.message.mx.MXCamt053RequestInfo;
import com.bihu.swifttrack.request.message.mx.TypedValue.ValueType;
import com.bihu.swifttrack.utils.MXMessageValidator;
import com.prowidesoftware.swift.model.MxSwiftMessage;
import com.prowidesoftware.swift.model.mx.MxCamt05300108;
import com.prowidesoftware.swift.model.mx.MxWriteConfiguration;
import com.prowidesoftware.swift.model.mx.dic.AccountStatement9;
import com.prowidesoftware.swift.model.mx.dic.BankToCustomerStatementV08;
import com.prowidesoftware.swift.model.mx.dic.DateAndDateTime2Choice;
import com.prowidesoftware.swift.model.mx.dic.DateTimePeriod1;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Optional;

public class MXCamt053ContentFiller implements MessageContentFiller {

  @Override
  public void fillContent(List<MessageDetailsPO> lists, MessageRequest messageRequest)
      throws Exception {
    validateInputs(lists, messageRequest);
    MxCamt05300108 mxCamt053 = createMxCamt053Message(messageRequest.getMxCamt053RequestInfo());
    setMessageToDetails(lists, mxCamt053);
  }

  private void validateInputs(List<MessageDetailsPO> lists, MessageRequest messageRequest) {
    if (lists == null) {
      throw new IllegalArgumentException("MessageDetailsPO list cannot be null");
    }
    if (messageRequest == null) {
      throw new IllegalArgumentException("MessageRequest cannot be null");
    }
    MXMessageValidator.validateMXCamt053RequestInfo(messageRequest.getMxCamt053RequestInfo());
  }

  private void setMessageToDetails(List<MessageDetailsPO> lists, MxCamt05300108 mxCamt053)
      throws Exception {
    setFieldToMessageDetails(
        "mxCamt053",
        new MxSwiftMessage(mxCamt053.message(MxHelper.getWriteConfiguration(null,null, false)))
            .message(),
        lists);
  }

  private MxCamt05300108 createMxCamt053Message(MXCamt053RequestInfo requestInfo) {
    MxCamt05300108 mxCamt053 = new MxCamt05300108();
    mxCamt053.setAppHdr(
        BusinessApplicationHeaderConverter.convert(requestInfo.getBusinessApplicationHeader()));
    mxCamt053.setBkToCstmrStmt(createBankToCustomerStatement(requestInfo));
    return mxCamt053;
  }

  private BankToCustomerStatementV08 createBankToCustomerStatement(
      MXCamt053RequestInfo requestInfo) {
    BankToCustomerStatementV08 statement = new BankToCustomerStatementV08();
    statement.setGrpHdr(MxHelper.createCamtGroupHeader(requestInfo.getGroupHeader()));

    Optional.ofNullable(requestInfo.getStatement())
        .ifPresent(stmts -> stmts.forEach(stmt -> statement.addStmt(createAccountStatement(stmt))));

    return statement;
  }

  private AccountStatement9 createAccountStatement(MXCamt053RequestInfo.AccountStatement stmt) {
    AccountStatement9 statement =
        new AccountStatement9()
            .setId(stmt.getId())
            .setElctrncSeqNb(stmt.getElctrncSeqNb())
            .setLglSeqNb(stmt.getLglSeqNb())
            .setCreDtTm(OffsetDateTime.of(stmt.getCreDtTm(), ZoneOffset.UTC));

    setStatementDateRange(statement, stmt);

    Optional.ofNullable(stmt.getCpyDplctInd()).ifPresent(statement::setCpyDplctInd);

    Optional.ofNullable(stmt.getRptgSeq()).ifPresent(statement::setRptgSeq);

    Optional.ofNullable(stmt.getStmtPgntn())
        .ifPresent(
            pgntn -> {
              com.prowidesoftware.swift.model.mx.dic.Pagination1 pagination =
                  new com.prowidesoftware.swift.model.mx.dic.Pagination1()
                      .setPgNb(pgntn.getPgNb())
                      .setLastPgInd(pgntn.getLastPgInd());
              statement.setStmtPgntn(pagination);
            });

    Optional.ofNullable(stmt.getRptgSrc())
        .ifPresent(
            src -> {
              com.prowidesoftware.swift.model.mx.dic.ReportingSource1Choice rptgSrc =
                  new com.prowidesoftware.swift.model.mx.dic.ReportingSource1Choice();
              if (ValueType.CODE.equals(src.getType())) {
                rptgSrc.setCd(src.getValue());
              } else {
                rptgSrc.setPrtry(src.getValue());
              }
              statement.setRptgSrc(rptgSrc);
            });

    Optional.ofNullable(stmt.getAcct())
        .map(MxHelper::createCashAccount39)
        .ifPresent(statement::setAcct);

    Optional.ofNullable(stmt.getRltdAcct())
        .ifPresent(rltdAcct -> statement.setRltdAcct(MxHelper.createCashAccount(rltdAcct)));

    MxHelper.addInterestInformation(stmt.getIntrst(), statement::addIntrst);

    setBalances(statement, stmt);

    Optional.ofNullable(stmt.getTxsSummry()).ifPresent(statement::setTxsSummry);

    Optional.ofNullable(stmt.getNtry())
        .filter(entries -> !entries.isEmpty())
        .ifPresent(
            entries ->
                entries.forEach(entry -> statement.addNtry(MxHelper.createReportEntry(entry))));

    Optional.ofNullable(stmt.getAddtlStmtInf()).ifPresent(statement::setAddtlStmtInf);

    return statement;
  }

  private void setStatementDateRange(
      AccountStatement9 statement, MXCamt053RequestInfo.AccountStatement stmt) {
    Optional.ofNullable(stmt.getFrToDt())
        .ifPresent(
            dateRange -> {
              DateTimePeriod1 frToDt = new DateTimePeriod1();
              Optional.ofNullable(dateRange.getFrDtTm())
                  .ifPresent(dt -> frToDt.setFrDtTm(OffsetDateTime.of(dt, ZoneOffset.UTC)));
              Optional.ofNullable(dateRange.getToDtTm())
                  .ifPresent(dt -> frToDt.setToDtTm(OffsetDateTime.of(dt, ZoneOffset.UTC)));
              statement.setFrToDt(frToDt);
            });
  }

  private void setBalances(
      AccountStatement9 statement, MXCamt053RequestInfo.AccountStatement stmt) {
    Optional.ofNullable(stmt.getBal())
        .filter(balances -> !balances.isEmpty())
        .ifPresent(
            balances ->
                balances.forEach(
                    balance -> {
                      com.prowidesoftware.swift.model.mx.dic.CashBalance8 prowideBalance =
                          new com.prowidesoftware.swift.model.mx.dic.CashBalance8()
                              .setTp(balance.getTp())
                              .setAmt(balance.getAmt())
                              .setCdtDbtInd(balance.getCdtDbtInd());

                      // Handle credit lines if present
                      Optional.ofNullable(balance.getCdtLine())
                          .filter(lines -> !lines.isEmpty())
                          .ifPresent(lines -> lines.forEach(prowideBalance::addCdtLine));

                      // Handle date
                      Optional.ofNullable(balance.getDt())
                          .ifPresent(
                              dt -> {
                                DateAndDateTime2Choice balanceDate = new DateAndDateTime2Choice();
                                Optional.ofNullable(dt.getDt()).ifPresent(balanceDate::setDt);
                                Optional.ofNullable(dt.getDtTm())
                                    .ifPresent(
                                        dateTm ->
                                            balanceDate.setDtTm(
                                                OffsetDateTime.of(dateTm, ZoneOffset.UTC)));
                                prowideBalance.setDt(balanceDate);
                              });

                      // Handle availability if present
                      Optional.ofNullable(balance.getAvlbty())
                          .filter(avl -> !avl.isEmpty())
                          .ifPresent(avl -> avl.forEach(prowideBalance::addAvlbty));

                      statement.addBal(prowideBalance);
                    }));
  }

}
