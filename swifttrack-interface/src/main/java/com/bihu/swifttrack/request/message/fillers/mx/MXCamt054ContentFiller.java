package com.bihu.swifttrack.request.message.fillers.mx;

import static com.bihu.swifttrack.utils.FieldUtils.setFieldToMessageDetails;
import static com.bihu.swifttrack.utils.MXMessageValidator.validateMXCamt054RequestInfo;
import static com.bihu.swifttrack.utils.MXMessageValidator.validateNotNull;

import com.bihu.swifttrack.mybatis.repository.po.MessageDetailsPO;
import com.bihu.swifttrack.request.message.MessageContentFiller;
import com.bihu.swifttrack.request.message.MessageRequest;
import com.bihu.swifttrack.request.message.mx.MXCamt054RequestInfo;
import com.bihu.swifttrack.request.message.mx.TypedValue.ValueType;
import com.prowidesoftware.swift.model.MxSwiftMessage;
import com.prowidesoftware.swift.model.mx.MxCamt05400108;
import com.prowidesoftware.swift.model.mx.MxWriteConfiguration;
import com.prowidesoftware.swift.model.mx.dic.AccountNotification17;
import com.prowidesoftware.swift.model.mx.dic.BankToCustomerDebitCreditNotificationV08;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Optional;

public class MXCamt054ContentFiller implements MessageContentFiller {

  @Override
  public void fillContent(List<MessageDetailsPO> lists, MessageRequest messageRequest)
      throws Exception {
    validateNotNull(lists, "MessageDetailsPO list");
    validateNotNull(messageRequest, "MessageRequest");

    MxCamt05400108 mxCamt054 = new MxCamt05400108();
    MXCamt054RequestInfo requestInfo = messageRequest.getMxCamt054RequestInfo();

    validateMXCamt054RequestInfo(requestInfo);

    fillMxCamt054(mxCamt054, requestInfo);

    setFieldToMessageDetails(
        "mxCamt054",
        new MxSwiftMessage(mxCamt054.message(MxHelper.getWriteConfiguration(null,null, false)))
            .message(),
        lists);
  }

  private void fillMxCamt054(MxCamt05400108 mxCamt054, MXCamt054RequestInfo requestInfo) {
    // Fill Business Application Header
    mxCamt054.setAppHdr(
        BusinessApplicationHeaderConverter.convert(requestInfo.getBusinessApplicationHeader()));

    // Fill BankToCustomerDebitCreditNotificationV08
    BankToCustomerDebitCreditNotificationV08 notification =
        new BankToCustomerDebitCreditNotificationV08();

    // Fill Group Header
    notification.setGrpHdr(MxHelper.createCamtGroupHeader(requestInfo.getGroupHeader()));

    for (MXCamt054RequestInfo.AccountNotification notif : requestInfo.getAccountNotification()) {
      notification.addNtfctn(createNotification(notif));
    }

    mxCamt054.setBkToCstmrDbtCdtNtfctn(notification);
  }

  private AccountNotification17 createNotification(MXCamt054RequestInfo.AccountNotification notif) {
    AccountNotification17 ntfctn =
        new AccountNotification17()
            .setId(notif.getId())
            .setCreDtTm(notif.getCreDtTm().atOffset(ZoneOffset.UTC));

    // Set Account using MxHelper
    Optional.ofNullable(notif.getAcct())
        .map(MxHelper::createCashAccount39)
        .ifPresent(ntfctn::setAcct);

    Optional.ofNullable(notif.getRltdAcct())
        .ifPresent(rltdAcct -> ntfctn.setRltdAcct(MxHelper.createCashAccount(rltdAcct)));

    Optional.ofNullable(notif.getNtfctnPgntn()).ifPresent(ntfctn::setNtfctnPgntn);

    Optional.ofNullable(notif.getElctrncSeqNb()).ifPresent(ntfctn::setElctrncSeqNb);

    Optional.ofNullable(notif.getRptgSeq()).ifPresent(ntfctn::setRptgSeq);

    Optional.ofNullable(notif.getLglSeqNb()).ifPresent(ntfctn::setLglSeqNb);

    Optional.ofNullable(notif.getFrToDt())
        .map(MxHelper::convertToDateTimePeriod)
        .ifPresent(ntfctn::setFrToDt);

    Optional.ofNullable(notif.getCpyDplctInd()).ifPresent(ntfctn::setCpyDplctInd);

    Optional.ofNullable(notif.getRptgSrc())
        .ifPresent(
            src -> {
              com.prowidesoftware.swift.model.mx.dic.ReportingSource1Choice rptgSrc =
                  new com.prowidesoftware.swift.model.mx.dic.ReportingSource1Choice();
              if (ValueType.CODE.equals(src.getType())) {
                rptgSrc.setCd(src.getValue());
              } else {
                rptgSrc.setPrtry(src.getValue());
              }
              ntfctn.setRptgSrc(rptgSrc);
            });

    Optional.ofNullable(notif.getTxsSummry()).ifPresent(ntfctn::setTxsSummry);

    Optional.ofNullable(notif.getAddtlNtfctnInf()).ifPresent(ntfctn::setAddtlNtfctnInf);

    // Handle interest information
    MxHelper.addInterestInformation(notif.getIntrst(), ntfctn::addIntrst);

    // Handle entries
    Optional.ofNullable(notif.getNtry())
        .filter(list -> !list.isEmpty())
        .ifPresent(
            entries -> entries.forEach(entry -> ntfctn.addNtry(MxHelper.createReportEntry(entry))));

    return ntfctn;
  }
}
