/**
 * 收款方信息类 包含收款方的详细信息，如银行信息、个人信息等
 *
 * <AUTHOR>
 * @date 2024/06/06
 */
package com.bihu.swifttrack.request.message;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "收款方信息")
public class PayeeInfo {

  @Schema(description = "收款方银行名称", example = "JPMorgan Chase")
  private String payeeBank;

  @Schema(description = "收款人名称", example = "<PERSON>")
  private String payeeName;

  @Schema(description = "收款人账号", example = "**********")
  private String payeeAccount;

  @Schema(description = "收款人地址", example = "456 Park Ave, New York, NY 10022")
  private String payeeAddress;

  @Schema(description = "收款方银行代码(BIC/LEI)", example = "CHASUS33XXX")
  private String payeeBankCode;

  @Schema(description = "收款方所在国家/地区代码", example = "US")
  private String payeeCountry;
}
