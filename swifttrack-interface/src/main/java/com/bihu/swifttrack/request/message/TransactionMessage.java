/**
 * 交易消息类 包含交易消息的基本信息，如交易参考号、相关引用号和消息内容
 *
 * <AUTHOR>
 * @date 2024/06/25
 */
package com.bihu.swifttrack.request.message;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "交易消息信息")
public class TransactionMessage {

  @Schema(description = "交易参考号，用于唯一标识一笔交易", example = "TRN2024001")
  private String transactionReferenceNumber;

  @Schema(description = "相关引用号，用于关联其他交易或消息", example = "REF2024001")
  private String relatedReferenceNumber;

  @Schema(description = "消息内容，包含具体的交易信息", example = "Payment for invoice #12345")
  private String messageContent;
}
