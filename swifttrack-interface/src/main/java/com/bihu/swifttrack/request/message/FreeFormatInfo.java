/**
 * 自由格式消息信息类 用于MT199/MT299/MT999等自由格式消息的内容
 *
 * <AUTHOR>
 * @date 2024/06/06
 */
package com.bihu.swifttrack.request.message;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "自由格式消息信息")
public class FreeFormatInfo {

  @Schema(description = "报文目的，说明发送此消息的目的", example = "Request for payment status")
  private String purpose;

  @Schema(
      description = "文本消息内容，自由格式的消息正文",
      example = "Please provide the status of payment reference TRN2024001")
  private String text;

  @Schema(description = "相关报文参考号，用于关联其他消息", example = "REF2024001")
  private String relatedMessageRefNum;
}
