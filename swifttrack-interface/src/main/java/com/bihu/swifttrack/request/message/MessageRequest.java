/**
 * SWIFT消息请求对象，用于封装各种类型的SWIFT消息请求信息。 支持MT和MX两种格式的消息，包括: - MT系列: MT103, MT202, MT199, MT299, MT910,
 * MT940, MT950, MT999 - MX系列: pacs.008, pacs.009, pacs.004, camt.053, camt.054
 *
 * <AUTHOR>
 * @date 2024/06/06
 */
package com.bihu.swifttrack.request.message;

import static com.bihu.swifttrack.swift.SwiftConstants.BIC_PATTERN;

import com.bihu.swifttrack.common.context.UserContext;
import com.bihu.swifttrack.common.enums.MessageFormat;
import com.bihu.swifttrack.common.enums.MessagingDetailFormField;
import com.bihu.swifttrack.common.enums.MessagingType;
import com.bihu.swifttrack.dto.AdditionalInfo;
import com.bihu.swifttrack.dto.BaseInfo;
import com.bihu.swifttrack.dto.TransactionInfo;
import com.bihu.swifttrack.enums.Direction;
import com.bihu.swifttrack.enums.StatusEnum;
import com.bihu.swifttrack.mybatis.repository.po.MessageDetailsPO;
import com.bihu.swifttrack.mybatis.repository.po.MessagesPO;
import com.bihu.swifttrack.mybatis.repository.po.ReceiverPO;
import com.bihu.swifttrack.mybatis.repository.po.SenderPO;
import com.bihu.swifttrack.param.AddMessagingParam;
import com.bihu.swifttrack.request.message.fillers.mt.MT103ContentFiller;
import com.bihu.swifttrack.request.message.fillers.mt.MT199OrMT299ContentFiller;
import com.bihu.swifttrack.request.message.fillers.mt.MT202ContentFiller;
import com.bihu.swifttrack.request.message.fillers.mt.MT910ContentFiller;
import com.bihu.swifttrack.request.message.fillers.mt.MT940ContentFiller;
import com.bihu.swifttrack.request.message.fillers.mt.MT950ContentFiller;
import com.bihu.swifttrack.request.message.fillers.mt.MT999ContentFiller;
import com.bihu.swifttrack.request.message.fillers.mx.MXCamt053ContentFiller;
import com.bihu.swifttrack.request.message.fillers.mx.MXCamt054ContentFiller;
import com.bihu.swifttrack.request.message.fillers.mx.MXPacs004ContentFiller;
import com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFiller;
import com.bihu.swifttrack.request.message.fillers.mx.MXPacs009ContentFiller;
import com.bihu.swifttrack.request.message.mx.MXCamt053RequestInfo;
import com.bihu.swifttrack.request.message.mx.MXCamt054RequestInfo;
import com.bihu.swifttrack.request.message.mx.MXPacs004RequestInfo;
import com.bihu.swifttrack.request.message.mx.MXPacs008RequestInfo;
import com.bihu.swifttrack.request.message.mx.MXPacs009RequestInfo;
import com.bihu.swifttrack.swift.SwiftMessageConvertor;
import com.bihu.swifttrack.utils.FieldUtils;
import com.google.common.collect.ImmutableMap;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "SWIFT消息请求对象，包含各种类型消息的详细信息")
public class MessageRequest {

  @Schema(description = "是否为初始处理标志", example = "true")
  private Boolean initialProcess;

  @Schema(description = "消息ID", example = "123456")
  private Long id;

  @Schema(description = "消息类型，如pacs.008、pacs.009等", example = "pacs.008")
  private String messagingType;

  @Schema(description = "基础信息，包含发送方、接收方等基本信息")
  private BaseInfo baseInfo;

  @Schema(description = "付款信息，包含付款人详细信息")
  private PaymentInfo paymentInfo;

  @Schema(description = "收款信息，包含收款人详细信息")
  private PayeeInfo payeeInfo;

  @Schema(description = "交易信息，包含金额、币种等交易相关信息")
  private TransactionInfo transactionInfo;

  @Schema(description = "附加信息，用于存储额外的业务信息")
  private AdditionalInfo additionalInfo;

  @Schema(description = "交易消息详情，包含具体的交易相关信息")
  private TransactionMessage transactionMessage;

  @Schema(description = "自由格式信息，用于MT199/MT299/MT999等自由格式消息")
  private FreeFormatInfo freeFormatInfo;

  @Schema(description = "MT950对账单请求信息")
  private MT950RequestInfo mt950RequestInfo;

  @Schema(description = "MT940对账单请求信息")
  private MT940RequestInfo mt940RequestInfo;

  @Schema(description = "贷记交易信息")
  private CreditTransactionInfo creditTransactionInfo;

  @Schema(description = "代理银行信息")
  private ProxyBankInfo proxyBankInfo;

  @Schema(description = "pacs.008(客户贷记转账)消息信息")
  private MXPacs008RequestInfo mxPacs008RequestInfo;

  @Schema(description = "pacs.009(金融机构贷记转账)消息信息")
  private MXPacs009RequestInfo mxPacs009RequestInfo;

  @Schema(description = "pacs.004(付款退回)消息信息")
  private MXPacs004RequestInfo mxPacs004RequestInfo;

  @Schema(description = "camt.053(银行对客户对账单)消息信息")
  private MXCamt053RequestInfo mxCamt053RequestInfo;

  @Schema(description = "camt.054(银行对客户借贷通知)消息信息")
  private MXCamt054RequestInfo mxCamt054RequestInfo;

  /** 消息内容填充器映射表，用于根据消息类型选择对应的内容填充器 支持MT和MX两种格式的所有消息类型 */
  private static final ImmutableMap<MessagingType, MessageContentFiller> fillerMap =
      ImmutableMap.<MessagingType, MessageContentFiller>builder()
          .put(MessagingType.CA103, new MT103ContentFiller())
          .put(MessagingType.MT103, new MT103ContentFiller())
          .put(MessagingType.MT202, new MT202ContentFiller())
          .put(MessagingType.MT199, new MT199OrMT299ContentFiller())
          .put(MessagingType.MT299, new MT199OrMT299ContentFiller())
          .put(MessagingType.MT910, new MT910ContentFiller())
          .put(MessagingType.MT940, new MT940ContentFiller())
          .put(MessagingType.MT950, new MT950ContentFiller())
          .put(MessagingType.MT999, new MT999ContentFiller())
          .put(MessagingType.MX_PACS008, new MXPacs008ContentFiller())
          .put(MessagingType.MX_PACS009, new MXPacs009ContentFiller())
          .put(MessagingType.MX_PACS004, new MXPacs004ContentFiller())
          .put(MessagingType.MX_CAMT053, new MXCamt053ContentFiller())
          .put(MessagingType.MX_CAMT054, new MXCamt054ContentFiller())
          .build();

  /**
   * 将请求对象转换为消息PO对象
   *
   * @param messageType 消息类型
   * @return MessagesPO对象
   */
  public MessagesPO toMessagesPo(MessagingType messageType) {
    MessagesPO messagesPO = new MessagesPO();
    messagesPO.setUpdatedBy(UserContext.getUserId());
    if (transactionInfo != null) {
      messagesPO.setAmount(transactionInfo.getAmount());
    }
    messagesPO.setMessageType(messageType.getType());
    messagesPO.setStatus(StatusEnum.MESSAGE_DRAFT.getCode());
    messagesPO.setDirection(Direction.Outcoming.getDir());
    messagesPO.setCreatedBy(UserContext.getUserId());
    messagesPO.setExtraInfo("");
    messagesPO.setAssignee(-1L);
    Optional.ofNullable(id).ifPresent(messagesPO::setId);
    return messagesPO;
  }

  /**
   * 将请求对象转换为发送方PO对象
   *
   * @return SenderPO对象
   */
  public SenderPO toSender() {
    SenderPO senderPO = new SenderPO();
    senderPO.setAccount(ObjectUtils.defaultIfNull(paymentInfo.getPayerAccount(), ""));
    senderPO.setAddress(ObjectUtils.defaultIfNull(paymentInfo.getPayerAddress(), ""));
    senderPO.setBicCode(ObjectUtils.defaultIfNull(paymentInfo.getPayerBankCode(), ""));
    senderPO.setName(ObjectUtils.defaultIfNull(paymentInfo.getPayerName(), ""));
    senderPO.setInstitution(ObjectUtils.defaultIfNull(paymentInfo.getPayerBank(), ""));
    senderPO.setCountry(ObjectUtils.defaultIfNull(paymentInfo.getPayerCountry(), ""));
    return senderPO;
  }

  /**
   * 将请求对象转换为接收方PO对象
   *
   * @return ReceiverPO对象
   */
  public ReceiverPO toReceiver() {
    ReceiverPO receiverPO = new ReceiverPO();
    receiverPO.setAccount(ObjectUtils.defaultIfNull(payeeInfo.getPayeeAccount(), ""));
    receiverPO.setAddress(ObjectUtils.defaultIfNull(payeeInfo.getPayeeAddress(), ""));
    receiverPO.setBicCode(ObjectUtils.defaultIfNull(payeeInfo.getPayeeBankCode(), ""));
    receiverPO.setInstitution(ObjectUtils.defaultIfNull(payeeInfo.getPayeeBank(), ""));
    receiverPO.setName(ObjectUtils.defaultIfNull(payeeInfo.getPayeeName(), ""));
    receiverPO.setCountry(ObjectUtils.defaultIfNull(payeeInfo.getPayeeCountry(), ""));
    return receiverPO;
  }

  /**
   * 将请求对象转换为消息详情PO对象列表
   *
   * @param type 消息类型
   * @param isDraft 是否为草稿
   * @return 消息详情PO对象列表
   * @throws Exception 转换过程中可能出现的异常
   */
  public List<MessageDetailsPO> toMessageDetail(MessagingType type, Boolean isDraft)
      throws Exception {
    ArrayList<MessageDetailsPO> lists = new ArrayList<>();

    if (transactionInfo != null) {
      FieldUtils.setFieldToMessageDetails(
          MessagingDetailFormField.TransactionInfo.getFieldName(), transactionInfo, lists);
    }
    if (additionalInfo != null) {
      FieldUtils.setFieldToMessageDetails(
          MessagingDetailFormField.AdditionalInfo.getFieldName(), additionalInfo, lists);
    }
    if (transactionMessage != null) {
      FieldUtils.setFieldToMessageDetails(
          MessagingDetailFormField.TransactionMessage.getFieldName(), transactionMessage, lists);
    }
    if (freeFormatInfo != null) {
      FieldUtils.setFieldToMessageDetails(
          MessagingDetailFormField.FreeFormatInfo.getFieldName(), freeFormatInfo, lists);
    }

    if (mt940RequestInfo != null) {
      FieldUtils.setFieldToMessageDetails(
          MessagingDetailFormField.MT940_INFO.getFieldName(), mt940RequestInfo, lists);
    }

    if (mt950RequestInfo != null) {
      FieldUtils.setFieldToMessageDetails(
          MessagingDetailFormField.MT950_INFO.getFieldName(), mt950RequestInfo, lists);
    }

    if (creditTransactionInfo != null) {
      FieldUtils.setFieldToMessageDetails(
          MessagingDetailFormField.CreditTransactionInfo.getFieldName(),
          creditTransactionInfo,
          lists);
    }

    if (proxyBankInfo != null) {
      FieldUtils.setFieldToMessageDetails(
          MessagingDetailFormField.PROXY_BANK_INFO.getFieldName(), proxyBankInfo, lists);
    }

    if (baseInfo != null) {
      if (baseInfo.getReceiver() != null && !baseInfo.getReceiver().matches(BIC_PATTERN)) {
        throw new IllegalArgumentException("Invalid BIC code");
      }
      if (baseInfo.getSender() != null && !baseInfo.getSender().matches(BIC_PATTERN)) {
        throw new IllegalArgumentException("Invalid BIC code");
      }

      FieldUtils.setFieldToMessageDetails(
          MessagingDetailFormField.BASE_INFO.getFieldName(), baseInfo, lists);
    }

    if (mxPacs008RequestInfo != null) {
      FieldUtils.setFieldToMessageDetails(
          MessagingDetailFormField.MX_PACS008_INFO.getFieldName(), mxPacs008RequestInfo, lists);
    }

    if (mxPacs009RequestInfo != null) {
      FieldUtils.setFieldToMessageDetails(
          MessagingDetailFormField.MX_PACS009_INFO.getFieldName(), mxPacs009RequestInfo, lists);
    }

    if (mxPacs004RequestInfo != null) {
      FieldUtils.setFieldToMessageDetails(
          MessagingDetailFormField.MX_PACS004_INFO.getFieldName(), mxPacs004RequestInfo, lists);
    }

    if (mxCamt053RequestInfo != null) {
      FieldUtils.setFieldToMessageDetails(
          MessagingDetailFormField.MX_CAMT053_INFO.getFieldName(), mxCamt053RequestInfo, lists);
    }

    if (mxCamt054RequestInfo != null) {
      FieldUtils.setFieldToMessageDetails(
          MessagingDetailFormField.MX_CAMT054_INFO.getFieldName(), mxCamt054RequestInfo, lists);
    }

    if (!isDraft) {
      Objects.requireNonNull(fillerMap.get(type)).fillContent(lists, this);
    }
    return lists;
  }

  /**
   * 将请求对象转换为消息参数对象
   *
   * @param type 消息类型
   * @param isDraft 是否为草稿
   * @return AddMessagingParam对象
   * @throws Exception 转换过程中可能出现的异常
   */
  public AddMessagingParam of(MessagingType type, Boolean isDraft) throws Exception {
    return new AddMessagingParam(
        this.toMessagesPo(type),
        this.toSender(),
        this.toReceiver(),
        this.toMessageDetail(type, isDraft));
  }

  /**
   * 获取标准格式的消息内容
   *
   * @param format 消息格式(MT/MX)
   * @param type 消息类型
   * @return 标准格式的消息内容字符串
   * @throws Exception 转换过程中可能出现的异常
   */
  public String getStandardFormat(MessageFormat format, MessagingType type) throws Exception {
    MessagesPO messagesPO = toMessagesPo(type);
    SenderPO senderPO = toSender();
    ReceiverPO receiverPO = toReceiver();
    List<MessageDetailsPO> messagesDetailList = toMessageDetail(type, false);

    return SwiftMessageConvertor.getStandardFormat(
        format, messagesPO, senderPO, receiverPO, messagesDetailList);
  }
}
