package com.bihu.swifttrack.request.message.fillers.mx;

import static com.bihu.swifttrack.utils.FieldUtils.setFieldToMessageDetails;

import com.bihu.swifttrack.mybatis.repository.po.MessageDetailsPO;
import com.bihu.swifttrack.request.message.MessageContentFiller;
import com.bihu.swifttrack.request.message.MessageRequest;
import com.bihu.swifttrack.request.message.mx.Agent;
import com.bihu.swifttrack.request.message.mx.BusinessApplicationHeader;
import com.bihu.swifttrack.request.message.mx.ChargesInformation;
import com.bihu.swifttrack.request.message.mx.GroupHeader;
import com.bihu.swifttrack.request.message.mx.MXPacs008RequestInfo;
import com.bihu.swifttrack.request.message.mx.MXPacs008RequestInfo.CreditTransferTransactionInformation39;
import com.bihu.swifttrack.request.message.mx.PaymentTypeInformation;
import com.bihu.swifttrack.request.message.mx.PreviousInstrictingInfo;
import com.bihu.swifttrack.request.message.mx.TaxInformation;
import com.bihu.swifttrack.request.message.mx.TypedValue;
import com.bihu.swifttrack.utils.MXMessageValidator;
import com.prowidesoftware.swift.model.MxSwiftMessage;
import com.prowidesoftware.swift.model.mx.MxPacs00800108;
import com.prowidesoftware.swift.model.mx.MxWriteConfiguration;
import com.prowidesoftware.swift.model.mx.dic.ActiveCurrencyAndAmount;
import com.prowidesoftware.swift.model.mx.dic.ActiveOrHistoricCurrencyAndAmount;
import com.prowidesoftware.swift.model.mx.dic.ChargeBearerType1Code;
import com.prowidesoftware.swift.model.mx.dic.Charges7;
import com.prowidesoftware.swift.model.mx.dic.ClearingChannel2Code;
import com.prowidesoftware.swift.model.mx.dic.CreditTransferTransaction39;
import com.prowidesoftware.swift.model.mx.dic.FIToFICustomerCreditTransferV08;
import com.prowidesoftware.swift.model.mx.dic.GroupHeader93;
import com.prowidesoftware.swift.model.mx.dic.Instruction3Code;
import com.prowidesoftware.swift.model.mx.dic.Instruction4Code;
import com.prowidesoftware.swift.model.mx.dic.InstructionForCreditorAgent1;
import com.prowidesoftware.swift.model.mx.dic.InstructionForNextAgent1;
import com.prowidesoftware.swift.model.mx.dic.PaymentTypeInformation28;
import com.prowidesoftware.swift.model.mx.dic.Priority2Code;
import com.prowidesoftware.swift.model.mx.dic.Priority3Code;
import com.prowidesoftware.swift.model.mx.dic.Purpose2Choice;
import com.prowidesoftware.swift.model.mx.dic.RegulatoryAuthority2;
import com.prowidesoftware.swift.model.mx.dic.RegulatoryReporting3;
import com.prowidesoftware.swift.model.mx.dic.RemittanceInformation16;
import com.prowidesoftware.swift.model.mx.dic.RemittanceLocation7;
import com.prowidesoftware.swift.model.mx.dic.SettlementDateTimeIndication1;
import com.prowidesoftware.swift.model.mx.dic.SettlementInstruction7;
import com.prowidesoftware.swift.model.mx.dic.SettlementTimeRequest2;
import com.prowidesoftware.swift.model.mx.dic.StructuredRegulatoryReporting3;
import com.prowidesoftware.swift.model.mx.dic.SupplementaryData1;
import com.prowidesoftware.swift.model.mx.dic.SupplementaryDataEnvelope1;
import com.prowidesoftware.swift.model.mx.dic.TaxAmount2;
import com.prowidesoftware.swift.model.mx.dic.TaxAuthorisation1;
import com.prowidesoftware.swift.model.mx.dic.TaxInformation8;
import com.prowidesoftware.swift.model.mx.dic.TaxParty1;
import com.prowidesoftware.swift.model.mx.dic.TaxParty2;
import com.prowidesoftware.swift.model.mx.dic.TaxPeriod2;
import com.prowidesoftware.swift.model.mx.dic.TaxRecord2;
import com.prowidesoftware.swift.model.mx.dic.TaxRecordDetails2;
import com.prowidesoftware.swift.model.mx.dic.TaxRecordPeriod1Code;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.List;
import org.apache.commons.lang3.StringUtils;

/**
 * MXPacs008ContentFiller
 *
 * <p>
 *
 * <AUTHOR>
 * @date 2024/10/08
 */
public class MXPacs008ContentFiller implements MessageContentFiller {

  @Override
  public void fillContent(List<MessageDetailsPO> messageDetails, MessageRequest messageRequest)
      throws Exception {
    MXPacs008RequestInfo requestInfo = messageRequest.getMxPacs008RequestInfo();

    MXMessageValidator.validateMXPacs008RequestInfo(requestInfo);

    setFieldToMessageDetails(
        "mxPacs008",
        new MxSwiftMessage(
                createMxPacs008(requestInfo)
                    .message(MxHelper.getWriteConfiguration(null,null, false)))
            .message(),
        messageDetails);
  }

  private MxPacs00800108 createMxPacs008(MXPacs008RequestInfo requestInfo) {
    MxPacs00800108 mxPacs008 = new MxPacs00800108();
    fillBusinessApplicationHeader(mxPacs008, requestInfo.getBusinessApplicationHeader());
    fillFIToFICustomerCreditTransfer(mxPacs008, requestInfo);
    return mxPacs008;
  }

  private void fillBusinessApplicationHeader(
      MxPacs00800108 mxPacs008, BusinessApplicationHeader header) {
    mxPacs008.setAppHdr(BusinessApplicationHeaderConverter.convert(header));
  }

  private void fillFIToFICustomerCreditTransfer(
      MxPacs00800108 mxPacs008, MXPacs008RequestInfo requestInfo) {
    FIToFICustomerCreditTransferV08 creditTransfer = new FIToFICustomerCreditTransferV08();

    fillGroupHeader(creditTransfer, requestInfo.getGroupHeader());
    fillCreditTransferTransactionInformation(
        creditTransfer,
        requestInfo.getCreditTransferTransactionInformation(),
        MxHelper.createAgent(requestInfo.getBusinessApplicationHeader().getFrom()),
        MxHelper.createAgent(requestInfo.getBusinessApplicationHeader().getTo()));

    mxPacs008.setFIToFICstmrCdtTrf(creditTransfer);
  }

  private void fillGroupHeader(
      FIToFICustomerCreditTransferV08 creditTransfer, GroupHeader groupHeader) {
    GroupHeader93 grpHdr = new GroupHeader93();

    // 基本字段
    grpHdr.setMsgId(groupHeader.getMsgId());
    grpHdr.setCreDtTm(convertToOffsetDateTime(groupHeader.getCreDtTm()));
    grpHdr.setNbOfTxs(groupHeader.getNbOfTxs());

    // 批量记账标志
    if (groupHeader.getBtchBookg() != null) {
      grpHdr.setBtchBookg(groupHeader.getBtchBookg());
    }

    // 控制总和
    if (groupHeader.getCtrlSum() != null) {
      grpHdr.setCtrlSum(groupHeader.getCtrlSum());
    }

    // 总银行间结算金额
    if (groupHeader.getTtlIntrBkSttlmAmt() != null) {
      ActiveCurrencyAndAmount amount = new ActiveCurrencyAndAmount();
      amount.setCcy(groupHeader.getTtlIntrBkSttlmAmt().getCurrency());
      amount.setValue(groupHeader.getTtlIntrBkSttlmAmt().getAmount());
      grpHdr.setTtlIntrBkSttlmAmt(amount);
    }

    // 银行间结算日期
    if (groupHeader.getIntrBkSttlmDt() != null) {
      grpHdr.setIntrBkSttlmDt(groupHeader.getIntrBkSttlmDt());
    }

    // 结算信息
    grpHdr.setSttlmInf(createSettlementInstruction(groupHeader));

    // 支付类型信息
    if (groupHeader.getPmtTpInf() != null) {
      grpHdr.setPmtTpInf(createPaymentTypeInformation(groupHeader.getPmtTpInf()));
    }

    // 发起代理机构
    if (groupHeader.getInstgAgt() != null) {
      grpHdr.setInstgAgt(MxHelper.createFinancialInstitution(groupHeader.getInstgAgt()));
    }

    // 接收代理机构
    if (groupHeader.getInstdAgt() != null) {
      grpHdr.setInstdAgt(MxHelper.createFinancialInstitution(groupHeader.getInstdAgt()));
    }

    creditTransfer.setGrpHdr(grpHdr);
  }

  private PaymentTypeInformation28 createPaymentTypeInformation(PaymentTypeInformation pmtTpInf) {
    PaymentTypeInformation28 result = new PaymentTypeInformation28();

    // 指令优先级
    if (StringUtils.isNotBlank(pmtTpInf.getInstrPrty())) {
      result.setInstrPrty(Priority2Code.valueOf(pmtTpInf.getInstrPrty()));
    }

    // 清算渠道
    if (StringUtils.isNotBlank(pmtTpInf.getClrChanl())) {
      result.setClrChanl(ClearingChannel2Code.valueOf(pmtTpInf.getClrChanl()));
    }

    // 服务级别
    if (pmtTpInf.getSvcLvl() != null && !pmtTpInf.getSvcLvl().isEmpty()) {
      for (TypedValue svcLvl : pmtTpInf.getSvcLvl()) {
        result.addSvcLvl(MxHelper.createServiceLevel(svcLvl));
      }
    }

    // 本地工具
    if (pmtTpInf.getLclInstrm() != null) {
      result.setLclInstrm(MxHelper.createLocalInstrument(pmtTpInf.getLclInstrm()));
    }

    // 类别目的
    if (pmtTpInf.getCtgyPurp() != null) {
      result.setCtgyPurp(MxHelper.createCategoryPurpose(pmtTpInf.getCtgyPurp()));
    }

    return result;
  }

  private void fillCreditTransferTransactionInformation(
      FIToFICustomerCreditTransferV08 creditTransfer,
      CreditTransferTransactionInformation39 txInfo,
      Agent instgAgt,
      Agent instdAgt) {
    CreditTransferTransaction39 cdtTrfTxInf = new CreditTransferTransaction39();

    // 设置支付标识信息
    cdtTrfTxInf.setPmtId(MxHelper.createPaymentIdentification(txInfo.getPmtId()));

    // 设置结算金额和日期
    cdtTrfTxInf.setIntrBkSttlmAmt(createActiveCurrencyAndAmount(txInfo.getIntrBkSttlmAmt()));
    cdtTrfTxInf.setIntrBkSttlmDt(txInfo.getIntrBkSttlmDt());

    // 设置指示金额
    if (txInfo.getInstdAmt() != null) {
      cdtTrfTxInf.setInstdAmt(createActiveOrHistoricCurrencyAndAmount(txInfo.getInstdAmt()));
    }

    // 设置费用承担方和费用信息
    if (StringUtils.isNotBlank(txInfo.getChrgBr())) {
      cdtTrfTxInf.setChrgBr(ChargeBearerType1Code.valueOf(txInfo.getChrgBr()));

      // 只有在 SHAR 或 DEBT 或 CRED 时才设置费用信息
      if (("SHAR".equals(txInfo.getChrgBr())
              || "DEBT".equals(txInfo.getChrgBr())
              || "CRED".equals(txInfo.getChrgBr()))
          && txInfo.getChrgsInf() != null
          && !txInfo.getChrgsInf().isEmpty()) {
        txInfo
            .getChrgsInf()
            .forEach(chargeInfo -> cdtTrfTxInf.addChrgsInf(createCharges7(chargeInfo)));
      }
    }

    // 设置代理行信息
    if (txInfo.getInstgAgt() != null) {
      cdtTrfTxInf.setInstgAgt(MxHelper.createFinancialInstitution(txInfo.getInstgAgt()));
    } else if (instgAgt != null) {
      cdtTrfTxInf.setInstgAgt(MxHelper.createFinancialInstitution(instgAgt));
    }

    if (txInfo.getInstdAgt() != null) {
      cdtTrfTxInf.setInstdAgt(MxHelper.createFinancialInstitution(txInfo.getInstdAgt()));
    } else if (instdAgt != null) {
      cdtTrfTxInf.setInstdAgt(MxHelper.createFinancialInstitution(instdAgt));
    }

    // Set intermediary agent 1
    if (txInfo.getIntrmyAgt1() != null) {
      cdtTrfTxInf.setIntrmyAgt1(MxHelper.createFinancialInstitution(txInfo.getIntrmyAgt1()));
    }
    if (txInfo.getIntrmyAgt1Acct() != null) {
      cdtTrfTxInf.setIntrmyAgt1Acct(MxHelper.createCashAccount(txInfo.getIntrmyAgt1Acct()));
    }

    // Set intermediary agent 2
    if (txInfo.getIntrmyAgt2() != null) {
      cdtTrfTxInf.setIntrmyAgt2(MxHelper.createFinancialInstitution(txInfo.getIntrmyAgt2()));
    }
    if (txInfo.getIntrmyAgt2Acct() != null) {
      cdtTrfTxInf.setIntrmyAgt2Acct(MxHelper.createCashAccount(txInfo.getIntrmyAgt2Acct()));
    }

    // Set intermediary agent 3
    if (txInfo.getIntrmyAgt3() != null) {
      cdtTrfTxInf.setIntrmyAgt3(MxHelper.createFinancialInstitution(txInfo.getIntrmyAgt3()));
    }
    if (txInfo.getIntrmyAgt3Acct() != null) {
      cdtTrfTxInf.setIntrmyAgt3Acct(MxHelper.createCashAccount(txInfo.getIntrmyAgt3Acct()));
    }

    // 设置付款人和收款人信息
    cdtTrfTxInf.setDbtr(MxHelper.createPartyIdentification(txInfo.getDebtor()));
    cdtTrfTxInf.setDbtrAcct(MxHelper.createCashAccount(txInfo.getDebtor().getAcctId()));
    cdtTrfTxInf.setDbtrAgt(MxHelper.createFinancialInstitution(txInfo.getDbtrAgt()));

    // 设置债务人代理账户
    if (txInfo.getDbtrAgtAcct() != null) {
      cdtTrfTxInf.setDbtrAgtAcct(MxHelper.createCashAccount(txInfo.getDbtrAgtAcct()));
    }

    cdtTrfTxInf.setCdtr(MxHelper.createPartyIdentification(txInfo.getCreditor()));
    cdtTrfTxInf.setCdtrAcct(MxHelper.createCashAccount(txInfo.getCreditor().getAcctId()));
    cdtTrfTxInf.setCdtrAgt(MxHelper.createFinancialInstitution(txInfo.getCdtrAgt()));

    // 设置债权人代理账户
    if (txInfo.getCdtrAgtAcct() != null) {
      cdtTrfTxInf.setCdtrAgtAcct(MxHelper.createCashAccount(txInfo.getCdtrAgtAcct()));
    }

    // 设置最终债权人
    if (txInfo.getUltmtCdtr() != null) {
      cdtTrfTxInf.setUltmtCdtr(MxHelper.createPartyIdentification(txInfo.getUltmtCdtr()));
    }

    // 设置债权人代理指令
    if (txInfo.getInstrForCdtrAgt() != null && !txInfo.getInstrForCdtrAgt().isEmpty()) {
      txInfo
          .getInstrForCdtrAgt()
          .forEach(
              instrForCdtrAgt -> {
                InstructionForCreditorAgent1 instruction = new InstructionForCreditorAgent1();
                if (instrForCdtrAgt.getCd() != null) {
                  instruction.setCd(Instruction3Code.valueOf(instrForCdtrAgt.getCd()));
                }
                if (instrForCdtrAgt.getInstrInf() != null) {
                  instruction.setInstrInf(instrForCdtrAgt.getInstrInf());
                }
                cdtTrfTxInf.addInstrForCdtrAgt(instruction);
              });
    }

    // 设置下一代理指令
    if (txInfo.getInstrForNxtAgt() != null && !txInfo.getInstrForNxtAgt().isEmpty()) {
      txInfo
          .getInstrForNxtAgt()
          .forEach(
              instrForNxtAgt -> {
                InstructionForNextAgent1 instruction = new InstructionForNextAgent1();
                if (instrForNxtAgt.getCd() != null) {
                  instruction.setCd(Instruction4Code.valueOf(instrForNxtAgt.getCd()));
                }
                if (instrForNxtAgt.getInstrInf() != null) {
                  instruction.setInstrInf(instrForNxtAgt.getInstrInf());
                }
                cdtTrfTxInf.addInstrForNxtAgt(instruction);
              });
    }

    // 设置汇款信息和用途
    cdtTrfTxInf.setRmtInf(createRemittanceInformation(txInfo.getRmtInf()));
    if (txInfo.getPurp() != null) {
      Purpose2Choice purp = new Purpose2Choice();
      if (txInfo.getPurp().getType() == TypedValue.ValueType.CODE) {
        purp.setCd(txInfo.getPurp().getValue());
      } else {
        purp.setPrtry(txInfo.getPurp().getValue());
      }
      cdtTrfTxInf.setPurp(purp);
    }

    // 设置支付类型信息
    if (txInfo.getPmtTpInf() != null) {
      cdtTrfTxInf.setPmtTpInf(MxHelper.createPaymentTypeInformation(txInfo.getPmtTpInf()));
    }

    // 设置前序指示信息
    if (txInfo.getPreviousInstrictingInfos() != null
        && !txInfo.getPreviousInstrictingInfos().isEmpty()) {
      setPreviousInstructingAgents(cdtTrfTxInf, txInfo.getPreviousInstrictingInfos());
    }

    // 设置税务信息
    if (txInfo.getTax() != null) {
      cdtTrfTxInf.setTax(createTaxInformation(txInfo.getTax()));
    }

    // 设置相关汇款信息
    if (txInfo.getRltdRmtInf() != null && !txInfo.getRltdRmtInf().isEmpty()) {
      txInfo
          .getRltdRmtInf()
          .forEach(
              relatedRmtInfo -> {
                RemittanceLocation7 rmtLoc = new RemittanceLocation7();

                if (relatedRmtInfo.getRmtId() != null) {
                  rmtLoc.setRmtId(relatedRmtInfo.getRmtId());
                }

                cdtTrfTxInf.addRltdRmtInf(rmtLoc);
              });
    }

    // 设置补充数据
    setSupplementaryData(cdtTrfTxInf, txInfo);

    // 设置监管报告
    if (txInfo.getRgltryRptg() != null && !txInfo.getRgltryRptg().isEmpty()) {
      txInfo
          .getRgltryRptg()
          .forEach(
              regReporting -> {
                RegulatoryReporting3 rr = new RegulatoryReporting3();

                if (regReporting.getAuthorityName() != null) {
                  RegulatoryAuthority2 authority = new RegulatoryAuthority2();
                  authority.setNm(regReporting.getAuthorityName());

                  if (regReporting.getAuthorityCountry() != null) {
                    authority.setCtry(regReporting.getAuthorityCountry());
                  }

                  rr.setAuthrty(authority);
                }

                if (regReporting.getDetails() != null && !regReporting.getDetails().isEmpty()) {
                  regReporting
                      .getDetails()
                      .forEach(
                          detail -> {
                            StructuredRegulatoryReporting3 structDetail =
                                new StructuredRegulatoryReporting3();

                            if (detail.getType() != null) {
                              structDetail.setTp(detail.getType());
                            }

                            if (detail.getDate() != null) {
                              try {
                                LocalDate date = LocalDate.parse(detail.getDate());
                                structDetail.setDt(date);
                              } catch (Exception e) {
                                // 日期格式不正确，忽略
                              }
                            }

                            if (detail.getCountry() != null) {
                              structDetail.setCtry(detail.getCountry());
                            }

                            if (detail.getCode() != null) {
                              structDetail.setCd(detail.getCode());
                            }

                            if (detail.getAmount() != null) {
                              structDetail.setAmt(
                                  createActiveOrHistoricCurrencyAndAmount(detail.getAmount()));
                            }

                            if (detail.getInformation() != null) {
                              structDetail.addInf(detail.getInformation());
                            }

                            rr.addDtls(structDetail);
                          });
                }

                cdtTrfTxInf.addRgltryRptg(rr);
              });
    }

    // 设置结算时间指示
    if (txInfo.getSttlmTmIndctn() != null) {
      SettlementDateTimeIndication1 sttlmTmIndctn = new SettlementDateTimeIndication1();

      if (txInfo.getSttlmTmIndctn().getDbtDtTm() != null) {
        sttlmTmIndctn.setDbtDtTm(txInfo.getSttlmTmIndctn().getDbtDtTm());
      }

      if (txInfo.getSttlmTmIndctn().getCdtDtTm() != null) {
        sttlmTmIndctn.setCdtDtTm(txInfo.getSttlmTmIndctn().getCdtDtTm());
      }

      cdtTrfTxInf.setSttlmTmIndctn(sttlmTmIndctn);
    }

    // 设置结算时间请求
    if (txInfo.getSttlmTmReq() != null) {
      SettlementTimeRequest2 sttlmTmReq = new SettlementTimeRequest2();

      if (txInfo.getSttlmTmReq().getFrTm() != null) {
        sttlmTmReq.setFrTm(txInfo.getSttlmTmReq().getFrTm());
      }

      if (txInfo.getSttlmTmReq().getRjctTm() != null) {
        sttlmTmReq.setRjctTm(txInfo.getSttlmTmReq().getRjctTm());
      }

      cdtTrfTxInf.setSttlmTmReq(sttlmTmReq);
    }

    // 设置结算优先级
    if (txInfo.getSttlmPrty() != null) {
      try {
        Priority3Code priority = Priority3Code.valueOf(txInfo.getSttlmPrty());
        cdtTrfTxInf.setSttlmPrty(priority);
      } catch (IllegalArgumentException e) {
        // 忽略无效的优先级
      }
    }

    // 设置接受日期时间
    if (txInfo.getAccptncDtTm() != null) {
      cdtTrfTxInf.setAccptncDtTm(txInfo.getAccptncDtTm().atOffset(ZoneOffset.UTC));
    }

    // 设置汇率
    if (txInfo.getXchgRate() != null) {
      cdtTrfTxInf.setXchgRate(txInfo.getXchgRate());
    }

    // 设置最终债务人
    if (txInfo.getUltmtDbtr() != null) {
      cdtTrfTxInf.setUltmtDbtr(MxHelper.createPartyIdentification(txInfo.getUltmtDbtr()));
    }

    // 设置发起方
    if (txInfo.getInitgPty() != null) {
      cdtTrfTxInf.setInitgPty(MxHelper.createPartyIdentification(txInfo.getInitgPty()));
    }

    creditTransfer.addCdtTrfTxInf(cdtTrfTxInf);
  }

  private void setPreviousInstructingAgents(
      CreditTransferTransaction39 cdtTrfTxInf, List<PreviousInstrictingInfo> prvsInstgInfoList) {

    for (int i = 0; i < Math.min(prvsInstgInfoList.size(), 3); i++) {
      PreviousInstrictingInfo prvsInstgInfo = prvsInstgInfoList.get(i);

      if (prvsInstgInfo == null) {
        continue;
      }

      switch (i) {
        case 0:
          if (prvsInstgInfo.getPrvsInstgAgt() != null) {
            cdtTrfTxInf.setPrvsInstgAgt1(
                MxHelper.createFinancialInstitution(prvsInstgInfo.getPrvsInstgAgt()));
          }
          if (prvsInstgInfo.getPrvsInstgAgtAcct() != null) {
            cdtTrfTxInf.setPrvsInstgAgt1Acct(
                MxHelper.createCashAccount(prvsInstgInfo.getPrvsInstgAgtAcct()));
          }
          break;
        case 1:
          if (prvsInstgInfo.getPrvsInstgAgt() != null) {
            cdtTrfTxInf.setPrvsInstgAgt2(
                MxHelper.createFinancialInstitution(prvsInstgInfo.getPrvsInstgAgt()));
          }
          if (prvsInstgInfo.getPrvsInstgAgtAcct() != null) {
            cdtTrfTxInf.setPrvsInstgAgt2Acct(
                MxHelper.createCashAccount(prvsInstgInfo.getPrvsInstgAgtAcct()));
          }
          break;
        case 2:
          if (prvsInstgInfo.getPrvsInstgAgt() != null) {
            cdtTrfTxInf.setPrvsInstgAgt3(
                MxHelper.createFinancialInstitution(prvsInstgInfo.getPrvsInstgAgt()));
          }
          if (prvsInstgInfo.getPrvsInstgAgtAcct() != null) {
            cdtTrfTxInf.setPrvsInstgAgt3Acct(
                MxHelper.createCashAccount(prvsInstgInfo.getPrvsInstgAgtAcct()));
          }
          break;
      }
    }
  }

  private SettlementInstruction7 createSettlementInstruction(GroupHeader groupHeader) {
    if (groupHeader.getSttlmInf() == null) {
      return null;
    }
    return MxHelper.createSettlementInstruction(groupHeader.getSttlmInf());
  }

  private ActiveCurrencyAndAmount createActiveCurrencyAndAmount(
      com.bihu.swifttrack.dto.Amount amount) {
    return new ActiveCurrencyAndAmount().setCcy(amount.getCurrency()).setValue(amount.getAmount());
  }

  private RemittanceInformation16 createRemittanceInformation(List<String> remittanceInfoList) {
    if (remittanceInfoList == null || remittanceInfoList.isEmpty()) {
      return null;
    }

    RemittanceInformation16 rmtInf = new RemittanceInformation16();
    remittanceInfoList.forEach(
        info -> {
          MXMessageValidator.validateLength(info, 1, 140, "Remittance Information");
          rmtInf.addUstrd(info);
        });
    return rmtInf;
  }

  private OffsetDateTime convertToOffsetDateTime(LocalDateTime localDateTime) {
    return localDateTime.atZone(ZoneOffset.systemDefault()).toOffsetDateTime();
  }

  private ActiveOrHistoricCurrencyAndAmount createActiveOrHistoricCurrencyAndAmount(
      com.bihu.swifttrack.dto.Amount amount) {
    return new ActiveOrHistoricCurrencyAndAmount()
        .setCcy(amount.getCurrency())
        .setValue(amount.getAmount());
  }

  private Charges7 createCharges7(ChargesInformation chargeInfo) {
    Charges7 charges = new Charges7();
    charges.setAmt(createActiveOrHistoricCurrencyAndAmount(chargeInfo.getAmt()));
    charges.setAgt(MxHelper.createFinancialInstitution(chargeInfo.getAgt()));
    return charges;
  }

  private TaxInformation8 createTaxInformation(TaxInformation taxInfo) {
    TaxInformation8 taxInformation = new TaxInformation8();

    // 设置债权人
    if (taxInfo.getCreditor() != null) {
      TaxParty1 creditor = new TaxParty1();

      if (taxInfo.getCreditor().getTaxId() != null) {
        creditor.setTaxId(taxInfo.getCreditor().getTaxId());
      }

      if (taxInfo.getCreditor().getRegnId() != null) {
        creditor.setRegnId(taxInfo.getCreditor().getRegnId());
      }

      if (taxInfo.getCreditor().getTaxTp() != null) {
        creditor.setTaxTp(taxInfo.getCreditor().getTaxTp());
      }

      taxInformation.setCdtr(creditor);
    }

    // 设置债务人
    if (taxInfo.getDebtor() != null) {
      TaxParty2 debtor = new TaxParty2();

      if (taxInfo.getDebtor().getTaxId() != null) {
        debtor.setTaxId(taxInfo.getDebtor().getTaxId());
      }

      if (taxInfo.getDebtor().getRegnId() != null) {
        debtor.setRegnId(taxInfo.getDebtor().getRegnId());
      }

      if (taxInfo.getDebtor().getTaxTp() != null) {
        debtor.setTaxTp(taxInfo.getDebtor().getTaxTp());
      }

      if (taxInfo.getDebtor().getAuthrty() != null) {
        TaxAuthorisation1 auth = new TaxAuthorisation1();

        // 注意：由于 TaxAuthorisation1 类中没有 setTitle 方法，我们暂时不设置这个字段

        if (taxInfo.getDebtor().getAuthrty().getNm() != null) {
          auth.setNm(taxInfo.getDebtor().getAuthrty().getNm());
        }

        // 注意：由于 TaxParty2 类中没有 setAuthorisation 方法，我们暂时不设置这个字段
      }

      taxInformation.setDbtr(debtor);
    }

    // 设置管理区
    if (taxInfo.getAdmstnZone() != null) {
      taxInformation.setAdmstnZone(taxInfo.getAdmstnZone());
    }

    // 设置参考编号
    if (taxInfo.getRefNb() != null) {
      taxInformation.setRefNb(taxInfo.getRefNb());
    }

    // 设置方法
    if (taxInfo.getMtd() != null) {
      taxInformation.setMtd(taxInfo.getMtd());
    }

    // 设置总金额
    if (taxInfo.getTotalAmount() != null) {
      taxInformation.setTtlTaxAmt(
          createActiveOrHistoricCurrencyAndAmount(taxInfo.getTotalAmount()));
    }

    // 设置日期
    if (taxInfo.getDate() != null) {
      try {
        LocalDate date = LocalDate.parse(taxInfo.getDate());
        taxInformation.setDt(date);
      } catch (Exception e) {
        // 日期格式不正确，忽略
      }
    }

    // 设置序列号
    if (taxInfo.getSequenceNumber() != null) {
      try {
        BigDecimal seqNb = new BigDecimal(taxInfo.getSequenceNumber());
        taxInformation.setSeqNb(seqNb);
      } catch (NumberFormatException e) {
        // 序列号格式不正确，忽略
      }
    }

    // 设置税务记录
    if (taxInfo.getRecords() != null && !taxInfo.getRecords().isEmpty()) {
      taxInfo
          .getRecords()
          .forEach(
              record -> {
                TaxRecord2 taxRecord = new TaxRecord2();

                if (record.getType() != null) {
                  taxRecord.setTp(record.getType());
                }

                if (record.getCategory() != null) {
                  taxRecord.setCtgy(record.getCategory());
                }

                if (record.getCategoryDetails() != null) {
                  taxRecord.setCtgyDtls(record.getCategoryDetails());
                }

                if (record.getDebtorStatus() != null) {
                  taxRecord.setDbtrSts(record.getDebtorStatus());
                }

                if (record.getCertificateId() != null) {
                  taxRecord.setCertId(record.getCertificateId());
                }

                if (record.getFrmsCd() != null) {
                  taxRecord.setFrmsCd(record.getFrmsCd());
                }

                if (record.getPeriod() != null) {
                  TaxPeriod2 period = new TaxPeriod2();

                  if (record.getPeriod().getYear() != null) {
                    try {
                      LocalDate year = LocalDate.parse(record.getPeriod().getYear() + "-01-01");
                      period.setYr(year);
                    } catch (Exception e) {
                      // 年份格式不正确，忽略
                    }
                  }

                  if (record.getPeriod().getType() != null) {
                    try {
                      TaxRecordPeriod1Code tp =
                          TaxRecordPeriod1Code.valueOf(record.getPeriod().getType());
                      period.setTp(tp);
                    } catch (IllegalArgumentException e) {
                      // 类型不正确，忽略
                    }
                  }

                  // 注意：由于 TaxPeriod2 类中没有 setFromDate 和 setToDate 方法，我们暂时不设置这些字段

                  taxRecord.setPrd(period);
                }

                if (record.getAmount() != null) {
                  TaxAmount2 taxAmount = new TaxAmount2();

                  if (record.getAmount().getRate() != null) {
                    try {
                      BigDecimal rate = new BigDecimal(record.getAmount().getRate());
                      taxAmount.setRate(rate);
                    } catch (NumberFormatException e) {
                      // 税率格式不正确，忽略
                    }
                  }

                  if (record.getAmount().getTaxableBaseAmount() != null) {
                    taxAmount.setTaxblBaseAmt(
                        createActiveOrHistoricCurrencyAndAmount(
                            record.getAmount().getTaxableBaseAmount()));
                  }

                  if (record.getAmount().getTotalAmount() != null) {
                    taxAmount.setTtlAmt(
                        createActiveOrHistoricCurrencyAndAmount(
                            record.getAmount().getTotalAmount()));
                  }

                  if (record.getAmount().getDetails() != null
                      && !record.getAmount().getDetails().isEmpty()) {
                    record
                        .getAmount()
                        .getDetails()
                        .forEach(
                            detail -> {
                              TaxRecordDetails2 taxDetail = new TaxRecordDetails2();

                              if (detail.getPeriod() != null) {
                                TaxPeriod2 detailPeriod = new TaxPeriod2();

                                if (detail.getPeriod().getYear() != null) {
                                  try {
                                    LocalDate year =
                                        LocalDate.parse(detail.getPeriod().getYear() + "-01-01");
                                    detailPeriod.setYr(year);
                                  } catch (Exception e) {
                                    // 年份格式不正确，忽略
                                  }
                                }

                                if (detail.getPeriod().getType() != null) {
                                  try {
                                    TaxRecordPeriod1Code tp =
                                        TaxRecordPeriod1Code.valueOf(detail.getPeriod().getType());
                                    detailPeriod.setTp(tp);
                                  } catch (IllegalArgumentException e) {
                                    // 类型不正确，忽略
                                  }
                                }

                                // 注意：由于 TaxPeriod2 类中没有 setFromDate 和 setToDate 方法，我们暂时不设置这些字段

                                taxDetail.setPrd(detailPeriod);
                              }

                              if (detail.getAmount() != null) {
                                taxDetail.setAmt(
                                    createActiveOrHistoricCurrencyAndAmount(detail.getAmount()));
                              }

                              taxAmount.addDtls(taxDetail);
                            });
                  }

                  taxRecord.setTaxAmt(taxAmount);
                }

                if (record.getAdditionalInformation() != null) {
                  taxRecord.setAddtlInf(record.getAdditionalInformation());
                }

                taxInformation.addRcrd(taxRecord);
              });
    }

    return taxInformation;
  }

  private void setSupplementaryData(
      CreditTransferTransaction39 creditTransferTransaction,
      CreditTransferTransactionInformation39 txInfo) {
    if (txInfo.getSplmtryData() != null && !txInfo.getSplmtryData().isEmpty()) {
      txInfo
          .getSplmtryData()
          .forEach(
              splmtryData -> {
                SupplementaryData1 supplementaryData = new SupplementaryData1();

                if (splmtryData.getPlcAndNm() != null) {
                  supplementaryData.setPlcAndNm(splmtryData.getPlcAndNm());
                }

                if (splmtryData.getEnvelope() != null) {
                  // 注意：由于我们不知道 SupplementaryDataEnvelope1 的具体结构，
                  // 这里我们只是创建一个空的 SupplementaryDataEnvelope1 对象
                  SupplementaryDataEnvelope1 envelope = new SupplementaryDataEnvelope1();
                  supplementaryData.setEnvlp(envelope);
                }

                creditTransferTransaction.addSplmtryData(supplementaryData);
              });
    }
  }
}
