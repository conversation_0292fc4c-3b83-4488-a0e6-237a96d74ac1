package com.bihu.swifttrack.request.message;

import java.math.BigDecimal;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import lombok.Data;

/**
 * Balance
 *
 * <p></p>
 *
 * <AUTHOR>
 * @date 2024/09/04
 */
@Data
public class Balance {

  /**
   * 余额类型 (F: 首次余额, M: 中间余额) 必填，只能是 'F' 或 'M'
   */
  @NotNull(groups = FullValidation.class)
  @Pattern(regexp = "[FM]", groups = {FullValidation.class, DraftValidation.class})
  private String balanceType;

  /**
   * 借贷标记 (D: 借, C: 贷) 必填，只能是 'D' 或 'C'
   */
  @NotNull(groups = FullValidation.class)
  @Pattern(regexp = "[DC]", groups = {FullValidation.class, DraftValidation.class})
  private String debitCreditMark;

  /**
   * 日期 (YYMMDD格式) 必填
   */
  @NotNull(groups = FullValidation.class)
  @Pattern(regexp = "\\d{6}", groups = {FullValidation.class, DraftValidation.class})
  private String date;

  /**
   * 货币代码 (ISO 4217标准) 必填，3个字符
   */
  @NotNull(groups = FullValidation.class)
  @Size(min = 3, max = 3, groups = {FullValidation.class, DraftValidation.class})
  private String currency;

  /**
   * 金额 必填，最多15位数字，小数点后最多2位
   */
  @NotNull(groups = FullValidation.class)
  @Digits(integer = 13, fraction = 2, groups = {FullValidation.class, DraftValidation.class})
  private BigDecimal amount;
}
