package com.bihu.swifttrack.request.message;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ProxyBankInfo
 *
 * <p>
 *
 * <AUTHOR>
 * @date 2024/09/25
 */
@Data
public class ProxyBankInfo {

  /*
   * 发送方代理行
   */
  private BankInfo senderProxy;

  /** 53B字段信息 */
  private Field53B field53B;

  /** 接收方代理行 */
  private BankInfo receiverProxy;

  /** 中间行 */
  private BankInfo middleProxy;

  /** 账户行信息 */
  private BankInfo bankInfo57A;

  @Data
  public static class BankInfo {

    private String account;
    private String bicCode;
  }

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class Field53B {
    /** 借贷标识 D = 借记(Debit) C = 贷记(Credit) 可选字段，仅允许单个字符 D 或 C */
    private String dcMark;

    /** 账号 可选字段，最多34个字符 可以是银行账号、IBAN号或其他账户标识符 */
    private String account;

    /** 银行所在地 可选字段，最多35个字符 通常包含银行名称和地理位置信息 例如: "BANK OF CHINA BEIJING" */
    private String location;

    /**
     * 校验Field53B格式 格式定义: [[/<DC>][/34x]$][35x] 说明： [/<DC>] - 可选的D/C标记，前缀"/" [/34x] -
     * 必选的账号信息，前缀"/"，最多34个字符 [35x] - 可选的位置信息，最多35个字符
     */
    public boolean isValid() {
      // 1. 校验dcMark：如果存在，必须是D或C
      if (dcMark != null && !dcMark.matches("^[DC]$")) {
        return false;
      }

      // 2. 校验account：必须存在且不超过34个字符
      if (account == null || account.trim().isEmpty()) {
        return false;
      }
      // 移除可能存在的前导斜杠后检查长度
      String cleanAccount = account.startsWith("/") ? account.substring(1) : account;
      if (cleanAccount.length() > 34) {
        return false;
      }

      // 3. 校验location：如果存在则不能超过35个字符
      if (location != null && !location.trim().isEmpty() && location.length() > 35) {
        return false;
      }

      return true;
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder();
      // 确保DC标记和账号都带有前导斜杠
      if (dcMark != null) {
        sb.append("/").append(dcMark);
      }
      // 账号部分确保有前导斜杠
      sb.append("/").append(account.startsWith("/") ? account.substring(1) : account);
      // 位置信息如果存在则另起一行
      if (location != null && !location.trim().isEmpty()) {
        sb.append("\n").append(location);
      }
      return sb.toString();
    }
  }
}
