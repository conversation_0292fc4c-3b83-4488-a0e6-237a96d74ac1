/**
 * 付款方信息类 包含付款方的详细信息，如银行信息、个人信息等
 *
 * <AUTHOR>
 * @date 2024/06/06
 */
package com.bihu.swifttrack.request.message;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "付款方信息")
public class PaymentInfo {

  @Schema(description = "付款方银行名称", example = "Bank of America")
  private String payerBank;

  @Schema(description = "付款方银行代码(BIC/LEI)", example = "BOFAUS3NXXX")
  private String payerBankCode;

  @Schema(description = "付款人名称", example = "John Doe")
  private String payerName;

  @Schema(description = "付款人账号", example = "**********")
  private String payerAccount;

  @Schema(description = "付款人地址", example = "123 Main St, New York, NY 10001")
  private String payerAddress;

  @Schema(description = "付款人所在国家/地区代码", example = "US")
  private String payerCountry;
}
