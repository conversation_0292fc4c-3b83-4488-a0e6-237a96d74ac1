/**
 * MT950对账单请求信息类 用于封装MT950(Money Market Fund/Call Money Market Update)消息的请求信息 包含对账单的基本信息，如账户信息、余额信息等
 *
 * <AUTHOR>
 * @date 2024/06/06
 */
package com.bihu.swifttrack.request.message;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import lombok.Data;

@Data
@Schema(description = "MT950对账单请求信息")
public class MT950RequestInfo {

  /** 交易参考号 SWIFT标签: 20 格式要求: 最大16个字符 必填字段 */
  @NotNull(groups = FullValidation.class)
  @Size(
      max = 16,
      groups = {FullValidation.class, DraftValidation.class})
  @Schema(description = "交易参考号，最大16个字符", example = "TRN2024001")
  private String transactionReferenceNumber;

  /** 账户标识 SWIFT标签: 25 格式要求: 最大35个字符 必填字段 */
  @NotNull(groups = FullValidation.class)
  @Size(
      max = 35,
      groups = {FullValidation.class, DraftValidation.class})
  @Schema(description = "账户标识，最大35个字符", example = "*********")
  private String accountIdentification;

  /** 对账单编号 SWIFT标签: 28C 格式要求: 5位数字 必填字段 */
  @NotNull(groups = FullValidation.class)
  @Pattern(
      regexp = "\\d{1,5}",
      groups = {FullValidation.class, DraftValidation.class})
  @Schema(description = "对账单编号，5位数字", example = "00001")
  private String statementNumber;

  /** 对账单页码 SWIFT标签: 28C 格式要求: 5位数字 必填字段 */
  @NotNull(groups = FullValidation.class)
  @Pattern(
      regexp = "\\d{1,5}",
      groups = {FullValidation.class, DraftValidation.class})
  @Schema(description = "对账单页码，5位数字", example = "00001")
  private String statementPageNumber;

  /** 期初余额 SWIFT标签: 60a (60F或60M) 必填字段 */
  @NotNull(groups = FullValidation.class)
  @Schema(description = "期初余额信息")
  private Balance openingBalance;

  /** 对账单明细列表 SWIFT标签: 61 可选字段 */
  @Schema(description = "对账单明细列表")
  private List<StatementLine> statementLines;

  /** 期末余额（账面资金） SWIFT标签: 62a (62F或62M) 必填字段 */
  @NotNull(groups = FullValidation.class)
  @Schema(description = "期末余额（账面资金）信息")
  private Balance closingBalance;

  /** 可用余额（可用资金） SWIFT标签: 64 可选字段 */
  @Schema(description = "可用余额（可用资金）信息")
  private Balance availableBalance;
}
