package com.bihu.swifttrack.request.message;

import com.bihu.swifttrack.mybatis.repository.po.MessageDetailsPO;
import java.util.List;

public interface MessageContentFiller {

  /**
   * Fills the content of the message details list based on the given lists and message request.
   *
   * @param lists          the list of message details to be filled
   * @param messageRequest the message request containing the information to fill the message
   *                       details
   * @throws Exception if an error occurs while filling the content
   */
  void fillContent(List<MessageDetailsPO> lists, MessageRequest messageRequest) throws Exception;
}
