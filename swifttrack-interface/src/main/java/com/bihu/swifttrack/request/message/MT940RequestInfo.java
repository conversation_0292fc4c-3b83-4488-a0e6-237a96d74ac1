package com.bihu.swifttrack.request.message;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import lombok.Data;

/**
 * MT940客户对账单请求信息类 用于封装MT940(Customer Statement Message)消息的请求信息 包含客户对账单的详细信息，如账户信息、余额信息、交易明细等
 *
 * <AUTHOR>
 * @date 2024/09/04
 */
@Data
@Schema(description = "MT940客户对账单请求信息")
public class MT940RequestInfo {

  /** 交易参考号 SWIFT标签: 20 格式要求: 最大16个字符 必填字段 */
  @NotNull(groups = FullValidation.class)
  @Size(
      max = 16,
      groups = {FullValidation.class, DraftValidation.class})
  @Schema(description = "交易参考号，最大16个字符", example = "TRN2024001")
  private String transactionReferenceNumber;

  /** 相关参考号 SWIFT标签: 21 格式要求: 最大16个字符 可选字段，MT940特有 */
  @Size(
      max = 16,
      groups = {FullValidation.class, DraftValidation.class})
  @Schema(description = "相关参考号，最大16个字符", example = "REF2024001")
  private String relatedReferenceNumber;

  /** 账户标识 SWIFT标签: 25 格式要求: 最大35个字符 必填字段 */
  @NotNull(groups = FullValidation.class)
  @Size(
      max = 35,
      groups = {FullValidation.class, DraftValidation.class})
  @Schema(description = "账户标识，最大35个字符", example = "*********")
  private String accountIdentification;

  /** 对账单编号 SWIFT标签: 28C 格式要求: 5位数字 必填字段 */
  @NotNull(groups = FullValidation.class)
  @Pattern(
      regexp = "\\d{1,5}",
      groups = {FullValidation.class, DraftValidation.class})
  @Schema(description = "对账单编号，5位数字", example = "00001")
  private String statementNumber;

  /** 对账单页码 SWIFT标签: 28C 格式要求: 5位数字 必填字段 */
  @NotNull(groups = FullValidation.class)
  @Pattern(
      regexp = "\\d{1,5}",
      groups = {FullValidation.class, DraftValidation.class})
  @Schema(description = "对账单页码，5位数字", example = "00001")
  private String statementPageNumber;

  /** 期初余额 SWIFT标签: 60a (60F或60M) 必填字段 */
  @NotNull(groups = FullValidation.class)
  @Schema(description = "期初余额信息")
  private Balance openingBalance;

  /** 对账单明细列表 SWIFT标签: 61 可选字段 */
  @Schema(description = "对账单明细列表")
  private List<StatementLine> statementLines;

  /** 账户所有者信息 SWIFT标签: 86 可选字段，MT940特有 */
  @Schema(description = "账户所有者信息", example = "Additional account information")
  private String informationToAccountOwner;

  /** 期末余额（账面资金） SWIFT标签: 62a (62F或62M) 必填字段 */
  @NotNull(groups = FullValidation.class)
  @Schema(description = "期末余额（账面资金）信息")
  private Balance closingBalance;

  /** 可用余额（可用资金） SWIFT标签: 64 可选字段 */
  @Schema(description = "可用余额（可用资金）信息")
  private Balance availableBalance;

  /** 远期可用余额列表 SWIFT标签: 65 可选字段，MT940特有 */
  @Schema(description = "远期可用余额列表")
  private List<Balance> forwardAvailableBalance;
}
