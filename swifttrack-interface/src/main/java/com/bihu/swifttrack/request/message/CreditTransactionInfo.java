/**
 * 贷记交易信息类 包含贷记交易的详细信息，如代理行信息、账号信息、参考号等
 *
 * <AUTHOR>
 * @date 2024/06/06
 */
package com.bihu.swifttrack.request.message;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "贷记交易信息")
public class CreditTransactionInfo {

  @Schema(description = "付款方银行代理行BIC代码", example = "BOFAUS3NXXX")
  private String payerAgentCode;

  @Schema(description = "收款方银行代理行BIC代码", example = "CHASUS33XXX")
  private String payeeAgentCode;

  @Schema(description = "贷记卡号/账号", example = "****************")
  private String creditAccount;

  @Schema(description = "交易参考号，用于唯一标识一笔交易", example = "TRN2024001")
  private String transactionReferenceNumber;

  @Schema(description = "相关参考号，用于关联其他交易或消息", example = "REF2024001")
  private String relatedReferenceNumber;

  @Schema(description = "费用详情，包含手续费等费用信息", example = "Processing fee: USD 25.00")
  private String expenseDetail;
}
