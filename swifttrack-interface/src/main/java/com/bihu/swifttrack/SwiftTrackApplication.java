package com.bihu.swifttrack;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Contact;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.info.License;
import io.swagger.v3.oas.annotations.servers.Server;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * <AUTHOR>
 * @date 2024/5/28
 */
@SpringBootApplication
@OpenAPIDefinition(
    info =
        @Info(
            title = "SwiftTrack API Documentation",
            version = "1.0.0",
            description =
                "API documentation for SWIFT message processing, including pacs.008 and pacs.009",
            contact = @Contact(name = "SwiftTrack Team", email = "<EMAIL>"),
            license = @License(name = "Private License")),
    servers = {@Server(url = "/api", description = "Default Server URL")})
public class SwiftTrackApplication {

  public static void main(String[] args) {
    SpringApplication.run(SwiftTrackApplication.class, args);
  }
}
