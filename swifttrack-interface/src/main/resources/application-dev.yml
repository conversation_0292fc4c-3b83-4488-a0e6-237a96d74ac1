spring:
  redis:
    database: 4
    host: r-6webimaqqplgv65ogqpd.redis.japan.rds.aliyuncs.com
    port: 6379
    password: Beog91dotNB
    timeout: 15000
    lettuce:
      pool:
        max-active: 100
        max-idle: 10
        min-idle: 1
        max-wait: 10000
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    username: swifttrack_qa
    password: i9hBJoJm4pXWcG0ECVFW
    url: **************************************************************************************************************************************************************************************************************************************************************
    hikari:
      # 连接池最大连接数，默认是10
      maximumPoolSize: 20
      # 最小空闲连接数量
      minimumIdle: 2
      # 空闲连接存活最大时间，默认600000（10分钟）
      idleTimeout: 300000
      # 数据库连接超时时间,默认30秒，即30000
      connectionTimeout: 6000
      connectionTestQuery: SELECT 1
      ## 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
      maxLifetime: 600000
  # Spring Security 配置
  security:
    ignored: /swagger-ui/**, /api-docs/**, /v3/api-docs/**, /swagger-resources/**, /webjars/**


jwt:
  secret: tCVjGIoWFU2YmYVc6Gg42I73WLTBL+8ikWm3Z/j8k5c=

oss:
  enabled: true
  access-key-id: LTAI4FzsbzzUz1HwjX8nXa43
  access-key-secret: ******************************
  bucket-name: swifttrack-qa
  endpoint: oss-cn-hangzhou.aliyuncs.com
  expired: 3600

reset:
  url: https://messaging-qa.piaodian.net/reset-pwd?token=

rest:
  connectTimeout: 20000 # http连接超时时间，默认500ms
  readTimeout: 30000 # http读取超时时间，默认1000ms
  maxConnTotal: 200 # 表示连接池最大并发连接数,默认300
  maxConnPerRoute: 100 # 表示单路由的最大并发连接数,默认150
  validateAfterInactivity: 1000 # 空闲的永久连接检查间隔，默认2000ms

gfin:
  api:
    base-url: http://192.168.0.2:8090/

sms:
  aliyun:
    enabled: false
    accessKeyId: LTAI5tLRRDMzHWUeUCc4Vy47
    accessKeySecret: ******************************
    sign: 由我付YouWorld
    endpoint: dysmsapi.aliyuncs.com
    templateCodeMap:
      register: SMS_173140682
      login: SMS_173140684
      verify: SMS_276401215
      updateUserMobileVerify: SMS_276401215
      updateNewMobileVerify: SMS_276401215
      UpdatePassword: SMS_276401215
  baiwu:
    enabled: true
    serverUrl: https://plate.hbsmservice.com:8443/sms/v2/send-same
    account: bh1221
    password: 243nna
    sign: WildCard
    ext: 01
  twilio:
    enabled: false
    sign: WildCard
    accountSid: **********************************
    authToken: 9c4888f1b73d2650bfc79c0b17277499
    formNumber: +***********

  templateMap:
    __CompanyRegister_phone__: "验证码 %s ，您正在注册，若非本人操作，请勿泄露。"

# 允许 Swagger UI 访问
springdoc:
  swagger-ui:
    enabled: true