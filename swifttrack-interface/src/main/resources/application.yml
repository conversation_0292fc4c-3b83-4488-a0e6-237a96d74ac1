server:
  port: 8083
  servlet:
    context-path: /api
spring:
  application:
    name: swifttrack
  profiles:
    active: dev
    include: mybatis
  config:
    import:
      - classpath:config/message-routing.yml
      - classpath:config/cache-config.yml
  sleuth:
    sampler:
      probability: 1.0  # 设置采样率为100%
  cloud:
    kubernetes:
      reload:
        enabled: true
      config:
        namespace: default
        name: ${spring.application.name}-${spring.profiles.active}
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true

# SpringDoc 配置
springdoc:
  api-docs:
    path: /api-docs
    enabled: true
    groups:
      enabled: true
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: method
    tagsSorter: alpha
    disable-swagger-default-url: true
    display-request-duration: true
    filter: true
  packages-to-scan: 
    - com.bihu.swifttrack.controller
  paths-to-match: 
    - /**
  default-produces-media-type: application/json
  default-consumes-media-type: application/json
  model-and-view-allowed: true
  auto-tag-classes: true
  show-actuator: false