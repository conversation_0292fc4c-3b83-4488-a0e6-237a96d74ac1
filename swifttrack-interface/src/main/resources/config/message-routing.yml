message-routing:
  endpoints:
    - id: "gfin-remote"
      type: "GFIN_REMOTE"
      name: "GFIN远程端点"
      enabled: true
      properties:
        url: "https://gfin.example.com"
        timeout: 30000
    - id: "local-database"
      type: "LOCAL_DATABASE"
      name: "本地数据库端点"
      enabled: true
      properties:
        table: "outbound_messages"
  
  routing-rules:
    - bic-patterns:
        - "BTCBCNBJ*"
        - "BTCBCNBJRLY"
        - "BTCBCNBJRLB"
        - "BTCBCNBJRLC"
        - "BTCBCNBJRLD"
        - "BTCBCNBJRLE"
        - "BTCBCNBJRLF"
        - "BTCBCNBJRLG"
        - "BTCBCNBJRLH"
        - "BTCBCNBJRLI"
        - "BTCBCNBJRLJ"
        - "BTCBCNBJRLK"
        - "BTCBCNBJRLL"
        - "BTCBCNBJRLM"
      endpoint-id: "gfin-remote"
      priority: 1
      enabled: true
    - bic-patterns:
        - "TESTBANK*"
        - "DEVBANK*"
      endpoint-id: "local-database"
      priority: 2
      enabled: true
    - bic-patterns:
        - "*"  # 默认规则
      endpoint-id: "gfin-remote"
      priority: 999
      enabled: true 