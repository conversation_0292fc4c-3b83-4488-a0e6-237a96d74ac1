package com.bihu.swifttrack.request.message.fillers.mx;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.bihu.swifttrack.dto.Amount;
import com.bihu.swifttrack.mybatis.repository.po.MessageDetailsPO;
import com.bihu.swifttrack.request.message.MessageRequest;
import com.bihu.swifttrack.request.message.mx.Account;
import com.bihu.swifttrack.request.message.mx.Agent;
import com.bihu.swifttrack.request.message.mx.BusinessApplicationHeader;
import com.bihu.swifttrack.request.message.mx.CreditTransferTransaction37;
import com.bihu.swifttrack.request.message.mx.GroupHeader;
import com.bihu.swifttrack.request.message.mx.InstructionForAgentInfo;
import com.bihu.swifttrack.request.message.mx.InstructionForCreditorAgent;
import com.bihu.swifttrack.request.message.mx.MXPacs009RequestInfo;
import com.bihu.swifttrack.request.message.mx.MXPacs009RequestInfo.CreditTransferTransactionInformation36;
import com.bihu.swifttrack.request.message.mx.Party;
import com.bihu.swifttrack.request.message.mx.PaymentIdentification;
import com.bihu.swifttrack.request.message.mx.PaymentTypeInformation;
import com.bihu.swifttrack.request.message.mx.PreviousInstrictingInfo;
import com.bihu.swifttrack.request.message.mx.SettlementInformation;
import com.bihu.swifttrack.request.message.mx.SettlementTimeIndication;
import com.bihu.swifttrack.request.message.mx.SupplementaryData;
import com.bihu.swifttrack.request.message.mx.TypedValue;
import com.prowidesoftware.swift.model.mx.BusinessAppHdrV02;
import com.prowidesoftware.swift.model.mx.MxPacs00900108;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class MXPacs009ContentFillerTest {

  private MXPacs009ContentFiller filler;
  private MessageRequest messageRequest;
  private List<MessageDetailsPO> messageDetailsList;

  @BeforeEach
  void setUp() {
    filler = new MXPacs009ContentFiller();
    messageRequest = mock(MessageRequest.class);
    messageDetailsList = new ArrayList<>();
  }

  @Test
  void fillContent_withValidRequestInfo_shouldFillMessageDetails() throws Exception {
    // Arrange
    MXPacs009RequestInfo requestInfo = createValidRequestInfo();
    when(messageRequest.getMxPacs009RequestInfo()).thenReturn(requestInfo);

    // Act
    filler.fillContent(messageDetailsList, messageRequest);

    // Assert
    assertFalse(messageDetailsList.isEmpty());
    MessageDetailsPO messageDetails = messageDetailsList.get(0);
    assertNotNull(messageDetails);

    assertEquals("mxPacs009", messageDetails.getFieldName());
    assertNotNull(messageDetails.getFieldValue());
    System.out.println(messageDetails.getFieldValue());

    MxPacs00900108 mxPacs009 = MxPacs00900108.parse(messageDetails.getFieldValue());
    assertNotNull(mxPacs009);

    verifyBusinessApplicationHeader(mxPacs009);
    verifyGroupHeader(mxPacs009);
    verifyCreditTransferTransactionInformation(mxPacs009);
  }

  @Test
  void fillContent_withNullRequestInfo_shouldThrowException() {
    // Arrange
    when(messageRequest.getMxPacs009RequestInfo()).thenReturn(null);

    // Act & Assert
    assertThrows(
        IllegalArgumentException.class,
        () -> filler.fillContent(messageDetailsList, messageRequest));
  }

  @Test
  void fillContent_withAdditionalFields_shouldFillMessageDetails() throws Exception {
    // Arrange
    MXPacs009RequestInfo requestInfo = createRequestInfoWithAdditionalFields();
    when(messageRequest.getMxPacs009RequestInfo()).thenReturn(requestInfo);

    // Act
    filler.fillContent(messageDetailsList, messageRequest);

    // Assert
    assertFalse(messageDetailsList.isEmpty());
    MessageDetailsPO messageDetails = messageDetailsList.get(0);
    assertNotNull(messageDetails);

    assertEquals("mxPacs009", messageDetails.getFieldName());
    assertNotNull(messageDetails.getFieldValue());
    System.out.println(messageDetails.getFieldValue());

    MxPacs00900108 mxPacs009 = MxPacs00900108.parse(messageDetails.getFieldValue());
    assertNotNull(mxPacs009);

    verifyBusinessApplicationHeader(mxPacs009);
    verifyGroupHeader(mxPacs009);
    verifyAdditionalFields(mxPacs009);
  }

  @Test
  void fillContent_withSupplementaryData_shouldFillMessageDetails() throws Exception {
    // Arrange
    MXPacs009RequestInfo requestInfo = createRequestInfoWithSupplementaryData();
    when(messageRequest.getMxPacs009RequestInfo()).thenReturn(requestInfo);

    // Act
    filler.fillContent(messageDetailsList, messageRequest);

    // Assert
    assertFalse(messageDetailsList.isEmpty());
    MessageDetailsPO messageDetails = messageDetailsList.get(0);
    assertNotNull(messageDetails);

    assertEquals("mxPacs009", messageDetails.getFieldName());
    assertNotNull(messageDetails.getFieldValue());
    System.out.println(messageDetails.getFieldValue());

    MxPacs00900108 mxPacs009 = MxPacs00900108.parse(messageDetails.getFieldValue());
    assertNotNull(mxPacs009);

    verifyBusinessApplicationHeader(mxPacs009);
    verifyGroupHeader(mxPacs009);
    verifySupplementaryData(mxPacs009);
  }

  @Test
  void fillContent_withUnderlyingCustomerCreditTransfer_shouldFillMessageDetails()
      throws Exception {
    // Arrange
    MXPacs009RequestInfo requestInfo = createRequestInfoWithUnderlyingCustomerCreditTransfer();
    when(messageRequest.getMxPacs009RequestInfo()).thenReturn(requestInfo);

    // Act
    filler.fillContent(messageDetailsList, messageRequest);

    // Assert
    assertFalse(messageDetailsList.isEmpty());
    MessageDetailsPO messageDetails = messageDetailsList.get(0);
    assertNotNull(messageDetails);

    assertEquals("mxPacs009", messageDetails.getFieldName());
    assertNotNull(messageDetails.getFieldValue());
    System.out.println(messageDetails.getFieldValue());

    MxPacs00900108 mxPacs009 = MxPacs00900108.parse(messageDetails.getFieldValue());
    assertNotNull(mxPacs009);

    verifyBusinessApplicationHeader(mxPacs009);
    verifyGroupHeader(mxPacs009);
    verifyUnderlyingCustomerCreditTransfer(mxPacs009);
  }

  private MXPacs009RequestInfo createValidRequestInfo() {
    MXPacs009RequestInfo requestInfo = new MXPacs009RequestInfo();
    requestInfo.setBusinessApplicationHeader(createBusinessApplicationHeader());
    requestInfo.setGroupHeader(createGroupHeader());
    requestInfo.setCreditTransferTransactionInformation(
        createCreditTransferTransactionInformation());
    return requestInfo;
  }

  private BusinessApplicationHeader createBusinessApplicationHeader() {
    BusinessApplicationHeader bah = new BusinessApplicationHeader();
    bah.setFrom("BOFAUS3NRLY");
    bah.setTo("CHASUS33RLY");
    bah.setBizMsgIdr("BIZ1728441119856");
    bah.setBizSvc("swift.cbprplus.02");
    bah.setCreDt(LocalDateTime.of(2024, 10, 9, 2, 26));
    bah.setMsgDefIdr("pacs.009.001.08");
    return bah;
  }

  private GroupHeader createGroupHeader() {
    GroupHeader groupHeader = new GroupHeader();
    groupHeader.setMsgId("MSG1728441119856");
    groupHeader.setCreDtTm(LocalDateTime.of(2024, 10, 9, 2, 26));
    groupHeader.setNbOfTxs("1");

    SettlementInformation sttlmInf = new SettlementInformation();
    sttlmInf.setSttlmMtd("CLRG");
    groupHeader.setSttlmInf(sttlmInf);

    return groupHeader;
  }

  private CreditTransferTransactionInformation36
      createCreditTransferTransactionInformation() {
    CreditTransferTransactionInformation36 txInfo =
        new CreditTransferTransactionInformation36();
    txInfo.setPmtId(createPaymentIdentification());
    txInfo.setPmtTpInf(createPaymentTypeInformation());

    Amount amount = new Amount();
    amount.setAmount(BigDecimal.valueOf(1000000.00));
    amount.setCurrency("USD");
    txInfo.setIntrBkSttlmAmt(amount);

    txInfo.setIntrBkSttlmDt(LocalDate.of(2024, 10, 9));
    txInfo.setDebtor(createAgent("BOFAUS3NRLY"));
    txInfo.setCreditor(createAgent("BOFAUS3NRLY"));
    txInfo.setDbtrAgt(createAgent("BOFAUS3NRLY"));
    txInfo.setCdtrAgt(createAgent("CHASUS33RLY"));
    txInfo.setInstgAgt(createAgent("DEUTDEFFRLY"));
    txInfo.setInstdAgt(createAgent("BKTRUS33RLY"));

    List<PreviousInstrictingInfo> previousInstrictingInfos = new ArrayList<>();
    PreviousInstrictingInfo info = new PreviousInstrictingInfo();
    info.setPrvsInstgAgt(createAgent("SOGEFRPPXXX"));
    Account account = new Account();
    account.setIban("***************************");
    info.setPrvsInstgAgtAcct(account);
    previousInstrictingInfos.add(info);
    txInfo.setPreviousInstrictingInfos(previousInstrictingInfos);

    List<InstructionForAgentInfo> instrForCdtrAgt = new ArrayList<>();
    InstructionForAgentInfo cdtrAgtInfo = new InstructionForAgentInfo();
    cdtrAgtInfo.setCd("PHOB");
    cdtrAgtInfo.setInstrInf("Please handle with care");
    instrForCdtrAgt.add(cdtrAgtInfo);
    txInfo.setInstrForCdtrAgt(instrForCdtrAgt);

    List<InstructionForAgentInfo> instrForNxtAgt = new ArrayList<>();
    InstructionForAgentInfo nxtAgtInfo = new InstructionForAgentInfo();
    nxtAgtInfo.setCd("PHOA");
    nxtAgtInfo.setInstrInf("Contact required");
    instrForNxtAgt.add(nxtAgtInfo);
    txInfo.setInstrForNxtAgt(instrForNxtAgt);

    TypedValue purp = new TypedValue();
    purp.setType(TypedValue.ValueType.CODE);
    purp.setValue("CASH");
    txInfo.setPurp(purp);

    txInfo.setRmtInf(Arrays.asList("Payment for invoice #12345", "Order ID: 67890"));

    // 设置结算时间指示
    SettlementTimeIndication timeIndication = new SettlementTimeIndication();
    timeIndication.setDbtDtTm(LocalDate.of(2024, 10, 9).atStartOfDay().atOffset(ZoneOffset.UTC));
    timeIndication.setCdtDtTm(LocalDate.of(2024, 10, 10).atStartOfDay().atOffset(ZoneOffset.UTC));
    txInfo.setSettlementTimeIndication(timeIndication);

    // Add SettlementTimeRequest
    CreditTransferTransactionInformation36.SettlementTimeRequest timeRequest =
        new CreditTransferTransactionInformation36.SettlementTimeRequest();
    timeRequest.setClsTime(LocalTime.of(8, 0));
    timeRequest.setTillTime(LocalTime.of(17, 0));
    timeRequest.setFromTime(LocalTime.of(9, 0));
    timeRequest.setRejectTime(LocalTime.of(18, 0));
    txInfo.setSettlementTimeRequest(timeRequest);

    return txInfo;
  }

  private PaymentIdentification createPaymentIdentification() {
    PaymentIdentification pmtId = new PaymentIdentification();
    pmtId.setInstrId("INSTR1728441119856");
    pmtId.setEndToEndId("E2E1728441126952");
    pmtId.setTxId("TID1728441126952");
    pmtId.setUetr("97ed4827-7b6f-4491-a06f-b548d5a7512d");
    return pmtId;
  }

  private PaymentTypeInformation createPaymentTypeInformation() {
    PaymentTypeInformation pmtTpInf = new PaymentTypeInformation();
    pmtTpInf.setInstrPrty("HIGH");
    pmtTpInf.setClrChanl("RTGS");

    // Add service level
    List<TypedValue> svcLvls = new ArrayList<>();
    TypedValue svcLvl = new TypedValue();
    svcLvl.setType(TypedValue.ValueType.CODE);
    svcLvl.setValue("URGP");
    svcLvls.add(svcLvl);
    pmtTpInf.setSvcLvl(svcLvls);

    // Add local instrument
    TypedValue lclInstrm = new TypedValue();
    lclInstrm.setType(TypedValue.ValueType.CODE);
    lclInstrm.setValue("INST");
    pmtTpInf.setLclInstrm(lclInstrm);

    // Add category purpose
    TypedValue ctgyPurp = new TypedValue();
    ctgyPurp.setType(TypedValue.ValueType.CODE);
    ctgyPurp.setValue("CASH");
    pmtTpInf.setCtgyPurp(ctgyPurp);

    return pmtTpInf;
  }

  private void verifyPaymentTypeInformation(MxPacs00900108 mxPacs009) {
    var pmtTpInf = mxPacs009.getFICdtTrf().getCdtTrfTxInf().get(0).getPmtTpInf();

    // Verify mandatory fields
    assertNotNull(pmtTpInf);
    assertEquals("HIGH", pmtTpInf.getInstrPrty().value());
    assertEquals("RTGS", pmtTpInf.getClrChanl().value());

    // Verify service level
    assertNotNull(pmtTpInf.getSvcLvl());
    assertFalse(pmtTpInf.getSvcLvl().isEmpty());
    assertEquals("URGP", pmtTpInf.getSvcLvl().get(0).getCd());

    // Verify local instrument
    assertNotNull(pmtTpInf.getLclInstrm());
    assertEquals("INST", pmtTpInf.getLclInstrm().getCd());

    // Verify category purpose
    assertNotNull(pmtTpInf.getCtgyPurp());
    assertEquals("CASH", pmtTpInf.getCtgyPurp().getCd());
  }

  private Agent createAgent(String bicfi) {
    Agent agent = new Agent();
    agent.setBicfi(bicfi);
    return agent;
  }

  private Party createParty(String name, String accountId) {
    Party party = new Party();
    party.setNm(name);
    party.setAcctId(accountId);
    return party;
  }

  private void verifyBusinessApplicationHeader(MxPacs00900108 mxPacs009) {
    BusinessAppHdrV02 appHdr = (BusinessAppHdrV02) mxPacs009.getAppHdr();
    assertNotNull(appHdr);
    assertEquals("BOFAUS3NRLY", appHdr.getFr().getFIId().getFinInstnId().getBICFI());
    assertEquals("CHASUS33RLY", appHdr.getTo().getFIId().getFinInstnId().getBICFI());
    assertEquals("BIZ1728441119856", appHdr.getBizMsgIdr());
    assertEquals("pacs.009.001.08", appHdr.getMsgDefIdr());
    assertEquals("swift.cbprplus.02", appHdr.getBizSvc());
  }

  private void verifyGroupHeader(MxPacs00900108 mxPacs009) {
    assertEquals("MSG1728441119856", mxPacs009.getFICdtTrf().getGrpHdr().getMsgId());
    assertEquals("1", mxPacs009.getFICdtTrf().getGrpHdr().getNbOfTxs());
    assertEquals("CLRG", mxPacs009.getFICdtTrf().getGrpHdr().getSttlmInf().getSttlmMtd().value());
  }

  private void verifyCreditTransferTransactionInformation(MxPacs00900108 mxPacs009) {
    var cdtTrfTxInf = mxPacs009.getFICdtTrf().getCdtTrfTxInf().get(0);

    // Verify PaymentIdentification
    assertEquals("INSTR1728441119856", cdtTrfTxInf.getPmtId().getInstrId());
    assertEquals("E2E1728441126952", cdtTrfTxInf.getPmtId().getEndToEndId());
    assertEquals("TID1728441126952", cdtTrfTxInf.getPmtId().getTxId());
    assertEquals("97ed4827-7b6f-4491-a06f-b548d5a7512d", cdtTrfTxInf.getPmtId().getUETR());

    // Verify PaymentTypeInformation
    verifyPaymentTypeInformation(mxPacs009);

    // Verify other fields
    assertEquals("USD", cdtTrfTxInf.getIntrBkSttlmAmt().getCcy());
    assertEquals(1000000.00, cdtTrfTxInf.getIntrBkSttlmAmt().getValue().doubleValue());
    assertEquals(LocalDate.of(2024, 10, 9), cdtTrfTxInf.getIntrBkSttlmDt());
    assertEquals("BOFAUS3NRLY", cdtTrfTxInf.getDbtrAgt().getFinInstnId().getBICFI());
    assertEquals("CHASUS33RLY", cdtTrfTxInf.getCdtrAgt().getFinInstnId().getBICFI());
    assertEquals("DEUTDEFFRLY", cdtTrfTxInf.getInstgAgt().getFinInstnId().getBICFI());
    assertEquals("BKTRUS33RLY", cdtTrfTxInf.getInstdAgt().getFinInstnId().getBICFI());

    // Verify Purpose Code
    assertEquals("CASH", cdtTrfTxInf.getPurp().getCd());

    // Verify Remittance Information
    assertNotNull(cdtTrfTxInf.getRmtInf());
    assertEquals(2, cdtTrfTxInf.getRmtInf().getUstrd().size());
    assertEquals("Payment for invoice #12345", cdtTrfTxInf.getRmtInf().getUstrd().get(0));
    assertEquals("Order ID: 67890", cdtTrfTxInf.getRmtInf().getUstrd().get(1));

    // Verify Debtor Agent
    assertNotNull(cdtTrfTxInf.getDbtrAgt());
    assertEquals("BOFAUS3NRLY", cdtTrfTxInf.getDbtrAgt().getFinInstnId().getBICFI());

    // Verify Creditor Agent
    assertNotNull(cdtTrfTxInf.getCdtrAgt());
    assertEquals("CHASUS33RLY", cdtTrfTxInf.getCdtrAgt().getFinInstnId().getBICFI());

    // Verify Instructing Agent
    assertNotNull(cdtTrfTxInf.getInstgAgt());
    assertEquals("DEUTDEFFRLY", cdtTrfTxInf.getInstgAgt().getFinInstnId().getBICFI());

    // Verify Instructed Agent
    assertNotNull(cdtTrfTxInf.getInstdAgt());
    assertEquals("BKTRUS33RLY", cdtTrfTxInf.getInstdAgt().getFinInstnId().getBICFI());

    // Verify Previous Instructing Agent
    assertNotNull(cdtTrfTxInf.getPrvsInstgAgt1());
    assertEquals("SOGEFRPPXXX", cdtTrfTxInf.getPrvsInstgAgt1().getFinInstnId().getBICFI());

    // Verify Previous Instructing Agent Account
    assertNotNull(cdtTrfTxInf.getPrvsInstgAgt1Acct());
    assertEquals(
        "***************************", cdtTrfTxInf.getPrvsInstgAgt1Acct().getId().getIBAN());

    // Verify SettlementTimeIndication
    assertNotNull(cdtTrfTxInf.getSttlmTmIndctn());
    assertEquals(
        LocalDate.of(2024, 10, 9), cdtTrfTxInf.getSttlmTmIndctn().getDbtDtTm().toLocalDate());
    assertEquals(
        LocalDate.of(2024, 10, 10), cdtTrfTxInf.getSttlmTmIndctn().getCdtDtTm().toLocalDate());

    // Verify SettlementTimeRequest
    assertNotNull(cdtTrfTxInf.getSttlmTmReq());
    assertEquals("08:00Z", cdtTrfTxInf.getSttlmTmReq().getCLSTm().toString());
    assertEquals("17:00Z", cdtTrfTxInf.getSttlmTmReq().getTillTm().toString());
    assertEquals("09:00Z", cdtTrfTxInf.getSttlmTmReq().getFrTm().toString());
    assertEquals("18:00Z", cdtTrfTxInf.getSttlmTmReq().getRjctTm().toString());

    // Add verification for instructions
    assertNotNull(cdtTrfTxInf.getInstrForCdtrAgt());
    assertEquals("PHOB", cdtTrfTxInf.getInstrForCdtrAgt().get(0).getCd().name());
    assertEquals("Please handle with care", cdtTrfTxInf.getInstrForCdtrAgt().get(0).getInstrInf());

    assertNotNull(cdtTrfTxInf.getInstrForNxtAgt());
    assertEquals("PHOA", cdtTrfTxInf.getInstrForNxtAgt().get(0).getCd().name());
    assertEquals("Contact required", cdtTrfTxInf.getInstrForNxtAgt().get(0).getInstrInf());
  }

  private MXPacs009RequestInfo createRequestInfoWithAdditionalFields() {
    MXPacs009RequestInfo requestInfo = createValidRequestInfo();
    CreditTransferTransactionInformation36 txInfo =
        requestInfo.getCreditTransferTransactionInformation();

    // 设置最终债务人
    txInfo.setUltmtDbtr(createAgent("ULTMDBTRXXX"));

    // 设置最终债权人
    txInfo.setUltmtCdtr(createAgent("ULTMCDTRXXX"));

    // 设置债务人代理账户
    Account dbtrAgtAcct = new Account();
    dbtrAgtAcct.setIban("**********************");
    txInfo.setDbtrAgtAcct(dbtrAgtAcct);

    // 设置债权人代理账户
    Account cdtrAgtAcct = new Account();
    cdtrAgtAcct.setIban("**********************");
    txInfo.setCdtrAgtAcct(cdtrAgtAcct);

    return requestInfo;
  }

  private void verifyAdditionalFields(MxPacs00900108 mxPacs009) {
    var cdtTrfTxInf = mxPacs009.getFICdtTrf().getCdtTrfTxInf().get(0);

    // 验证最终债务人
    assertNotNull(cdtTrfTxInf.getUltmtDbtr());
    assertEquals("ULTMDBTRXXX", cdtTrfTxInf.getUltmtDbtr().getFinInstnId().getBICFI());

    // 验证最终债权人
    assertNotNull(cdtTrfTxInf.getUltmtCdtr());
    assertEquals("ULTMCDTRXXX", cdtTrfTxInf.getUltmtCdtr().getFinInstnId().getBICFI());

    // 验证债务人代理账户
    assertNotNull(cdtTrfTxInf.getDbtrAgtAcct());
    assertEquals("**********************", cdtTrfTxInf.getDbtrAgtAcct().getId().getIBAN());

    // 验证债权人代理账户
    assertNotNull(cdtTrfTxInf.getCdtrAgtAcct());
    assertEquals("**********************", cdtTrfTxInf.getCdtrAgtAcct().getId().getIBAN());
  }

  private MXPacs009RequestInfo createRequestInfoWithSupplementaryData() {
    MXPacs009RequestInfo requestInfo = createValidRequestInfo();
    CreditTransferTransactionInformation36 txInfo =
        requestInfo.getCreditTransferTransactionInformation();

    // 设置交易级别的补充数据
    List<SupplementaryData> txSplmtryDataList = new ArrayList<>();
    SupplementaryData txSplmtryData1 = new SupplementaryData();
    txSplmtryData1.setPlcAndNm("Transaction/AdditionalInfo");
    txSplmtryDataList.add(txSplmtryData1);

    SupplementaryData txSplmtryData2 = new SupplementaryData();
    txSplmtryData2.setPlcAndNm("Transaction/RegulatoryInfo");
    txSplmtryDataList.add(txSplmtryData2);

    txInfo.setSplmtryData(txSplmtryDataList);

    // 设置消息级别的补充数据
    List<SupplementaryData> msgSplmtryDataList = new ArrayList<>();
    SupplementaryData msgSplmtryData = new SupplementaryData();
    msgSplmtryData.setPlcAndNm("Message/AdditionalInfo");
    msgSplmtryDataList.add(msgSplmtryData);

    requestInfo.setSplmtryData(msgSplmtryDataList);

    return requestInfo;
  }

  private void verifySupplementaryData(MxPacs00900108 mxPacs009) {
    // 验证交易级别的补充数据
    var cdtTrfTxInf = mxPacs009.getFICdtTrf().getCdtTrfTxInf().get(0);
    assertNotNull(cdtTrfTxInf.getSplmtryData());
    assertEquals(2, cdtTrfTxInf.getSplmtryData().size());
    assertEquals("Transaction/AdditionalInfo", cdtTrfTxInf.getSplmtryData().get(0).getPlcAndNm());
    assertEquals("Transaction/RegulatoryInfo", cdtTrfTxInf.getSplmtryData().get(1).getPlcAndNm());

    // 验证消息级别的补充数据
    assertNotNull(mxPacs009.getFICdtTrf().getSplmtryData());
    assertEquals(1, mxPacs009.getFICdtTrf().getSplmtryData().size());
    assertEquals(
        "Message/AdditionalInfo", mxPacs009.getFICdtTrf().getSplmtryData().get(0).getPlcAndNm());
  }

  private MXPacs009RequestInfo createRequestInfoWithUnderlyingCustomerCreditTransfer() {
    MXPacs009RequestInfo requestInfo = createValidRequestInfo();
    CreditTransferTransactionInformation36 txInfo =
        requestInfo.getCreditTransferTransactionInformation();

    // 创建底层客户信用转账信息
    CreditTransferTransaction37 undrlygCstmrCdtTrf = new CreditTransferTransaction37();

    // 设置最终债务人
    Party ultmtDbtr = new Party();
    ultmtDbtr.setNm("Ultimate Debtor");
    ultmtDbtr.setAcctId("**********************");
    undrlygCstmrCdtTrf.setUltmtDbtr(ultmtDbtr);

    // 设置发起方
    Party initgPty = new Party();
    initgPty.setNm("Initiating Party");
    undrlygCstmrCdtTrf.setInitgPty(initgPty);

    // 设置债务人
    Party dbtr = new Party();
    dbtr.setNm("Debtor");
    dbtr.setAcctId("***************************");
    undrlygCstmrCdtTrf.setDbtr(dbtr);

    // 设置债务人账户
    Account dbtrAcct = new Account();
    dbtrAcct.setIban("***************************");
    undrlygCstmrCdtTrf.setDbtrAcct(dbtrAcct);

    // 设置债务人代理机构
    Agent dbtrAgt = createAgent("BOFAUS3NXXX");
    undrlygCstmrCdtTrf.setDbtrAgt(dbtrAgt);

    // 设置债权人代理机构
    Agent cdtrAgt = createAgent("CHASUS33XXX");
    undrlygCstmrCdtTrf.setCdtrAgt(cdtrAgt);

    // 设置债权人
    Party cdtr = new Party();
    cdtr.setNm("Creditor");
    cdtr.setAcctId("**********************");
    undrlygCstmrCdtTrf.setCdtr(cdtr);

    // 设置债权人账户
    Account cdtrAcct = new Account();
    cdtrAcct.setIban("**********************");
    undrlygCstmrCdtTrf.setCdtrAcct(cdtrAcct);

    // 设置最终债权人
    Party ultmtCdtr = new Party();
    ultmtCdtr.setNm("Ultimate Creditor");
    undrlygCstmrCdtTrf.setUltmtCdtr(ultmtCdtr);

    // 设置债权人代理指令
    List<InstructionForCreditorAgent> instrForCdtrAgt = new ArrayList<>();
    InstructionForCreditorAgent cdtrAgtInstr = new InstructionForCreditorAgent();
    cdtrAgtInstr.setCd("PHOB");
    cdtrAgtInstr.setInstrInf("Please handle with care");
    instrForCdtrAgt.add(cdtrAgtInstr);
    undrlygCstmrCdtTrf.setInstrForCdtrAgt(instrForCdtrAgt);

    // 设置汇款信息
    List<String> rmtInf =
        Arrays.asList("Underlying payment for invoice #12345", "Underlying order ID: 67890");
    undrlygCstmrCdtTrf.setRmtInf(rmtInf);

    // 设置指示金额
    Amount instdAmt = new Amount();
    instdAmt.setAmount(BigDecimal.valueOf(500000.00));
    instdAmt.setCurrency("USD");
    undrlygCstmrCdtTrf.setInstdAmt(instdAmt);

    // 设置底层客户信用转账
    txInfo.setUndrlygCstmrCdtTrf(undrlygCstmrCdtTrf);

    return requestInfo;
  }

  private void verifyUnderlyingCustomerCreditTransfer(MxPacs00900108 mxPacs009) {
    var cdtTrfTxInf = mxPacs009.getFICdtTrf().getCdtTrfTxInf().get(0);

    // 验证底层客户信用转账
    assertNotNull(cdtTrfTxInf.getUndrlygCstmrCdtTrf());

    var undrlygCstmrCdtTrf = cdtTrfTxInf.getUndrlygCstmrCdtTrf();

    // 验证最终债务人
    assertNotNull(undrlygCstmrCdtTrf.getUltmtDbtr());
    assertEquals("Ultimate Debtor", undrlygCstmrCdtTrf.getUltmtDbtr().getNm());

    // 验证发起方
    assertNotNull(undrlygCstmrCdtTrf.getInitgPty());
    assertEquals("Initiating Party", undrlygCstmrCdtTrf.getInitgPty().getNm());

    // 验证债务人
    assertNotNull(undrlygCstmrCdtTrf.getDbtr());
    assertEquals("Debtor", undrlygCstmrCdtTrf.getDbtr().getNm());

    // 验证债务人账户
    assertNotNull(undrlygCstmrCdtTrf.getDbtrAcct());
    assertEquals("***************************", undrlygCstmrCdtTrf.getDbtrAcct().getId().getIBAN());

    // 验证债务人代理机构
    assertNotNull(undrlygCstmrCdtTrf.getDbtrAgt());
    assertEquals("BOFAUS3NXXX", undrlygCstmrCdtTrf.getDbtrAgt().getFinInstnId().getBICFI());

    // 验证债权人代理机构
    assertNotNull(undrlygCstmrCdtTrf.getCdtrAgt());
    assertEquals("CHASUS33XXX", undrlygCstmrCdtTrf.getCdtrAgt().getFinInstnId().getBICFI());

    // 验证债权人
    assertNotNull(undrlygCstmrCdtTrf.getCdtr());
    assertEquals("Creditor", undrlygCstmrCdtTrf.getCdtr().getNm());

    // 验证债权人账户
    assertNotNull(undrlygCstmrCdtTrf.getCdtrAcct());
    assertEquals("**********************", undrlygCstmrCdtTrf.getCdtrAcct().getId().getIBAN());

    // 验证最终债权人
    assertNotNull(undrlygCstmrCdtTrf.getUltmtCdtr());
    assertEquals("Ultimate Creditor", undrlygCstmrCdtTrf.getUltmtCdtr().getNm());

    // 验证债权人代理指令
    assertNotNull(undrlygCstmrCdtTrf.getInstrForCdtrAgt());
    assertEquals(1, undrlygCstmrCdtTrf.getInstrForCdtrAgt().size());
    assertEquals("PHOB", undrlygCstmrCdtTrf.getInstrForCdtrAgt().get(0).getCd().name());
    assertEquals(
        "Please handle with care", undrlygCstmrCdtTrf.getInstrForCdtrAgt().get(0).getInstrInf());

    // 验证汇款信息
    assertNotNull(undrlygCstmrCdtTrf.getRmtInf());
    assertEquals(2, undrlygCstmrCdtTrf.getRmtInf().getUstrd().size());
    assertEquals(
        "Underlying payment for invoice #12345", undrlygCstmrCdtTrf.getRmtInf().getUstrd().get(0));
    assertEquals("Underlying order ID: 67890", undrlygCstmrCdtTrf.getRmtInf().getUstrd().get(1));

    // 验证指示金额
    assertNotNull(undrlygCstmrCdtTrf.getInstdAmt());
    assertEquals("USD", undrlygCstmrCdtTrf.getInstdAmt().getCcy());
    assertEquals(500000.00, undrlygCstmrCdtTrf.getInstdAmt().getValue().doubleValue());
  }
}
