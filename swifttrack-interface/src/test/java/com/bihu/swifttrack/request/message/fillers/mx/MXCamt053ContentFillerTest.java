package com.bihu.swifttrack.request.message.fillers.mx;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.bihu.swifttrack.mybatis.repository.po.MessageDetailsPO;
import com.bihu.swifttrack.request.message.MessageRequest;
import com.bihu.swifttrack.request.message.mx.Account;
import com.bihu.swifttrack.request.message.mx.Account.Identification;
import com.bihu.swifttrack.request.message.mx.Agent;
import com.bihu.swifttrack.request.message.mx.BusinessApplicationHeader;
import com.bihu.swifttrack.request.message.mx.CamtGroupHeader;
import com.bihu.swifttrack.request.message.mx.DateTimePeriod1;
import com.bihu.swifttrack.request.message.mx.MXCamt053RequestInfo;
import com.bihu.swifttrack.request.message.mx.MXCamt053RequestInfo.AccountStatement;
import com.bihu.swifttrack.request.message.mx.MXCamt053RequestInfo.AccountStatement.CashBalance8;
import com.bihu.swifttrack.request.message.mx.MXCamt053RequestInfo.AccountStatement.DateAndDateTime2Choice;
import com.bihu.swifttrack.request.message.mx.MXCamt053RequestInfo.Pagination1;
import com.bihu.swifttrack.request.message.mx.ReportEntry10;
import com.bihu.swifttrack.request.message.mx.TypedValue;
import com.bihu.swifttrack.request.message.mx.TypedValue.ValueType;
import com.prowidesoftware.swift.model.mx.MxCamt05300108;
import com.prowidesoftware.swift.model.mx.dic.ActiveOrHistoricCurrencyAndAmount;
import com.prowidesoftware.swift.model.mx.dic.ActiveOrHistoricCurrencyAndAmountRange2;
import com.prowidesoftware.swift.model.mx.dic.BalanceType10Choice;
import com.prowidesoftware.swift.model.mx.dic.BalanceType13;
import com.prowidesoftware.swift.model.mx.dic.CashAvailability1;
import com.prowidesoftware.swift.model.mx.dic.CopyDuplicate1Code;
import com.prowidesoftware.swift.model.mx.dic.CreditDebitCode;
import com.prowidesoftware.swift.model.mx.dic.CreditLine3;
import com.prowidesoftware.swift.model.mx.dic.EntryStatus1Choice;
import com.prowidesoftware.swift.model.mx.dic.ImpliedCurrencyAmountRange1Choice;
import com.prowidesoftware.swift.model.mx.dic.NumberAndSumOfTransactions4;
import com.prowidesoftware.swift.model.mx.dic.Rate4;
import com.prowidesoftware.swift.model.mx.dic.RateType4Choice;
import com.prowidesoftware.swift.model.mx.dic.SequenceRange1Choice;
import com.prowidesoftware.swift.model.mx.dic.TaxCharges2;
import com.prowidesoftware.swift.model.mx.dic.TotalTransactions6;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class MXCamt053ContentFillerTest {

  private static final String FROM_BIC = "BOFAUS3NRLY";
  private static final String TO_BIC = "CHASUS33RLY";
  private static final String BIZ_MSG_IDR = "CAMT053-2023-05-15-001";
  private static final String MSG_ID = "MSGID-2023-05-15-001";
  private static final String STMT_ID = "STMT-2023-05-15-001";
  private static final String IBAN = "**********************";
  private static final String ACCOUNT_OWNER = "John Doe";

  private static final String FROM_DATE = "2023-05-15 00:00:00";
  private static final String TO_DATE = "2023-05-15 23:59:59";
  private static final String INTEREST_REASON = "Interest calculation reason";


  private static final String CURRENCY = "USD";
  private static final String SERVICER_BIC = "CHASUS33XXX";
  private static final LocalDateTime TEST_DATETIME = LocalDateTime.of(2023, 5, 15, 2, 0);
  private static final String RECEIVER_NAME = "RECEIVER BANK";
  private static final String RECEIVER_COUNTRY = "CN";

  private MXCamt053ContentFiller filler;
  private MessageRequest messageRequest;
  private List<MessageDetailsPO> messageDetailsList;
  private MXCamt053RequestInfo requestInfo;

  @BeforeEach
  void setUp() {
    filler = new MXCamt053ContentFiller();
    messageRequest = mock(MessageRequest.class);
    messageDetailsList = new ArrayList<>();
    requestInfo = createValidRequestInfo();
  }

  @Test
  void fillContent_withNullRequestInfo_shouldThrowException() {
    when(messageRequest.getMxCamt053RequestInfo()).thenReturn(null);

    assertThrows(
        IllegalArgumentException.class,
        () -> filler.fillContent(messageDetailsList, messageRequest));
  }

  @Test
  void fillContent_withCompleteRequestInfo_shouldFillAllFields() throws Exception {
    // Enhance requestInfo with additional fields
    enhanceRequestInfo(requestInfo);
    when(messageRequest.getMxCamt053RequestInfo()).thenReturn(requestInfo);

    filler.fillContent(messageDetailsList, messageRequest);

    MessageDetailsPO messageDetails = messageDetailsList.get(0);
    MxCamt05300108 mxCamt053 = MxCamt05300108.parse(messageDetails.getFieldValue());

    System.out.println(messageDetails.getFieldValue());

    // Enhanced verification methods
    verifyBusinessApplicationHeader(mxCamt053);
    verifyGroupHeader(mxCamt053);
    verifyStatementDetails(mxCamt053);
    verifyBalancesAndEntries(mxCamt053);
  }

  private void enhanceRequestInfo(MXCamt053RequestInfo requestInfo) {
    // Enhance group header
    CamtGroupHeader groupHeader = requestInfo.getGroupHeader();
    groupHeader.setAddtlInf("Additional Information");

    // Add original business query
    CamtGroupHeader.OriginalBusinessQuery1 origQuery = new CamtGroupHeader.OriginalBusinessQuery1();
    origQuery.setMsgId("ORIG-MSG-001");
    origQuery.setMsgNmId("camt.060.001.05");
    origQuery.setCreDtTm(TEST_DATETIME);
    groupHeader.setOrgnlBizQry(origQuery);

    // Enhance statement
    AccountStatement stmt = requestInfo.getStatement().get(0);

    // Set all basic fields
    stmt.setId(STMT_ID);
    stmt.setElctrncSeqNb(new BigDecimal("1"));
    stmt.setLglSeqNb(new BigDecimal("1"));
    stmt.setCpyDplctInd(CopyDuplicate1Code.DUPL);
    stmt.setCreDtTm(TEST_DATETIME);
    stmt.setAddtlStmtInf("Additional Information");

    // Set pagination
    Pagination1 stmtPgntn = new Pagination1();
    stmtPgntn.setPgNb("1");
    stmtPgntn.setLastPgInd(true);
    stmt.setStmtPgntn(stmtPgntn);

    // Set reporting sequence
    SequenceRange1Choice rptgSeq = new SequenceRange1Choice();
    rptgSeq.setFrSeq("1");
    rptgSeq.setToSeq("1");
    stmt.setRptgSeq(rptgSeq);

    // Set reporting source
    TypedValue rptgSrc = new TypedValue();
    rptgSrc.setType(ValueType.CODE);
    rptgSrc.setValue("SWIFT");
    stmt.setRptgSrc(rptgSrc);

    // Set date range
    DateTimePeriod1 dateRange = new DateTimePeriod1();
    dateRange.setFrDtTm(TEST_DATETIME.minusDays(1));
    dateRange.setToDtTm(TEST_DATETIME);
    stmt.setFrToDt(dateRange);

    // Set related account
    Account rltdAcct = new Account();
    rltdAcct.setIban("**********************");
    rltdAcct.setNm("Related Account Owner");
    rltdAcct.setCcy("USD");
    Agent rltdSvcr = new Agent();
    rltdSvcr.setBicfi("DEUTDEFFXXX");
    rltdAcct.setSvcr(rltdSvcr);
    stmt.setRltdAcct(rltdAcct);

    // Set interest information
    stmt.setIntrst(Collections.singletonList(createAccountInterest()));

    // Set transaction summary
    TotalTransactions6 txSummary = new TotalTransactions6();
    txSummary.setTtlNtries(createTotalEntries());
    stmt.setTxsSummry(txSummary);

    // Set transaction summary
    TotalTransactions6 txsSummry = new TotalTransactions6();
    // Set transaction summary details...
    stmt.setTxsSummry(txsSummry);

    // Set cash balance availability
    List<CashBalance8> balances = stmt.getBal();
    for (CashBalance8 balance : balances) {
      // Set balance type if not already set
      if (balance.getTp() == null) {
        BalanceType13 balanceType = new BalanceType13();
        balanceType.setCdOrPrtry(new BalanceType10Choice().setCd("CLBD")); // Closing Booked
        balance.setTp(balanceType);
      }

      // Set balance amount if not already set
      if (balance.getAmt() == null) {
        ActiveOrHistoricCurrencyAndAmount amount = new ActiveOrHistoricCurrencyAndAmount();
        amount.setCcy("USD");
        amount.setValue(new BigDecimal("1000.00"));
        balance.setAmt(amount);
      }

      // Set credit/debit indicator if not already set
      if (balance.getCdtDbtInd() == null) {
        balance.setCdtDbtInd(CreditDebitCode.CRDT);
      }

      // Set availability information
      List<CashAvailability1> avlbty = new ArrayList<>();
      CashAvailability1 availability = new CashAvailability1();
      ActiveOrHistoricCurrencyAndAmount availableAmount = new ActiveOrHistoricCurrencyAndAmount();
      availableAmount.setCcy("USD");
      availableAmount.setValue(new BigDecimal("900.00"));
      availability.setAmt(availableAmount);
      avlbty.add(availability);
      balance.setAvlbty(avlbty);

      // Set credit line information
      List<CreditLine3> creditLines = new ArrayList<>();
      CreditLine3 creditLine = new CreditLine3();

      // Set credit line amount
      ActiveOrHistoricCurrencyAndAmount creditLineAmount = new ActiveOrHistoricCurrencyAndAmount();
      creditLineAmount.setCcy("USD");
      creditLineAmount.setValue(new BigDecimal("5000.00"));
      creditLine.setAmt(creditLineAmount);

      // Set credit line type
      creditLine.setIncl(true); // Indicates if the credit line is included in the balance
      creditLines.add(creditLine);
      balance.setCdtLine(creditLines);

      // Set balance date
      DateAndDateTime2Choice balanceDate = new DateAndDateTime2Choice();
      balanceDate.setDtTm(TEST_DATETIME);
      balance.setDt(balanceDate);
    }
  }

  private NumberAndSumOfTransactions4 createTotalEntries() {
    NumberAndSumOfTransactions4 totalEntries = new NumberAndSumOfTransactions4();

    // Set number of entries
    totalEntries.setNbOfNtries("1"); // 设置条目数量

    // Set total sum
    totalEntries.setSum(new BigDecimal("1000.00")); // 设置总金额

    // Optionally set other fields if needed
    // totalEntries.setTtlNetNtryAmt(new BigDecimal("1000.00"));
    // totalEntries.setCdtDbtInd(CreditDebitCode.CRDT);

    return totalEntries;
  }

  private com.bihu.swifttrack.request.message.mx.AccountInterest4 createAccountInterest() {
    com.bihu.swifttrack.request.message.mx.AccountInterest4 interest =
        new com.bihu.swifttrack.request.message.mx.AccountInterest4();

    // Set interest type
    TypedValue type = new TypedValue();
    type.setType(ValueType.CODE);
    type.setValue("INDY");
    interest.setTp(type);

    // Set rate
    Rate4 rate = new Rate4();
    RateType4Choice rateType = new RateType4Choice();
    rate.setTp(rateType);
    ActiveOrHistoricCurrencyAndAmountRange2 validityRange =
        new ActiveOrHistoricCurrencyAndAmountRange2();
    ImpliedCurrencyAmountRange1Choice impliedCurrencyAmountRange =
        new ImpliedCurrencyAmountRange1Choice();
    validityRange.setAmt(impliedCurrencyAmountRange);
    rate.setVldtyRg(validityRange);
    interest.setRate(Collections.singletonList(rate));

    // Set date range
    DateTimePeriod1 dateRange = new DateTimePeriod1();
    dateRange.setFrDtTm(
        LocalDateTime.parse(FROM_DATE, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
    dateRange.setToDtTm(
        LocalDateTime.parse(TO_DATE, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
    interest.setFrToDt(dateRange);

    // Set reason
    interest.setRsn(INTEREST_REASON);

    // Set tax charges
    TaxCharges2 tax = new TaxCharges2();
    tax.setId("TAX001");
    interest.setTax(tax);

    return interest;
  }

  private void verifyBusinessApplicationHeader(MxCamt05300108 mxCamt053) {
    var appHdr = mxCamt053.getAppHdr();
    assertNotNull(appHdr);
    assertEquals(FROM_BIC, appHdr.from());
    assertEquals(TO_BIC, appHdr.to());
    assertEquals(BIZ_MSG_IDR, appHdr.reference());
    assertEquals("camt.053.001.08", appHdr.messageName());
    assertEquals("swift.cbprplus.02", appHdr.serviceName());
    assertFalse(appHdr.duplicate());
    assertNotNull(appHdr.creationDate());
  }

  private void verifyStatement(MxCamt05300108 mxCamt053) {
    var stmt = mxCamt053.getBkToCstmrStmt().getStmt().get(0);
    assertEquals(STMT_ID, stmt.getId());
    assertEquals(IBAN, stmt.getAcct().getId().getIBAN());
    assertEquals(ACCOUNT_OWNER, stmt.getAcct().getOwnr().getNm());
    assertEquals("USD", stmt.getAcct().getCcy());

    // Verify balances
    assertEquals(2, stmt.getBal().size());
    var openingBalance = stmt.getBal().get(0);
    assertEquals("OPBD", openingBalance.getTp().getCdOrPrtry().getCd());
    assertEquals("1000.00", openingBalance.getAmt().getValue().toString());

    var closingBalance = stmt.getBal().get(1);
    assertEquals("CLBD", closingBalance.getTp().getCdOrPrtry().getCd());
    assertEquals("1500.00", closingBalance.getAmt().getValue().toString());

    // Verify entry
    assertEquals(1, stmt.getNtry().size());
    var entry = stmt.getNtry().get(0);
    assertEquals("500.00", entry.getAmt().getValue().toString());
    assertEquals("CRDT", entry.getCdtDbtInd().value());
  }

  private MXCamt053RequestInfo createValidRequestInfo() {
    MXCamt053RequestInfo requestInfo = new MXCamt053RequestInfo();

    // Set BusinessApplicationHeader
    BusinessApplicationHeader bah = new BusinessApplicationHeader();
    bah.setFrom(FROM_BIC);
    bah.setTo(TO_BIC);
    bah.setBizMsgIdr(BIZ_MSG_IDR);
    bah.setMsgDefIdr("camt.053.001.08");
    bah.setBizSvc("swift.cbprplus.02");
    bah.setCreDt(LocalDateTime.of(2023, 5, 15, 2, 0));
    requestInfo.setBusinessApplicationHeader(bah);

    // Set CamtGroupHeader
    CamtGroupHeader groupHeader = new CamtGroupHeader();
    groupHeader.setMsgId(MSG_ID);
    groupHeader.setCreDtTm(LocalDateTime.of(2023, 5, 15, 2, 0));

    // Add PartyIdentification
    CamtGroupHeader.PartyIdentification135 msgRcpt = new CamtGroupHeader.PartyIdentification135();
    msgRcpt.setNm("RECEIVER BANK");
    msgRcpt.setCtryOfRes("CN");
    groupHeader.setMsgRcpt(msgRcpt);

    // Add Pagination
    Pagination1 pagination = new Pagination1();
    pagination.setPgNb("1");
    pagination.setLastPgInd(true);
    groupHeader.setMsgPgntn(pagination);

    requestInfo.setGroupHeader(groupHeader);

    // Set AccountStatement
    AccountStatement statement = new AccountStatement();
    statement.setId(STMT_ID);
    statement.setCreDtTm(LocalDateTime.of(2023, 5, 15, 2, 0));

    // Set Account
    Account account = new Account();
    Identification id = new Identification();
    id.setType("IBAN");
    id.setValue(IBAN);
    account.setId(id);
    account.setOwnrNm(ACCOUNT_OWNER);
    account.setCcy("USD");

    // Add Account Servicer
    Agent servicer = new Agent();
    servicer.setBicfi(SERVICER_BIC);
    account.setSvcr(servicer);

    statement.setAcct(account);

    // Set Balances
    List<CashBalance8> balances = new ArrayList<>();
    balances.add(createBalance("OPBD", "1000.00", "USD")); // Opening balance
    balances.add(createBalance("CLBD", "1500.00", "USD")); // Closing balance
    statement.setBal(balances);

    // Set Entry
    ReportEntry10 entry = new ReportEntry10();
    ActiveOrHistoricCurrencyAndAmount entryAmt = new ActiveOrHistoricCurrencyAndAmount();
    entryAmt.setCcy("USD");
    entryAmt.setValue(new BigDecimal("500.00"));
    entry.setAmt(entryAmt);
    entry.setCdtDbtInd(CreditDebitCode.CRDT);

    // Set status
    EntryStatus1Choice status = new EntryStatus1Choice();
    status.setCd("BOOK");
    entry.setSts(status);

    // Set booking date
    DateAndDateTime2Choice bookingDate = new DateAndDateTime2Choice();
    bookingDate.setDt(LocalDate.now());
    entry.setBookgDt(bookingDate);

    statement.setNtry(Collections.singletonList(entry));

    requestInfo.setStatement(Collections.singletonList(statement));

    return requestInfo;
  }

  private CashBalance8 createBalance(String code, String amount, String currency) {
    MXCamt053RequestInfo.AccountStatement.CashBalance8 balance =
        new MXCamt053RequestInfo.AccountStatement.CashBalance8();

    // Set balance type
    BalanceType13 balanceType = new BalanceType13();
    BalanceType10Choice choice = new BalanceType10Choice();
    choice.setCd(code);
    balanceType.setCdOrPrtry(choice);
    balance.setTp(balanceType);

    // Set amount
    ActiveOrHistoricCurrencyAndAmount amt = new ActiveOrHistoricCurrencyAndAmount();
    amt.setCcy(currency);
    amt.setValue(new BigDecimal(amount));
    balance.setAmt(amt);

    // Add credit/debit indicator
    balance.setCdtDbtInd(CreditDebitCode.CRDT);

    // Add date
    DateAndDateTime2Choice balanceDate = new DateAndDateTime2Choice();
    balanceDate.setDt(LocalDate.now());
    balance.setDt(balanceDate);

    return balance;
  }

  private void verifyMxCamt053(MxCamt05300108 mxCamt053) {
    assertNotNull(mxCamt053);
    verifyGroupHeader(mxCamt053);
    verifyStatementDetails(mxCamt053);
    verifyBalancesAndEntries(mxCamt053);
  }

  private void verifyGroupHeader(MxCamt05300108 mxCamt053) {
    var grpHdr = mxCamt053.getBkToCstmrStmt().getGrpHdr();
    assertNotNull(grpHdr);
    assertEquals("Additional Information", grpHdr.getAddtlInf());

    // Verify original business query
    var origQuery = grpHdr.getOrgnlBizQry();
    assertNotNull(origQuery);
    assertEquals("ORIG-MSG-001", origQuery.getMsgId());
    assertEquals("camt.060.001.05", origQuery.getMsgNmId());
    assertEquals(TEST_DATETIME.atOffset(ZoneOffset.UTC), origQuery.getCreDtTm());
  }

  private void verifyStatementDetails(MxCamt05300108 mxCamt053) {
    var stmt = mxCamt053.getBkToCstmrStmt().getStmt().get(0);
    assertNotNull(stmt);

    // Basic fields
    assertEquals(STMT_ID, stmt.getId());
    assertEquals(new BigDecimal("1"), stmt.getElctrncSeqNb());
    assertEquals(new BigDecimal("1"), stmt.getLglSeqNb());
    assertEquals("DUPL", stmt.getCpyDplctInd().value());
    assertEquals(TEST_DATETIME.atOffset(ZoneOffset.UTC), stmt.getCreDtTm());
    assertEquals("Additional Information", stmt.getAddtlStmtInf());

    // Pagination
    assertNotNull(stmt.getStmtPgntn());
    assertEquals("1", stmt.getStmtPgntn().getPgNb());
    assertTrue(stmt.getStmtPgntn().isLastPgInd());

    // Reporting sequence
    assertNotNull(stmt.getRptgSeq());
    assertEquals("1", stmt.getRptgSeq().getFrSeq());
    assertEquals("1", stmt.getRptgSeq().getToSeq());

    // Reporting source
    assertNotNull(stmt.getRptgSrc());
    assertEquals("SWIFT", stmt.getRptgSrc().getCd());

    // Date range
    assertNotNull(stmt.getFrToDt());
    assertEquals(TEST_DATETIME.minusDays(1).atOffset(ZoneOffset.UTC), stmt.getFrToDt().getFrDtTm());
    assertEquals(TEST_DATETIME.atOffset(ZoneOffset.UTC), stmt.getFrToDt().getToDtTm());

    // Related account
    var rltdAcct = stmt.getRltdAcct();
    assertNotNull(rltdAcct);
    assertEquals("**********************", rltdAcct.getId().getIBAN());
    assertEquals("Related Account Owner", rltdAcct.getNm());
    assertEquals("USD", rltdAcct.getCcy());
    assertEquals("**********************", rltdAcct.getId().getIBAN());

    // Interest information
    assertNotNull(stmt.getIntrst());
    assertFalse(stmt.getIntrst().isEmpty());
    var interest = stmt.getIntrst().get(0);
    // Add specific interest verifications based on what you set in enhanceRequestInfo

    // Transaction summary
    assertNotNull(stmt.getTxsSummry());
    // Add specific transaction summary verifications based on what you set in enhanceRequestInfo
  }

  private void verifyBalancesAndEntries(MxCamt05300108 mxCamt053) {
    var stmt = mxCamt053.getBkToCstmrStmt().getStmt().get(0);

    // Verify balances
    assertNotNull(stmt.getBal());
    assertFalse(stmt.getBal().isEmpty());

    for (var balance : stmt.getBal()) {
      // Verify balance type
      assertNotNull(balance.getTp());

      // Verify balance amount
      assertNotNull(balance.getAmt());

      // Verify credit/debit indicator
      assertEquals(CreditDebitCode.CRDT, balance.getCdtDbtInd());

      // Verify balance date
      assertNotNull(balance.getDt());
      assertEquals(TEST_DATETIME.atOffset(ZoneOffset.UTC), balance.getDt().getDtTm());

      // Verify availability
      assertNotNull(balance.getAvlbty());
      assertFalse(balance.getAvlbty().isEmpty());
      var availability = balance.getAvlbty().get(0);
      assertEquals("USD", availability.getAmt().getCcy());

      // Verify credit lines
      assertNotNull(balance.getCdtLine());

    }
  }
}
