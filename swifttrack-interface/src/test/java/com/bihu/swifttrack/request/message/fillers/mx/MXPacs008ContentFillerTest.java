package com.bihu.swifttrack.request.message.fillers.mx;

import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.ADDRESS_LINES;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.BIRTH_CITY;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.BIRTH_COUNTRY;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.BIRTH_DATE;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.BIRTH_PROVINCE;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.BUILDING_NAME;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.BUILDING_NUMBER;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.CLR_CHANL;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.COUNTRY_CODE;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.COUNTRY_SUBDIVISION;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.CTGY_PURP_CODE;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.CURRENCY;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.DEPT;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.DISTRICT_NAME;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.EMAIL_ADDRESS;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.FLOOR;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.FROM_BIC;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.INSTR_PRTY;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.LCL_INSTRM_CODE;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.MOBILE_NUMBER;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.MSG_ID;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.ORG_BIC;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.ORG_LEI;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.POST_BOX;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.POST_CODE;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.PRVS_INSTG_AGT_BIC;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.ROOM;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.STREET_NAME;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.SUB_DEPT;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.SVC_LVL_CODE;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.TOWN_LOCATION_NAME;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.TOWN_NAME;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.TO_BIC;
import static com.bihu.swifttrack.request.message.fillers.mx.MXPacs008ContentFillerTest.TestDataBuilder.createMxPacs008RequestInfo;
import static com.bihu.swifttrack.request.message.mx.TypedValue.ValueType;
import static org.assertj.core.api.Fail.fail;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.bihu.swifttrack.dto.Amount;
import com.bihu.swifttrack.mybatis.repository.po.MessageDetailsPO;
import com.bihu.swifttrack.request.message.MessageRequest;
import com.bihu.swifttrack.request.message.mx.Account;
import com.bihu.swifttrack.request.message.mx.Agent;
import com.bihu.swifttrack.request.message.mx.BusinessApplicationHeader;
import com.bihu.swifttrack.request.message.mx.ChargesInformation;
import com.bihu.swifttrack.request.message.mx.GroupHeader;
import com.bihu.swifttrack.request.message.mx.InstructionForCreditorAgent;
import com.bihu.swifttrack.request.message.mx.InstructionForNextAgent;
import com.bihu.swifttrack.request.message.mx.MXPacs008RequestInfo;
import com.bihu.swifttrack.request.message.mx.MXPacs008RequestInfo.CreditTransferTransactionInformation39;
import com.bihu.swifttrack.request.message.mx.Other;
import com.bihu.swifttrack.request.message.mx.Party;
import com.bihu.swifttrack.request.message.mx.PaymentIdentification;
import com.bihu.swifttrack.request.message.mx.PaymentTypeInformation;
import com.bihu.swifttrack.request.message.mx.PostalAddress;
import com.bihu.swifttrack.request.message.mx.PreviousInstrictingInfo;
import com.bihu.swifttrack.request.message.mx.RegulatoryReporting;
import com.bihu.swifttrack.request.message.mx.RelatedRemittanceInformation;
import com.bihu.swifttrack.request.message.mx.SettlementInformation;
import com.bihu.swifttrack.request.message.mx.SettlementTimeIndication;
import com.bihu.swifttrack.request.message.mx.SettlementTimeRequest;
import com.bihu.swifttrack.request.message.mx.SupplementaryData;
import com.bihu.swifttrack.request.message.mx.TaxInformation;
import com.bihu.swifttrack.request.message.mx.TypedValue;
import com.prowidesoftware.swift.model.mx.MxPacs00800108;
import com.prowidesoftware.swift.model.mx.dic.BranchAndFinancialInstitutionIdentification6;
import com.prowidesoftware.swift.model.mx.dic.CashAccount38;
import com.prowidesoftware.swift.model.mx.dic.Party38Choice;
import com.prowidesoftware.swift.model.mx.dic.PaymentTypeInformation28;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

class MXPacs008ContentFillerTest {

  private MXPacs008ContentFiller filler;
  private MessageRequest messageRequest;
  private List<MessageDetailsPO> messageDetailsList;

  @BeforeEach
  void setUp() {
    filler = new MXPacs008ContentFiller();
    messageRequest = mock(MessageRequest.class);
    messageDetailsList = new ArrayList<>();
  }

  @ParameterizedTest
  @ValueSource(strings = {"DEBT", "CRED", "SHAR", "SLEV"})
  void fillContent_withDifferentChargeBearers_shouldFillMessageDetails(String chargeBearer)
      throws Exception {
    MXPacs008RequestInfo requestInfo = createMxPacs008RequestInfo(chargeBearer);
    when(messageRequest.getMxPacs008RequestInfo()).thenReturn(requestInfo);

    filler.fillContent(messageDetailsList, messageRequest);

    assertFalse(messageDetailsList.isEmpty());
    MessageDetailsPO messageDetails = messageDetailsList.get(0);
    assertNotNull(messageDetails);
    assertEquals("mxPacs008", messageDetails.getFieldName());

    System.out.println(messageDetails.getFieldValue());

    MxPacs00800108 mxPacs008 = MxPacs00800108.parse(messageDetails.getFieldValue());
    assertNotNull(mxPacs008);

    verifyBusinessApplicationHeader(mxPacs008);
    verifyGroupHeader(mxPacs008);
    verifyCreditTransferTransactionInformation(mxPacs008, chargeBearer);
  }

  @Test
  void fillContent_withNullRequestInfo_shouldThrowException() {
    when(messageRequest.getMxPacs008RequestInfo()).thenReturn(null);

    assertThrows(
        IllegalArgumentException.class,
        () -> filler.fillContent(messageDetailsList, messageRequest));
  }

  @Test
  void fillContent_withExtendedGroupHeader_shouldMapCorrectly() throws Exception {
    // 准备带有扩展字段的GroupHeader
    MXPacs008RequestInfo requestInfo =
        TestDataBuilder.createMxPacs008RequestInfoWithExtendedGroupHeader();
    when(messageRequest.getMxPacs008RequestInfo()).thenReturn(requestInfo);

    // 执行测试
    filler.fillContent(messageDetailsList, messageRequest);

    // 验证结果
    assertFalse(messageDetailsList.isEmpty());
    String mxPacs008Str = messageDetailsList.get(0).getFieldValue();
    MxPacs00800108 mxPacs008 = MxPacs00800108.parse(mxPacs008Str);

    // 验证基本字段
    verifyBusinessApplicationHeader(mxPacs008);

    // 验证扩展的GroupHeader字段
    verifyExtendedGroupHeader(mxPacs008);

    // 验证交易信息
    verifyCreditTransferTransactionInformation(mxPacs008, "DEBT");
  }

  @Test
  void fillContent_withPaymentTypeInformation_shouldMapCorrectly() throws Exception {
    // 准备带有PaymentTypeInformation的请求
    MXPacs008RequestInfo requestInfo =
        TestDataBuilder.createMxPacs008RequestInfoWithPaymentTypeInfo();
    when(messageRequest.getMxPacs008RequestInfo()).thenReturn(requestInfo);

    // 执行测试
    filler.fillContent(messageDetailsList, messageRequest);

    // 验证结果
    assertFalse(messageDetailsList.isEmpty());
    String mxPacs008Str = messageDetailsList.get(0).getFieldValue();
    MxPacs00800108 mxPacs008 = MxPacs00800108.parse(mxPacs008Str);

    // 验证PaymentTypeInformation
    PaymentTypeInformation28 pmtTpInf = mxPacs008.getFIToFICstmrCdtTrf().getGrpHdr().getPmtTpInf();
    assertNotNull(pmtTpInf, "Payment type information should not be null");
    verifyPaymentTypeInformation(pmtTpInf);
  }

  @Test
  void fillContent_withSettlementAmountAndDate_shouldMapCorrectly() throws Exception {
    // 准备带有结算金额和日期的请求
    MXPacs008RequestInfo requestInfo =
        TestDataBuilder.createMxPacs008RequestInfoWithSettlementInfo();
    when(messageRequest.getMxPacs008RequestInfo()).thenReturn(requestInfo);

    // 执行测试
    filler.fillContent(messageDetailsList, messageRequest);

    // 验证结果
    assertFalse(messageDetailsList.isEmpty());
    String mxPacs008Str = messageDetailsList.get(0).getFieldValue();
    MxPacs00800108 mxPacs008 = MxPacs00800108.parse(mxPacs008Str);

    // 验证结算金额和日期
    var grpHdr = mxPacs008.getFIToFICstmrCdtTrf().getGrpHdr();

    // 验证总银行间结算金额
    assertNotNull(
        grpHdr.getTtlIntrBkSttlmAmt(), "Total interbank settlement amount should not be null");
    assertEquals(CURRENCY, grpHdr.getTtlIntrBkSttlmAmt().getCcy());
    assertEquals(new BigDecimal("1000.00"), grpHdr.getTtlIntrBkSttlmAmt().getValue());

    // 验证银行间结算日期
    assertNotNull(grpHdr.getIntrBkSttlmDt(), "Interbank settlement date should not be null");
    assertEquals(LocalDate.of(2024, 10, 10), grpHdr.getIntrBkSttlmDt());

    // 验证控制总和
    assertNotNull(grpHdr.getCtrlSum(), "Control sum should not be null");
    assertEquals(new BigDecimal("1000.00"), grpHdr.getCtrlSum());
  }

  @Test
  void fillContent_withAdditionalFields_shouldMapCorrectly() throws Exception {
    // 准备带有额外字段的请求
    MXPacs008RequestInfo requestInfo =
        TestDataBuilder.createMxPacs008RequestInfoWithAdditionalFields();
    when(messageRequest.getMxPacs008RequestInfo()).thenReturn(requestInfo);

    // 执行测试
    filler.fillContent(messageDetailsList, messageRequest);

    // 验证结果
    assertFalse(messageDetailsList.isEmpty());
    String mxPacs008Str = messageDetailsList.get(0).getFieldValue();
    MxPacs00800108 mxPacs008 = MxPacs00800108.parse(mxPacs008Str);

    // 验证交易信息中的额外字段
    verifyAdditionalFields(mxPacs008);
  }

  private void verifyBusinessApplicationHeader(MxPacs00800108 mxPacs008) {
    // 跳过应用程序头部的验证，因为我们无法访问正确的类型
    assertNotNull(mxPacs008.getAppHdr());
  }

  private void verifyGroupHeader(MxPacs00800108 mxPacs008) {
    var grpHdr = mxPacs008.getFIToFICstmrCdtTrf().getGrpHdr();
    assertEquals(MSG_ID, grpHdr.getMsgId());
    assertEquals("1", grpHdr.getNbOfTxs());
    assertEquals("CLRG", grpHdr.getSttlmInf().getSttlmMtd().value());

    // Verify InstgRmbrsmntAgt if present
    if (grpHdr.getSttlmInf().getInstgRmbrsmntAgt() != null) {
      assertEquals(FROM_BIC, grpHdr.getSttlmInf().getInstgRmbrsmntAgt().getFinInstnId().getBICFI());
    }

    // Verify InstdRmbrsmntAgt if present
    if (grpHdr.getSttlmInf().getInstdRmbrsmntAgt() != null) {
      assertEquals(TO_BIC, grpHdr.getSttlmInf().getInstdRmbrsmntAgt().getFinInstnId().getBICFI());
    }

    // Ensure at least one of InstgRmbrsmntAgt or InstdRmbrsmntAgt is present for COVE settlement
    // method
    if ("COVE".equals(grpHdr.getSttlmInf().getSttlmMtd().value())) {
      assertTrue(
          grpHdr.getSttlmInf().getInstgRmbrsmntAgt() != null
              || grpHdr.getSttlmInf().getInstdRmbrsmntAgt() != null,
          "For COVE settlement method, at least one of Instructing Reimbursement Agent or"
              + " Instructed Reimbursement Agent must be present");
    }
  }

  private void verifyCreditTransferTransactionInformation(
      MxPacs00800108 mxPacs008, String chargeBearer) {
    // 验证组头
    assertNotNull(mxPacs008.getFIToFICstmrCdtTrf());
    assertNotNull(mxPacs008.getFIToFICstmrCdtTrf().getGrpHdr());
    assertNotNull(mxPacs008.getFIToFICstmrCdtTrf().getGrpHdr().getMsgId());
    assertNotNull(mxPacs008.getFIToFICstmrCdtTrf().getGrpHdr().getCreDtTm());
    assertEquals("1", mxPacs008.getFIToFICstmrCdtTrf().getGrpHdr().getNbOfTxs());
    assertNotNull(mxPacs008.getFIToFICstmrCdtTrf().getGrpHdr().getSttlmInf());
    assertEquals(
        "CLRG",
        mxPacs008.getFIToFICstmrCdtTrf().getGrpHdr().getSttlmInf().getSttlmMtd().toString());

    // 验证交易信息
    assertNotNull(mxPacs008.getFIToFICstmrCdtTrf().getCdtTrfTxInf());
    assertEquals(1, mxPacs008.getFIToFICstmrCdtTrf().getCdtTrfTxInf().size());

    var cdtTrfTxInf = mxPacs008.getFIToFICstmrCdtTrf().getCdtTrfTxInf().get(0);

    // 验证结算优先级 - 注意：在某些测试用例中，sttlmPrty 可能为 null
    // 我们不在这里验证 sttlmPrty，而是在特定的测试方法中验证

    // 验证支付标识
    assertNotNull(cdtTrfTxInf.getPmtId());
    assertNotNull(cdtTrfTxInf.getPmtId().getInstrId());
    assertNotNull(cdtTrfTxInf.getPmtId().getEndToEndId());
    assertNotNull(cdtTrfTxInf.getPmtId().getTxId());
    assertNotNull(cdtTrfTxInf.getPmtId().getUETR());

    // 验证支付类型信息
    assertNotNull(cdtTrfTxInf.getPmtTpInf());
    assertEquals("HIGH", cdtTrfTxInf.getPmtTpInf().getInstrPrty().toString());
    assertEquals("RTGS", cdtTrfTxInf.getPmtTpInf().getClrChanl().toString());
    assertNotNull(cdtTrfTxInf.getPmtTpInf().getSvcLvl());
    assertEquals("URGP", cdtTrfTxInf.getPmtTpInf().getSvcLvl().get(0).getCd());
    assertNotNull(cdtTrfTxInf.getPmtTpInf().getLclInstrm());
    assertEquals("INST", cdtTrfTxInf.getPmtTpInf().getLclInstrm().getCd());
    assertNotNull(cdtTrfTxInf.getPmtTpInf().getCtgyPurp());
    assertEquals("CASH", cdtTrfTxInf.getPmtTpInf().getCtgyPurp().getCd());

    // 验证结算金额
    assertNotNull(cdtTrfTxInf.getIntrBkSttlmAmt());
    assertEquals("USD", cdtTrfTxInf.getIntrBkSttlmAmt().getCcy());
    assertEquals(new BigDecimal("6390.95"), cdtTrfTxInf.getIntrBkSttlmAmt().getValue());

    // 验证结算日期
    assertNotNull(cdtTrfTxInf.getIntrBkSttlmDt());

    // 验证指示金额
    assertNotNull(cdtTrfTxInf.getInstdAmt());
    assertEquals("USD", cdtTrfTxInf.getInstdAmt().getCcy());
    assertEquals(new BigDecimal("6400.95"), cdtTrfTxInf.getInstdAmt().getValue());

    // 验证费用承担方
    assertEquals(chargeBearer, cdtTrfTxInf.getChrgBr().toString());

    // 验证费用信息
    if (!"SLEV".equals(chargeBearer)) {
      assertNotNull(cdtTrfTxInf.getChrgsInf());
      assertFalse(cdtTrfTxInf.getChrgsInf().isEmpty());
      assertNotNull(cdtTrfTxInf.getChrgsInf().get(0).getAmt());
      assertEquals("USD", cdtTrfTxInf.getChrgsInf().get(0).getAmt().getCcy());
      assertEquals(new BigDecimal("10.00"), cdtTrfTxInf.getChrgsInf().get(0).getAmt().getValue());
      assertNotNull(cdtTrfTxInf.getChrgsInf().get(0).getAgt());
      assertNotNull(cdtTrfTxInf.getChrgsInf().get(0).getAgt().getFinInstnId());
      assertEquals(
          "BOFAUS3NRLY", cdtTrfTxInf.getChrgsInf().get(0).getAgt().getFinInstnId().getBICFI());
    }

    // 验证前一指示代理
    assertNotNull(cdtTrfTxInf.getPrvsInstgAgt1());
    assertNotNull(cdtTrfTxInf.getPrvsInstgAgt1().getFinInstnId());
    assertEquals("BOFAUS3NRLC", cdtTrfTxInf.getPrvsInstgAgt1().getFinInstnId().getBICFI());

    // 验证前一指示代理账户
    assertNotNull(cdtTrfTxInf.getPrvsInstgAgt1Acct());
    assertNotNull(cdtTrfTxInf.getPrvsInstgAgt1Acct().getId());
    assertNotNull(cdtTrfTxInf.getPrvsInstgAgt1Acct().getId().getOthr());
    assertEquals("**********", cdtTrfTxInf.getPrvsInstgAgt1Acct().getId().getOthr().getId());
    assertNotNull(cdtTrfTxInf.getPrvsInstgAgt1Acct().getId().getOthr().getSchmeNm());
    assertEquals("BBAN", cdtTrfTxInf.getPrvsInstgAgt1Acct().getId().getOthr().getSchmeNm().getCd());
    assertEquals("BANK", cdtTrfTxInf.getPrvsInstgAgt1Acct().getId().getOthr().getIssr());

    // 验证指示代理
    assertNotNull(cdtTrfTxInf.getInstgAgt());
    assertNotNull(cdtTrfTxInf.getInstgAgt().getFinInstnId());
    assertEquals("BOFAUS3NRLY", cdtTrfTxInf.getInstgAgt().getFinInstnId().getBICFI());

    // 验证被指示代理
    assertNotNull(cdtTrfTxInf.getInstdAgt());
    assertNotNull(cdtTrfTxInf.getInstdAgt().getFinInstnId());
    assertEquals("CHASUS33RLY", cdtTrfTxInf.getInstdAgt().getFinInstnId().getBICFI());

    // 验证债务人
    assertNotNull(cdtTrfTxInf.getDbtr());
    assertEquals("John Doe", cdtTrfTxInf.getDbtr().getNm());

    // 验证债务人账户
    assertNotNull(cdtTrfTxInf.getDbtrAcct());
    assertNotNull(cdtTrfTxInf.getDbtrAcct().getId());
    assertNotNull(cdtTrfTxInf.getDbtrAcct().getId().getOthr());
    assertEquals("**********", cdtTrfTxInf.getDbtrAcct().getId().getOthr().getId());

    // 验证债务人代理
    assertNotNull(cdtTrfTxInf.getDbtrAgt());
    assertNotNull(cdtTrfTxInf.getDbtrAgt().getFinInstnId());
    assertEquals("BOFAUS3NRLY", cdtTrfTxInf.getDbtrAgt().getFinInstnId().getBICFI());

    // 验证债权人代理
    assertNotNull(cdtTrfTxInf.getCdtrAgt());
    assertNotNull(cdtTrfTxInf.getCdtrAgt().getFinInstnId());
    assertEquals("CHASUS33RLY", cdtTrfTxInf.getCdtrAgt().getFinInstnId().getBICFI());

    // 验证债权人
    assertNotNull(cdtTrfTxInf.getCdtr());
    assertEquals("Jane Smith", cdtTrfTxInf.getCdtr().getNm());

    // 验证债权人账户
    assertNotNull(cdtTrfTxInf.getCdtrAcct());
    assertNotNull(cdtTrfTxInf.getCdtrAcct().getId());
    assertNotNull(cdtTrfTxInf.getCdtrAcct().getId().getOthr());
    assertEquals("**********", cdtTrfTxInf.getCdtrAcct().getId().getOthr().getId());

    // 验证目的
    assertNotNull(cdtTrfTxInf.getPurp());
    assertEquals("CASH", cdtTrfTxInf.getPurp().getCd());

    // 验证汇款信息
    assertNotNull(cdtTrfTxInf.getRmtInf());
    assertNotNull(cdtTrfTxInf.getRmtInf().getUstrd());
    assertEquals(2, cdtTrfTxInf.getRmtInf().getUstrd().size());
    assertEquals("Payment for invoice #12345", cdtTrfTxInf.getRmtInf().getUstrd().get(0));
    assertEquals("Order ID: 67890", cdtTrfTxInf.getRmtInf().getUstrd().get(1));
  }

  private void verifyPartyInformation(
      com.prowidesoftware.swift.model.mx.dic.Party38Choice partyId, boolean isOrganization) {
    assertNotNull(partyId);
    if (isOrganization) {
      assertNotNull(partyId.getOrgId());
      assertEquals(ORG_BIC, partyId.getOrgId().getAnyBIC());
      assertEquals(ORG_LEI, partyId.getOrgId().getLEI());
      assertNotNull(partyId.getOrgId().getOthr());
      assertEquals(1, partyId.getOrgId().getOthr().size());
      assertEquals("ORG_ID", partyId.getOrgId().getOthr().get(0).getId());
      assertEquals("ORG_SCHEME", partyId.getOrgId().getOthr().get(0).getSchmeNm().getCd());
      assertEquals("ORG_ISSUER", partyId.getOrgId().getOthr().get(0).getIssr());
    } else {
      assertNotNull(partyId.getPrvtId());
      var dtAndPlcOfBirth = partyId.getPrvtId().getDtAndPlcOfBirth();
      assertNotNull(dtAndPlcOfBirth);
      assertEquals(BIRTH_DATE, dtAndPlcOfBirth.getBirthDt());
      assertEquals(BIRTH_PROVINCE, dtAndPlcOfBirth.getPrvcOfBirth());
      assertEquals(BIRTH_CITY, dtAndPlcOfBirth.getCityOfBirth());
      assertEquals(BIRTH_COUNTRY, dtAndPlcOfBirth.getCtryOfBirth());
      assertNotNull(partyId.getPrvtId().getOthr());
      assertEquals(1, partyId.getPrvtId().getOthr().size());
      assertEquals("PRVT_ID", partyId.getPrvtId().getOthr().get(0).getId());
      assertEquals("PRVT_SCHEME", partyId.getPrvtId().getOthr().get(0).getSchmeNm().getCd());
      assertEquals("PRVT_ISSUER", partyId.getPrvtId().getOthr().get(0).getIssr());
    }
  }

  private void verifyContactInfo(com.prowidesoftware.swift.model.mx.dic.Contact4 contactDetails) {
    assertNotNull(contactDetails);
    assertEquals(MOBILE_NUMBER, contactDetails.getMobNb());
    assertEquals(EMAIL_ADDRESS, contactDetails.getEmailAdr());
  }

  private void verifyPostalAddress(
      com.prowidesoftware.swift.model.mx.dic.PostalAddress24 postalAddress) {
    assertNotNull(postalAddress);
    assertEquals(DEPT, postalAddress.getDept());
    assertEquals(SUB_DEPT, postalAddress.getSubDept());
    assertEquals(STREET_NAME, postalAddress.getStrtNm());
    assertEquals(BUILDING_NUMBER, postalAddress.getBldgNb());
    assertEquals(BUILDING_NAME, postalAddress.getBldgNm());
    assertEquals(FLOOR, postalAddress.getFlr());
    assertEquals(POST_BOX, postalAddress.getPstBx());
    assertEquals(ROOM, postalAddress.getRoom());
    assertEquals(POST_CODE, postalAddress.getPstCd());
    assertEquals(TOWN_NAME, postalAddress.getTwnNm());
    assertEquals(TOWN_LOCATION_NAME, postalAddress.getTwnLctnNm());
    assertEquals(DISTRICT_NAME, postalAddress.getDstrctNm());
    assertEquals(COUNTRY_SUBDIVISION, postalAddress.getCtrySubDvsn());
    assertEquals(COUNTRY_CODE, postalAddress.getCtry());
    assertEquals(ADDRESS_LINES, postalAddress.getAdrLine());
  }

  private void verifyPaymentTypeInformation(PaymentTypeInformation28 pmtTpInf) {
    assertNotNull(pmtTpInf);
    assertEquals(INSTR_PRTY, pmtTpInf.getInstrPrty().value());
    assertEquals(CLR_CHANL, pmtTpInf.getClrChanl().value());

    verifyTypedValue(pmtTpInf.getSvcLvl().get(0), ValueType.CODE, SVC_LVL_CODE);
    verifyTypedValue(pmtTpInf.getLclInstrm(), ValueType.CODE, LCL_INSTRM_CODE);
    verifyTypedValue(pmtTpInf.getCtgyPurp(), ValueType.CODE, CTGY_PURP_CODE);
  }

  private void verifyOtherInformation(Party38Choice party) {
    var other = party.getOrgId().getOthr().get(0);

    assertEquals("ORG_ID", other.getId());
    verifyTypedValue(other.getSchmeNm(), ValueType.CODE, "ORG_SCHEME");
    assertEquals("ORG_ISSUER", other.getIssr());
  }

  private void verifyTypedValue(
      Object object, TypedValue.ValueType expectedType, String expectedValue) {
    assertNotNull(object);

    if (object instanceof TypedValue) {
      TypedValue typedValue = (TypedValue) object;
      assertEquals(expectedType, typedValue.getType());
      assertEquals(expectedValue, typedValue.getValue());
    } else {
      try {
        Method getCdMethod = object.getClass().getMethod("getCd");
        Method getPrtryMethod = object.getClass().getMethod("getPrtry");

        if (expectedType == TypedValue.ValueType.CODE) {
          assertEquals(expectedValue, getCdMethod.invoke(object));
          assertNull(getPrtryMethod.invoke(object));
        } else if (expectedType == TypedValue.ValueType.PROPRIETARY) {
          assertEquals(expectedValue, getPrtryMethod.invoke(object));
          assertNull(getCdMethod.invoke(object));
        } else {
          fail("Unexpected ValueType for object: " + object.getClass().getName());
        }
      } catch (NoSuchMethodException e) {
        fail("Object does not have expected methods: " + object.getClass().getName());
      } catch (Exception e) {
        fail("Error occurred during reflection: " + e.getMessage());
      }
    }
  }

  private void verifyPreviousInstructingInfo(
      BranchAndFinancialInstitutionIdentification6 prvsInstgAgt, CashAccount38 prvsInstgAgtAcct) {
    assertNotNull(prvsInstgAgt);
    assertEquals(PRVS_INSTG_AGT_BIC, prvsInstgAgt.getFinInstnId().getBICFI());

    assertNotNull(prvsInstgAgtAcct);
    assertNotNull(prvsInstgAgtAcct.getId().getOthr());
    assertEquals("**********", prvsInstgAgtAcct.getId().getOthr().getId());
  }

  private void verifyAgentInformation(
      BranchAndFinancialInstitutionIdentification6 agent, String expectedBIC) {
    assertNotNull(agent, "Agent should not be null");
    assertNotNull(agent.getFinInstnId(), "Financial Institution Identification should not be null");
    assertEquals(
        expectedBIC, agent.getFinInstnId().getBICFI(), "BIC should match the expected value");
  }

  private void verifyExtendedGroupHeader(MxPacs00800108 mxPacs008) {
    var grpHdr = mxPacs008.getFIToFICstmrCdtTrf().getGrpHdr();

    // 验证基本字段
    assertEquals(MSG_ID, grpHdr.getMsgId());
    assertEquals("1", grpHdr.getNbOfTxs());
    assertEquals("CLRG", grpHdr.getSttlmInf().getSttlmMtd().value());

    // 验证批量记账标志
    assertEquals(Boolean.FALSE, grpHdr.isBtchBookg());

    // 验证控制总和
    assertEquals(new BigDecimal("1000.00"), grpHdr.getCtrlSum());

    // 验证总银行间结算金额
    assertEquals("USD", grpHdr.getTtlIntrBkSttlmAmt().getCcy());
    assertEquals(new BigDecimal("1000.00"), grpHdr.getTtlIntrBkSttlmAmt().getValue());

    // 验证银行间结算日期
    assertEquals(LocalDate.of(2024, 10, 10), grpHdr.getIntrBkSttlmDt());

    // 验证支付类型信息
    verifyPaymentTypeInformation(grpHdr.getPmtTpInf());

    // 验证代理机构
    if (grpHdr.getInstgAgt() != null) {
      assertEquals(FROM_BIC, grpHdr.getInstgAgt().getFinInstnId().getBICFI());
    }

    if (grpHdr.getInstdAgt() != null) {
      assertEquals(TO_BIC, grpHdr.getInstdAgt().getFinInstnId().getBICFI());
    }
  }

  private void verifyAdditionalFields(MxPacs00800108 mxPacs008) {
    var cdtTrfTxInf = mxPacs008.getFIToFICstmrCdtTrf().getCdtTrfTxInf().get(0);

    // 验证结算优先级
    assertEquals("HIGH", cdtTrfTxInf.getSttlmPrty().toString());

    // 验证结算时间指示
    assertNotNull(cdtTrfTxInf.getSttlmTmIndctn());
    assertNotNull(cdtTrfTxInf.getSttlmTmIndctn().getDbtDtTm());
    assertNotNull(cdtTrfTxInf.getSttlmTmIndctn().getCdtDtTm());

    // 验证结算时间请求
    assertNotNull(cdtTrfTxInf.getSttlmTmReq());

    // 验证接受日期时间
    assertNotNull(cdtTrfTxInf.getAccptncDtTm());

    // 验证汇率
    assertEquals(new BigDecimal("1.1"), cdtTrfTxInf.getXchgRate());

    // 验证最终债务人
    assertNotNull(cdtTrfTxInf.getUltmtDbtr());
    assertEquals("Ultimate Debtor", cdtTrfTxInf.getUltmtDbtr().getNm());

    // 验证发起方
    assertNotNull(cdtTrfTxInf.getInitgPty());
    assertEquals("Initiating Party", cdtTrfTxInf.getInitgPty().getNm());

    // 验证债务人代理账户
    assertNotNull(cdtTrfTxInf.getDbtrAgtAcct());

    // 验证债权人代理账户
    assertNotNull(cdtTrfTxInf.getCdtrAgtAcct());

    // 验证最终债权人
    assertNotNull(cdtTrfTxInf.getUltmtCdtr());
    assertEquals("Ultimate Creditor", cdtTrfTxInf.getUltmtCdtr().getNm());

    // 验证债权人代理指令
    assertNotNull(cdtTrfTxInf.getInstrForCdtrAgt());
    assertFalse(cdtTrfTxInf.getInstrForCdtrAgt().isEmpty());
    assertEquals("PHOB", cdtTrfTxInf.getInstrForCdtrAgt().get(0).getCd().value());
    assertEquals(
        "Please contact beneficiary by phone",
        cdtTrfTxInf.getInstrForCdtrAgt().get(0).getInstrInf());

    // 验证下一代理指令
    assertNotNull(cdtTrfTxInf.getInstrForNxtAgt());
    assertFalse(cdtTrfTxInf.getInstrForNxtAgt().isEmpty());
    assertEquals("PHOA", cdtTrfTxInf.getInstrForNxtAgt().get(0).getCd().value());
    assertEquals(
        "Please contact next agent by phone", cdtTrfTxInf.getInstrForNxtAgt().get(0).getInstrInf());

    // 验证监管报告
    assertNotNull(cdtTrfTxInf.getRgltryRptg());
    assertFalse(cdtTrfTxInf.getRgltryRptg().isEmpty());

    // 验证税务信息
    assertNotNull(cdtTrfTxInf.getTax());

    // 验证相关汇款信息
    assertNotNull(cdtTrfTxInf.getRltdRmtInf());
    assertFalse(cdtTrfTxInf.getRltdRmtInf().isEmpty());

    // 验证补充数据
    assertNotNull(cdtTrfTxInf.getSplmtryData());
    assertFalse(cdtTrfTxInf.getSplmtryData().isEmpty());
  }

  private static RelatedRemittanceInformation.StructuredRemittanceInformation
          .ReferredDocumentInformation
      createReferredDocumentInformation() {
    RelatedRemittanceInformation.StructuredRemittanceInformation.ReferredDocumentInformation
        refDocInfo =
            new RelatedRemittanceInformation.StructuredRemittanceInformation
                .ReferredDocumentInformation();
    refDocInfo.setType("CINV");
    refDocInfo.setNumber("INV-12345");
    refDocInfo.setRelatedDate("2024-09-15");
    return refDocInfo;
  }

  private static RelatedRemittanceInformation.StructuredRemittanceInformation.RemittanceAmount
      createReferredDocumentAmount() {
    RelatedRemittanceInformation.StructuredRemittanceInformation.RemittanceAmount refDocAmt =
        new RelatedRemittanceInformation.StructuredRemittanceInformation.RemittanceAmount();
    refDocAmt.setDuePayableAmount(new Amount("USD", new BigDecimal("6390.95")));
    refDocAmt.setDiscountAppliedAmount(new Amount("USD", new BigDecimal("0.00")));
    refDocAmt.setCreditNoteAmount(new Amount("USD", new BigDecimal("0.00")));
    refDocAmt.setTaxAmount(new Amount("USD", new BigDecimal("639.10")));
    refDocAmt.setRemittedAmount(new Amount("USD", new BigDecimal("6390.95")));
    return refDocAmt;
  }

  private static String createCreditorReferenceInformation() {
    return "CRED-REF-12345";
  }

  public static class TestDataBuilder {
    // 所有常量定义移到这里

    public static final String FROM_BIC = "BOFAUS3NRLY";
    public static final String TO_BIC = "CHASUS33RLY";
    public static final String BIZ_MSG_IDR = "BIZ1728440763200";
    public static final String MSG_ID = "MSG1728440763200";
    public static final String INSTR_ID = "INSTR1728440763200";
    public static final String END_TO_END_ID = "E2E1728440763200";
    public static final String TX_ID = "***************";
    public static final double AMOUNT = 6390.95;
    public static final String CURRENCY = "USD";
    public static final String UETR = "97ed4827-7b6f-4491-a06f-b548d5a7512d";
    public static final String DEPT = "Finance";
    public static final String SUB_DEPT = "Accounts";
    public static final String STREET_NAME = "Main Street";
    public static final String BUILDING_NUMBER = "123";
    public static final String BUILDING_NAME = "Tower A";
    public static final String FLOOR = "15";
    public static final String POST_BOX = "PO Box 789";
    public static final String ROOM = "1501";
    public static final String POST_CODE = "12345";
    public static final String TOWN_NAME = "Anytown";
    public static final String TOWN_LOCATION_NAME = "City Center";
    public static final String DISTRICT_NAME = "Downtown";
    public static final String COUNTRY_SUBDIVISION = "State";
    public static final String COUNTRY_CODE = "US";
    public static final List<String> ADDRESS_LINES =
        Arrays.asList("Address Line 1", "Address Line 2");
    public static final String DEBTOR_COUNTRY = "US";
    public static final String CREDITOR_COUNTRY = "GB";
    public static final String MOBILE_NUMBER = "+**********";
    public static final String EMAIL_ADDRESS = "<EMAIL>";
    public static final String DEBTOR_NAME = "John Doe";
    public static final String CREDITOR_NAME = "Jane Smith";
    public static final String DEBTOR_ACCOUNT_ID = "**********";
    public static final String CREDITOR_ACCOUNT_ID = "**********";
    public static final String ORG_BIC = "ORGBICCODE";
    public static final String ORG_LEI = "ORGLEICODE";
    public static final LocalDate BIRTH_DATE = LocalDate.of(1990, 1, 1);
    public static final String BIRTH_PROVINCE = "California";
    public static final String BIRTH_CITY = "Los Angeles";
    public static final String BIRTH_COUNTRY = "US";

    public static final BigDecimal INSTRUCTED_AMOUNT = new BigDecimal("6400.95");
    public static final BigDecimal INTERBANK_SETTLEMENT_AMOUNT = new BigDecimal("6390.95");
    public static final BigDecimal CHARGE_AMOUNT = new BigDecimal("10.00");
    public static final String CHARGE_AGENT_BIC = "BOFAUS3NRLY";
    public static final String INSTR_PRTY = "HIGH";
    public static final String CLR_CHANL = "RTGS";
    public static final String SVC_LVL_CODE = "URGP";
    public static final String LCL_INSTRM_CODE = "INST";
    public static final String CTGY_PURP_CODE = "CASH";
    public static final String PRVS_INSTG_AGT_BIC = "BOFAUS3NRLC";
    public static final String PRVS_INSTG_AGT_ACCT_ID = "**********";

    static MXPacs008RequestInfo createValidRequestInfo(String chargeBearer) {
      MXPacs008RequestInfo requestInfo = new MXPacs008RequestInfo();
      requestInfo.setBusinessApplicationHeader(createBusinessApplicationHeader());
      requestInfo.setGroupHeader(createGroupHeader());
      requestInfo.setCreditTransferTransactionInformation(
          createCreditTransferTransactionInformation(chargeBearer));
      return requestInfo;
    }

    static BusinessApplicationHeader createBusinessApplicationHeader() {
      BusinessApplicationHeader bah = new BusinessApplicationHeader();
      bah.setFrom(FROM_BIC);
      bah.setTo(TO_BIC);
      bah.setBizMsgIdr(BIZ_MSG_IDR);
      bah.setBizSvc("swift.cbprplus.02");
      bah.setCreDt(LocalDateTime.of(2024, 10, 9, 2, 26));
      bah.setMsgDefIdr("pacs.008.001.08");
      return bah;
    }

    static GroupHeader createGroupHeader() {
      GroupHeader groupHeader = new GroupHeader();
      groupHeader.setMsgId(MSG_ID);
      groupHeader.setCreDtTm(LocalDateTime.of(2024, 10, 9, 2, 26));
      groupHeader.setNbOfTxs("1");

      SettlementInformation sttlmInf = new SettlementInformation();
      sttlmInf.setSttlmMtd("CLRG");
      sttlmInf.setInstgRmbrsmntAgt(createAgent(FROM_BIC));
      sttlmInf.setInstdRmbrsmntAgt(createAgent(TO_BIC));
      groupHeader.setSttlmInf(sttlmInf);

      return groupHeader;
    }

    static CreditTransferTransactionInformation39
        createCreditTransferTransactionInformation(String chargeBearer) {
      CreditTransferTransactionInformation39 txInfo =
          new CreditTransferTransactionInformation39();

      // 设置支付标识信息
      PaymentIdentification pmtId = new PaymentIdentification();
      pmtId.setInstrId(INSTR_ID);
      pmtId.setEndToEndId(END_TO_END_ID);
      pmtId.setTxId(TX_ID);
      pmtId.setUetr(UETR);
      txInfo.setPmtId(pmtId);

      // 设置支付类型信息
      PaymentTypeInformation pmtTpInf = new PaymentTypeInformation();
      pmtTpInf.setInstrPrty(INSTR_PRTY);
      pmtTpInf.setClrChanl(CLR_CHANL);
      pmtTpInf.setSvcLvl(Arrays.asList(createTypedValue(TypedValue.ValueType.CODE, SVC_LVL_CODE)));
      pmtTpInf.setLclInstrm(createTypedValue(TypedValue.ValueType.CODE, LCL_INSTRM_CODE));
      pmtTpInf.setCtgyPurp(createTypedValue(TypedValue.ValueType.CODE, CTGY_PURP_CODE));
      txInfo.setPmtTpInf(pmtTpInf);

      // 设置指示金额
      txInfo.setInstdAmt(new Amount(CURRENCY, INSTRUCTED_AMOUNT));

      // 设置银行间结算金额和日期
      txInfo.setIntrBkSttlmAmt(new Amount(CURRENCY, INTERBANK_SETTLEMENT_AMOUNT));
      txInfo.setIntrBkSttlmDt(LocalDate.of(2024, 10, 10));

      // 设置费用承担方和费用信息
      txInfo.setChrgBr(chargeBearer);
      if ("SHAR".equals(chargeBearer)
          || "DEBT".equals(chargeBearer)
          || "CRED".equals(chargeBearer)) {
        ChargesInformation chargeInfo = new ChargesInformation();
        chargeInfo.setAmt(new Amount(CURRENCY, CHARGE_AMOUNT));
        chargeInfo.setAgt(createAgent(CHARGE_AGENT_BIC));
        txInfo.setChrgsInf(Arrays.asList(chargeInfo));
      }

      // 设置前置指示行信息
      List<PreviousInstrictingInfo> previousInstrictingInfos = new ArrayList<>();
      PreviousInstrictingInfo info = new PreviousInstrictingInfo();
      info.setPrvsInstgAgt(createAgent(PRVS_INSTG_AGT_BIC));
      Account account = new Account();
      account.setOther(createOther("**********", "BBAN", "BANK", ValueType.CODE));
      info.setPrvsInstgAgtAcct(account);
      previousInstrictingInfos.add(info);
      txInfo.setPreviousInstrictingInfos(previousInstrictingInfos);

      // 设置发起方和指示方
      txInfo.setInstgAgt(createAgent(FROM_BIC));
      txInfo.setInstdAgt(createAgent(TO_BIC));

      // 设置汇款人和汇款人代理机构
      txInfo.setDebtor(createParty(DEBTOR_NAME, DEBTOR_ACCOUNT_ID, DEBTOR_COUNTRY, true));
      txInfo.setDbtrAgt(createAgent(FROM_BIC));

      // 设置收款人和收款人代理机构
      txInfo.setCreditor(createParty(CREDITOR_NAME, CREDITOR_ACCOUNT_ID, CREDITOR_COUNTRY, false));
      txInfo.setCdtrAgt(createAgent(TO_BIC));

      // 修改汇款信息和用途的设置
      txInfo.setRmtInf(Arrays.asList("Payment for invoice #12345", "Order ID: 67890"));
      txInfo.setPurp(createTypedValue(ValueType.CODE, "CASH"));

      return txInfo;
    }

    static Agent createAgent(String bicfi) {
      Agent agent = new Agent();
      agent.setBicfi(bicfi);
      return agent;
    }

    static Party createParty(
        String name, String accountId, String countryOfResidence, boolean isOrganization) {
      Party party = new Party();
      party.setNm(name);
      party.setAcctId(accountId);
      party.setCtryOfRes(countryOfResidence);
      party.setPstlAdr(createPostalAddress());

      Party.Identification identification = new Party.Identification();
      if (isOrganization) {
        Party.OrganisationIdentification orgId = new Party.OrganisationIdentification();
        orgId.setAnyBic(ORG_BIC);
        orgId.setLei(ORG_LEI);
        orgId.setOthr(
            Arrays.asList(createOther("ORG_ID", "ORG_SCHEME", "ORG_ISSUER", ValueType.CODE)));
        identification.setOrgId(orgId);
      } else {
        Party.PrivateIdentification prvtId = new Party.PrivateIdentification();
        Party.DateAndPlaceOfBirth dateAndPlaceOfBirth = new Party.DateAndPlaceOfBirth();
        dateAndPlaceOfBirth.setBirthDt(BIRTH_DATE);
        dateAndPlaceOfBirth.setPrvcOfBirth(BIRTH_PROVINCE);
        dateAndPlaceOfBirth.setCityOfBirth(BIRTH_CITY);
        dateAndPlaceOfBirth.setCtryOfBirth(BIRTH_COUNTRY);
        prvtId.setDtAndPlcOfBirth(dateAndPlaceOfBirth);
        prvtId.setOthr(
            Arrays.asList(createOther("PRVT_ID", "PRVT_SCHEME", "PRVT_ISSUER", ValueType.CODE)));
        identification.setPrvtId(prvtId);
      }
      party.setId(identification);

      Party.ContactInfo contactInfo = new Party.ContactInfo();
      contactInfo.setMobileNumber(MOBILE_NUMBER);
      contactInfo.setEmailAddress(EMAIL_ADDRESS);
      party.setCtcDtls(contactInfo);

      return party;
    }

    static Other createOther(String id, String schemeName, String issuer, ValueType valueType) {
      Other other = new Other();
      other.setId(id);
      other.setSchmeNm(createTypedValue(valueType, schemeName));
      other.setIssr(issuer);
      return other;
    }

    static PostalAddress createPostalAddress() {
      PostalAddress address = new PostalAddress();
      address.setDept(DEPT);
      address.setSubDept(SUB_DEPT);
      address.setStrtNm(STREET_NAME);
      address.setBldgNb(BUILDING_NUMBER);
      address.setBldgNm(BUILDING_NAME);
      address.setFlr(FLOOR);
      address.setPstBx(POST_BOX);
      address.setRoom(ROOM);
      address.setPstCd(POST_CODE);
      address.setTwnNm(TOWN_NAME);
      address.setTwnLctnNm(TOWN_LOCATION_NAME);
      address.setDstrctNm(DISTRICT_NAME);
      address.setCtrySubDvsn(COUNTRY_SUBDIVISION);
      address.setCtry(COUNTRY_CODE);
      address.setAdrLine(ADDRESS_LINES);
      return address;
    }

    static PaymentTypeInformation createPaymentTypeInformation(ValueType valueType) {
      PaymentTypeInformation pmtTpInf = new PaymentTypeInformation();
      pmtTpInf.setInstrPrty(INSTR_PRTY);
      pmtTpInf.setClrChanl(CLR_CHANL);
      pmtTpInf.setSvcLvl(List.of(createTypedValue(valueType, SVC_LVL_CODE)));
      pmtTpInf.setLclInstrm(createTypedValue(valueType, LCL_INSTRM_CODE));
      pmtTpInf.setCtgyPurp(createTypedValue(valueType, CTGY_PURP_CODE));
      return pmtTpInf;
    }

    static TypedValue createTypedValue(ValueType type, String value) {
      TypedValue typedValue = new TypedValue();
      typedValue.setType(type);
      typedValue.setValue(value);
      return typedValue;
    }

    static List<PreviousInstrictingInfo> createPreviousInstructingInfoList() {
      PreviousInstrictingInfo prvsInstgInfo = new PreviousInstrictingInfo();
      prvsInstgInfo.setPrvsInstgAgt(createAgent(PRVS_INSTG_AGT_BIC));

      // 使用有效的IBAN格式
      Account account = new Account();
      account.setOther(createOther("**********", "BBAN", "BANK", ValueType.CODE));
      prvsInstgInfo.setPrvsInstgAgtAcct(account);

      return Arrays.asList(prvsInstgInfo);
    }

    static Account createAccount(String accountId) {
      Account account = new Account();
      Other other = new Other();
      other.setId(accountId);
      account.setOther(other);
      return account;
    }

    static MXPacs008RequestInfo createMxPacs008RequestInfo(String chargeBearer) {
      return createValidRequestInfo(chargeBearer);
    }

    static MXPacs008RequestInfo createMxPacs008RequestInfo(
        String chargeBearer, ValueType valueType) {
      MXPacs008RequestInfo requestInfo = createValidRequestInfo(chargeBearer);
      requestInfo
          .getCreditTransferTransactionInformation()
          .setPmtTpInf(createPaymentTypeInformation(valueType));
      return requestInfo;
    }

    static MXPacs008RequestInfo createMxPacs008RequestInfoWithExtendedGroupHeader() {
      MXPacs008RequestInfo requestInfo = new MXPacs008RequestInfo();
      requestInfo.setBusinessApplicationHeader(createBusinessApplicationHeader());
      requestInfo.setGroupHeader(createExtendedGroupHeader());
      requestInfo.setCreditTransferTransactionInformation(
          createCreditTransferTransactionInformation("DEBT"));
      return requestInfo;
    }

    static GroupHeader createExtendedGroupHeader() {
      GroupHeader groupHeader = new GroupHeader();

      // 基本字段
      groupHeader.setMsgId(MSG_ID);
      groupHeader.setCreDtTm(LocalDateTime.of(2024, 10, 9, 2, 26));
      groupHeader.setNbOfTxs("1");

      // 结算信息
      SettlementInformation sttlmInf = new SettlementInformation();
      sttlmInf.setSttlmMtd("CLRG");
      sttlmInf.setInstgRmbrsmntAgt(createAgent(FROM_BIC));
      sttlmInf.setInstdRmbrsmntAgt(createAgent(TO_BIC));
      groupHeader.setSttlmInf(sttlmInf);

      // 扩展字段
      groupHeader.setBtchBookg(false);
      groupHeader.setCtrlSum(new BigDecimal("1000.00"));

      // 总银行间结算金额
      Amount amount = new Amount(CURRENCY, new BigDecimal("1000.00"));
      groupHeader.setTtlIntrBkSttlmAmt(amount);

      // 银行间结算日期
      groupHeader.setIntrBkSttlmDt(LocalDate.of(2024, 10, 10));

      // 支付类型信息
      groupHeader.setPmtTpInf(createPaymentTypeInformation(ValueType.CODE));

      // 代理机构
      groupHeader.setInstgAgt(createAgent(FROM_BIC));
      groupHeader.setInstdAgt(createAgent(TO_BIC));

      return groupHeader;
    }

    static MXPacs008RequestInfo createMxPacs008RequestInfoWithPaymentTypeInfo() {
      MXPacs008RequestInfo requestInfo = new MXPacs008RequestInfo();
      requestInfo.setBusinessApplicationHeader(createBusinessApplicationHeader());

      // 创建带有PaymentTypeInformation的GroupHeader
      GroupHeader groupHeader = createGroupHeader();
      groupHeader.setPmtTpInf(createPaymentTypeInformation(ValueType.CODE));
      requestInfo.setGroupHeader(groupHeader);

      requestInfo.setCreditTransferTransactionInformation(
          createCreditTransferTransactionInformation("DEBT"));
      return requestInfo;
    }

    static MXPacs008RequestInfo createMxPacs008RequestInfoWithSettlementInfo() {
      MXPacs008RequestInfo requestInfo = new MXPacs008RequestInfo();
      requestInfo.setBusinessApplicationHeader(createBusinessApplicationHeader());

      // 创建带有结算金额和日期的GroupHeader
      GroupHeader groupHeader = createGroupHeader();

      // 设置总银行间结算金额
      Amount amount = new Amount(CURRENCY, new BigDecimal("1000.00"));
      groupHeader.setTtlIntrBkSttlmAmt(amount);

      // 设置银行间结算日期
      groupHeader.setIntrBkSttlmDt(LocalDate.of(2024, 10, 10));

      // 设置控制总和
      groupHeader.setCtrlSum(new BigDecimal("1000.00"));

      requestInfo.setGroupHeader(groupHeader);
      requestInfo.setCreditTransferTransactionInformation(
          createCreditTransferTransactionInformation("DEBT"));
      return requestInfo;
    }

    static MXPacs008RequestInfo createMxPacs008RequestInfoWithAdditionalFields() {
      MXPacs008RequestInfo requestInfo = new MXPacs008RequestInfo();
      requestInfo.setBusinessApplicationHeader(createBusinessApplicationHeader());
      requestInfo.setGroupHeader(createExtendedGroupHeader());

      CreditTransferTransactionInformation39 txInfo =
          createCreditTransferTransactionInformation("DEBT");

      // 添加结算优先级
      txInfo.setSttlmPrty("HIGH");

      // 添加结算时间指示
      SettlementTimeIndication sttlmTmIndctn = new SettlementTimeIndication();
      sttlmTmIndctn.setDbtDtTm(LocalDateTime.of(2024, 10, 9, 10, 0, 0).atOffset(ZoneOffset.UTC));
      sttlmTmIndctn.setCdtDtTm(LocalDateTime.of(2024, 10, 9, 14, 0, 0).atOffset(ZoneOffset.UTC));
      txInfo.setSttlmTmIndctn(sttlmTmIndctn);

      // 添加结算时间请求
      SettlementTimeRequest sttlmTmReq = new SettlementTimeRequest();
      sttlmTmReq.setClrSysTm(OffsetTime.of(16, 0, 0, 0, ZoneOffset.UTC));
      sttlmTmReq.setTill(OffsetTime.of(17, 0, 0, 0, ZoneOffset.UTC));
      sttlmTmReq.setFrTm(OffsetTime.of(8, 0, 0, 0, ZoneOffset.UTC));
      sttlmTmReq.setRjctTm(OffsetTime.of(18, 0, 0, 0, ZoneOffset.UTC));
      txInfo.setSttlmTmReq(sttlmTmReq);

      // 添加接受日期时间
      txInfo.setAccptncDtTm(LocalDateTime.of(2024, 10, 9, 9, 0, 0));

      // 添加汇率
      txInfo.setXchgRate(new BigDecimal("1.1"));

      // 添加最终债务人
      txInfo.setUltmtDbtr(createParty("Ultimate Debtor", "**********", "US", true));

      // 添加发起方
      txInfo.setInitgPty(createParty("Initiating Party", "**********", "US", true));

      // 添加债务人代理账户
      txInfo.setDbtrAgtAcct(createAccount("**********"));

      // 添加债权人代理账户
      txInfo.setCdtrAgtAcct(createAccount("**********"));

      // 添加最终债权人
      txInfo.setUltmtCdtr(createParty("Ultimate Creditor", "**********", "GB", false));

      // 添加收款人代理指令
      InstructionForCreditorAgent instrForCdtrAgt = new InstructionForCreditorAgent();
      instrForCdtrAgt.setCd("PHOB");
      instrForCdtrAgt.setInstrInf("Please contact beneficiary by phone");
      txInfo.setInstrForCdtrAgt(Collections.singletonList(instrForCdtrAgt));

      // 添加下一代理指令
      InstructionForNextAgent instrForNxtAgt = new InstructionForNextAgent();
      instrForNxtAgt.setCd("PHOA");
      instrForNxtAgt.setInstrInf("Please contact next agent by phone");
      txInfo.setInstrForNxtAgt(Collections.singletonList(instrForNxtAgt));

      // 添加监管报告
      RegulatoryReporting rgltryRptg = new RegulatoryReporting();
      rgltryRptg.setDbtCdtRptgInd("CRED");
      rgltryRptg.setAuthorityName("Central Bank");
      rgltryRptg.setRptgTp("CRED");
      txInfo.setRgltryRptg(Collections.singletonList(rgltryRptg));

      // 添加税务信息
      TaxInformation tax = new TaxInformation();
      tax.setAdmstnZone("US-NY");
      tax.setRefNb("TAX-REF-12345");
      tax.setMtd("CRED");
      tax.setTotalAmount(new Amount("USD", new BigDecimal("100.00")));
      tax.setDate("2024-10-09");
      tax.setSequenceNumber("12345");

      // 添加税务记录
      TaxInformation.TaxRecord taxRecord = new TaxInformation.TaxRecord();
      taxRecord.setType("TYPE1");
      taxRecord.setCategory("CAT1");
      taxRecord.setCategoryDetails("Details for CAT1");
      taxRecord.setDebtorStatus("DEBT");
      taxRecord.setCertificateId("CERT-12345");
      taxRecord.setFrmsCd("FORM-12345");

      // 添加税务记录期间
      TaxInformation.TaxRecord.TaxPeriod period = new TaxInformation.TaxRecord.TaxPeriod();
      period.setYear("2024");
      period.setType("MM01");
      period.setFromDate("2024-01-01");
      period.setToDate("2024-01-31");
      taxRecord.setPeriod(period);

      // 添加税务记录金额
      TaxInformation.TaxRecord.TaxAmount taxAmount = new TaxInformation.TaxRecord.TaxAmount();
      taxAmount.setRate("10.00");
      taxAmount.setTaxableBaseAmount(new Amount("USD", new BigDecimal("1000.00")));
      taxAmount.setTotalAmount(new Amount("USD", new BigDecimal("100.00")));
      taxRecord.setAmount(taxAmount);

      taxRecord.setAdditionalInformation("Additional tax information");
      tax.setRecords(Collections.singletonList(taxRecord));
      txInfo.setTax(tax);

      // 添加相关汇款信息
      RelatedRemittanceInformation rltdRmtInf = new RelatedRemittanceInformation();
      rltdRmtInf.setRmtId("RMT-12345");
      rltdRmtInf.setRmtLctnMtd("EDIC");
      rltdRmtInf.setRmtLctnElctrncAdr("http://example.com/remittance");

      PostalAddress pstlAdr = new PostalAddress();
      pstlAdr.setAdrLine(Arrays.asList("123 Main St", "Suite 456"));
      pstlAdr.setCtry("US");
      pstlAdr.setTwnNm("New York");
      rltdRmtInf.setRmtLctnPstlAdr(pstlAdr);

      rltdRmtInf.setUstrd(Arrays.asList("Unstructured remittance info"));

      RelatedRemittanceInformation.StructuredRemittanceInformation strd =
          new RelatedRemittanceInformation.StructuredRemittanceInformation();
      strd.setReferredDocumentInformation(createReferredDocumentInformation());
      strd.setRemittanceAmount(createReferredDocumentAmount());
      strd.setCreditorReferenceInformation(createCreditorReferenceInformation());
      strd.setInvoicer(createParty("Invoicer", "**********", "US", false));
      strd.setInvoicee(createParty("Invoicee", "**********", "US", false));
      strd.setAdditionalRemittanceInformation(Arrays.asList("Additional remittance info"));

      rltdRmtInf.setStrd(Arrays.asList(strd));
      txInfo.setRltdRmtInf(Arrays.asList(rltdRmtInf));

      // 添加补充数据
      SupplementaryData splmtryData = new SupplementaryData();
      splmtryData.setPlcAndNm("PlaceAndName");
      splmtryData.setEnvelope("<xml>Supplementary data content</xml>");
      txInfo.setSplmtryData(Arrays.asList(splmtryData));

      requestInfo.setCreditTransferTransactionInformation(txInfo);
      return requestInfo;
    }
  }
}
