FROM registry.cn-shenzhen.aliyuncs.com/bihupiaodian/bihu-java-17:v1
ENV JAR_PATH /usr/local/swifttrack/swift-track.jar
ENV ADD_OPENS --add-opens java.base/java.lang=ALL-UNNAMED
ENV CUSTOM_JAVA_OPTS -XX:+UseContainerSupport -XX:-UseAdaptiveSizePolicy -XX:MaxDirectMemorySize=512M -Xss512K -XX:MetaspaceSize=256m  -XX:MaxMetaspaceSize=512m -XX:MaxRAMPercentage=75.0 -XX:+UseG1GC -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/data/logs/jvm/oom/swift_track.hprof -XX:ErrorFile=/data/logs/jvm/swift-track_hs_err_pid.log
ENV CONFIG_NAMESPACE default
ENV profile prod
ADD ./target/swift-track.jar /usr/local/swifttrack/

ENTRYPOINT ["sh","-c","java $ADD_OPENS $CUSTOM_JAVA_OPTS -jar $JAR_PATH --spring.profiles.active=$profile --spring.cloud.kubernetes.config.namespace=$CONFIG_NAMESPACE"]

