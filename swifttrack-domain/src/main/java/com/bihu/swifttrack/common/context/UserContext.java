package com.bihu.swifttrack.common.context;

import com.bihu.swifttrack.dto.LoginInfoDTO;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/6/3
 */
@Slf4j
public class UserContext {

  /**
   * 存储当前线程用户信息
   */
  private static final ThreadLocal<LoginInfoDTO> USER_THREAD_LOCAL = new ThreadLocal<>();

  /**
   * 功能：获取用户信息
   */
  public static LoginInfoDTO getLoginUser() {
    return USER_THREAD_LOCAL.get();
  }

  /**
   * 设置用户信息
   */
  public static void setLoginUser(LoginInfoDTO loginUser) {
    USER_THREAD_LOCAL.set(loginUser);
  }

  /**
   * 获取用户名称
   *
   * @return 用户名称
   */
  public static String getUsername() {
    return USER_THREAD_LOCAL.get().getUsername();
  }

  /**
   * 获取用户名称
   *
   * @return 用户名称
   */
  public static Long getUserId() {
    return ((LoginInfoDTO) USER_THREAD_LOCAL.get()).getUserId();
  }

  /**
   * 清除数据
   */
  public static void clear() {
    USER_THREAD_LOCAL.remove();
  }


}
