package com.bihu.swifttrack.common.exception;

import com.bihu.swifttrack.common.response.IServerCodeEnum;
import java.io.Serial;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName BusinessException
 * @Description
 * <AUTHOR>
 * @Created 2023-04-27 10:52 上午
 */
@Getter
@Setter
public class BusinessException extends RuntimeException {

  @Serial
  private static final long serialVersionUID = 8573646261817653711L;

  /***
   * 服务状态码
   */
  private IServerCodeEnum serverCode;

  /**
   * 异常信息描述
   */
  private String desc;

  public BusinessException(IServerCodeEnum serverCode) {
    super();
    this.serverCode = null != serverCode ? serverCode : ServerCode.BUSINESS_ERROR;
    this.desc = this.serverCode.getDesc();
  }

  public BusinessException(IServerCodeEnum serverCode, String desc) {
    super(desc);
    this.serverCode = null != serverCode ? serverCode : ServerCode.BUSINESS_ERROR;
    this.desc = (null == desc || desc.isEmpty()) ? this.serverCode.getDesc() : desc;
  }

  public BusinessException(String desc) {
    super(desc);
    this.serverCode = ServerCode.BUSINESS_ERROR;
    this.desc = (null == desc || desc.isEmpty()) ? this.serverCode.getDesc() : desc;
  }
}
